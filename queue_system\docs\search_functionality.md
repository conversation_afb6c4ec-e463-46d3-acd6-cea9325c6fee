# 搜索功能文档

## 概述

已在所有主要的tab页面中添加了搜索功能，用户可以快速查找特定的单据、客户或库存项目。

## 功能特点

### 🔍 搜索范围
- **单据编号** (document_no)
- **客户编码** (customer)
- **客户名称** (customer_name)
- **库存编码** (stock)
- **描述** (description)
- **货架位置** (bin_shelf_no)
- **运输商名称** (transporter_name)
- **备注** (remarks)

### 🎯 支持的页面
1. **待检查页面** (to_check_tab)
2. **等待检查页面** (waiting_tab)
3. **待确认页面** (to_confirm_tab)
4. **BOM审核页面** (bom_review_tab)
5. **待发货页面** (to_ship_tab)
6. **送货状态页面** (delivery_tab)

### ✨ 搜索特性
- **实时搜索**：输入时即时过滤结果
- **不区分大小写**：支持大小写混合搜索
- **模糊匹配**：支持部分关键词匹配
- **搜索统计**：显示搜索结果数量
- **清除功能**：一键清除搜索条件

## 使用方法

### 基本搜索
1. 在页面顶部找到搜索框
2. 输入关键词（单据号、客户名、库存编码等）
3. 系统会自动过滤并显示匹配的结果
4. 搜索结果统计会显示在搜索框下方

### 搜索示例
```
搜索单据编号：D00001
搜索客户：Customer One
搜索库存：ABC123
搜索货架：A1-01
```

### 清除搜索
- 点击搜索框右侧的 ❌ 按钮
- 或者删除搜索框中的所有文字

## 技术实现

### 核心组件

#### SearchBarWidget
- 位置：`lib/widgets/search_bar_widget.dart`
- 功能：提供统一的搜索界面
- 特性：
  - 自定义提示文本
  - 实时搜索回调
  - 清除按钮
  - 初始值设置

#### SearchUtils
- 位置：`lib/utils/search_utils.dart`
- 功能：提供搜索逻辑
- 方法：
  - `searchCheckItems()` - 搜索检查项目
  - `searchShippingDocuments()` - 搜索发货文档
  - `searchGeneric()` - 通用搜索方法

### 集成方式

每个tab页面都按照以下模式集成搜索功能：

1. **添加搜索状态**
```dart
String _searchQuery = '';
List<ItemType> _filteredItems = [];
```

2. **实现搜索方法**
```dart
void _onSearchChanged(String query) {
  setState(() {
    _searchQuery = query;
    _updateFilteredItems();
  });
}

void _updateFilteredItems() {
  setState(() {
    _filteredItems = SearchUtils.searchCheckItems(widget.items, _searchQuery);
  });
}
```

3. **更新UI布局**
```dart
Column(
  children: [
    SearchBarWidget(
      hintText: context.t('search_documents'),
      onSearchChanged: _onSearchChanged,
      onClear: _onSearchCleared,
      initialValue: _searchQuery,
    ),
    // 搜索结果统计
    if (_searchQuery.isNotEmpty) ...,
    // 项目列表
    Expanded(child: _buildItemsList()),
  ],
)
```

## 本地化支持

搜索功能支持多语言：

### 中文 (zh)
- `search_documents`: '搜索单据、客户、库存...'
- `search_results`: '搜索结果'
- `no_search_results`: '未找到匹配的结果'
- `clear_search`: '清除搜索'
- `search_keyword`: '搜索关键词'

### 英文 (en)
- `search_documents`: 'Search documents, customers, stock...'
- `search_results`: 'Search Results'
- `no_search_results`: 'No matching results found'
- `clear_search`: 'Clear Search'
- `search_keyword`: 'Search Keyword'

### 马来文 (ms)
- `search_documents`: 'Cari dokumen, pelanggan, stok...'
- `search_results`: 'Hasil Carian'
- `no_search_results`: 'Tiada hasil yang sepadan ditemui'
- `clear_search`: 'Kosongkan Carian'
- `search_keyword`: 'Kata Kunci Carian'

## 测试

搜索功能包含完整的单元测试：
- 位置：`test/search_functionality_test.dart`
- 覆盖：所有搜索场景和边界情况
- 运行：`flutter test test/search_functionality_test.dart`

## 性能优化

- **实时过滤**：使用高效的字符串匹配算法
- **状态管理**：合理的setState调用，避免不必要的重建
- **内存管理**：及时清理搜索状态
- **用户体验**：平滑的搜索动画和反馈

## 未来扩展

可以考虑的功能增强：
- 搜索历史记录
- 高级搜索过滤器
- 搜索结果高亮显示
- 语音搜索支持
- 搜索建议/自动完成
