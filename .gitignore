# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment variables
.env
.env.*
!.env.example
!.env.firebase.example
queuesystem-1fd1f-firebase-adminsdk-fbsvc-e99fcac3c6.json

# PM2 Configuration (含敏感信息的配置文件)
ecosystem.production.config.js
ecosystem.staging.config.js
ecosystem.*.local.config.js

# Application Output
PDF_Output/
logs/

# TypeScript
*.tsbuildinfo