import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Main } from '../postgres/entities/main.entity';
import * as admin from 'firebase-admin';
import * as fs from 'fs';
import * as path from 'path';

export interface FirebaseUploadResult {
  success: boolean;
  downloadUrl?: string;
  firebasePath?: string;
  error?: string;
  skipped?: boolean;
  skipReason?: string;
}

@Injectable()
export class FirebaseService implements OnModuleInit {
  private readonly logger = new Logger(FirebaseService.name);
  private storage: admin.storage.Storage;
  private bucket: any;
  private isInitialized = false;

  constructor(
    private configService: ConfigService,
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
  ) { }

  async onModuleInit() {
    await this.initializeFirebase();
  }

  /**
   * 初始化Firebase Admin SDK
   */
  private async initializeFirebase(): Promise<void> {
    try {
      const firebaseConfig = this.configService.get('firebase');

      if (!firebaseConfig?.uploadConfig?.enabled) {
        this.logger.warn('Firebase上传功能已禁用');
        return;
      }

      // 检查必要的配置
      if (!firebaseConfig.projectId || !firebaseConfig.storageBucket) {
        this.logger.error('Firebase配置不完整：缺少projectId或storageBucket');
        return;
      }

      let credential: admin.credential.Credential;

      // 尝试使用服务账户JSON文件
      if (firebaseConfig.serviceAccountPath && fs.existsSync(firebaseConfig.serviceAccountPath)) {
        this.logger.log(`使用服务账户文件: ${firebaseConfig.serviceAccountPath}`);
        credential = admin.credential.cert(firebaseConfig.serviceAccountPath);
      }
      // 尝试使用环境变量中的密钥信息
      else if (firebaseConfig.privateKey && firebaseConfig.clientEmail) {
        this.logger.log('使用环境变量中的服务账户信息');
        credential = admin.credential.cert({
          projectId: firebaseConfig.projectId,
          privateKey: firebaseConfig.privateKey,
          clientEmail: firebaseConfig.clientEmail,
        });
      }
      else {
        this.logger.error('Firebase服务账户配置不完整');
        return;
      }

      // 初始化Firebase Admin
      if (!admin.apps.length) {
        admin.initializeApp({
          credential,
          projectId: firebaseConfig.projectId,
          storageBucket: firebaseConfig.storageBucket,
        });
        this.logger.log('Firebase Admin SDK初始化成功');
      }

      this.storage = admin.storage();
      this.bucket = this.storage.bucket();
      this.isInitialized = true;

      this.logger.log(`Firebase Storage已连接到: ${firebaseConfig.storageBucket}`);
    } catch (error) {
      this.logger.error(`Firebase初始化失败: ${error.message}`);
      this.isInitialized = false;
    }
  }

  /**
   * 上传PDF文件到Firebase Storage
   * @param localFilePath 本地文件路径
   * @param documentNo 订单编号
   * @param customPath 自定义路径（可选）
   * @returns 上传结果
   */
  async uploadPdf(
    localFilePath: string,
    documentNo: string,
    customPath?: string
  ): Promise<FirebaseUploadResult> {
    const startTime = Date.now();
    this.logger.log(`[Firebase Upload] 开始处理上传请求: ${documentNo}`);

    if (!this.isInitialized) {
      const firebaseConfig = this.configService.get('firebase');
      if (!firebaseConfig?.uploadConfig?.enabled) {
        this.logger.log(`[Firebase Upload] 上传功能已禁用: ${documentNo}`);
        return {
          success: true,
          skipped: true,
          skipReason: 'Firebase上传功能已禁用',
        };
      } else {
        this.logger.error(`[Firebase Upload] 服务未初始化: ${documentNo}`);
        return {
          success: false,
          error: 'Firebase服务未初始化',
        };
      }
    }

    try {
      // 检查本地文件是否存在
      if (!fs.existsSync(localFilePath)) {
        this.logger.error(`[Firebase Upload] 本地文件不存在: ${documentNo} -> ${localFilePath}`);
        return {
          success: false,
          error: `本地文件不存在: ${localFilePath}`,
        };
      }

      // 获取文件大小
      const fileStats = fs.statSync(localFilePath);
      const fileSizeMB = (fileStats.size / 1024 / 1024).toFixed(2);
      this.logger.log(`[Firebase Upload] 文件信息: ${documentNo} -> 大小: ${fileSizeMB}MB, 路径: ${localFilePath}`);

      // 生成Firebase Storage路径
      const firebasePath = this.generateFirebasePath(localFilePath, documentNo, customPath);

      this.logger.log(`[Firebase Upload] 开始上传: ${documentNo} -> Firebase路径: ${firebasePath}`);

      // 执行上传
      const result = await this.uploadFileWithRetry(localFilePath, firebasePath);

      const duration = Date.now() - startTime;

      if (result.success) {
        this.logger.log(`[Firebase Upload] 上传成功: ${documentNo} -> 耗时: ${duration}ms, URL: ${result.downloadUrl}`);

        // 保存Firebase下载链接到数据库
        await this.saveFirebaseUrlToDatabase(documentNo, result);
      } else {
        this.logger.error(`[Firebase Upload] 上传失败: ${documentNo} -> 耗时: ${duration}ms, 错误: ${result.error}`);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`[Firebase Upload] 上传异常: ${documentNo} -> 耗时: ${duration}ms, 异常: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 生成Firebase Storage中的文件路径
   */
  private generateFirebasePath(localFilePath: string, documentNo: string, customPath?: string): string {
    const firebaseConfig = this.configService.get('firebase');
    const rootPath = firebaseConfig.uploadConfig.pdfRootPath;

    if (customPath) {
      return `${rootPath}/${customPath}`;
    }

    // 从本地路径中提取日期信息
    const pathParts = localFilePath.split(path.sep);
    const dateFolder = pathParts.find(part => /^\d{4}-\d{2}-\d{2}$/.test(part));

    if (dateFolder) {
      return `${rootPath}/${dateFolder}/${documentNo}.pdf`;
    } else {
      // 如果无法从路径提取日期，使用当前日期
      const today = new Date().toISOString().split('T')[0];
      return `${rootPath}/${today}/${documentNo}.pdf`;
    }
  }

  /**
   * 带重试机制的文件上传
   */
  private async uploadFileWithRetry(
    localFilePath: string,
    firebasePath: string
  ): Promise<FirebaseUploadResult> {
    const firebaseConfig = this.configService.get('firebase');
    const maxRetries = firebaseConfig.uploadConfig.retryAttempts;
    const timeout = firebaseConfig.uploadConfig.uploadTimeout;

    this.logger.log(`[Firebase Retry] 开始重试上传: ${firebasePath}, 最大重试次数: ${maxRetries}, 超时: ${timeout}ms`);

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const attemptStartTime = Date.now();
      try {
        this.logger.log(`[Firebase Retry] 尝试 ${attempt}/${maxRetries}: ${firebasePath}`);

        const file = this.bucket.file(firebasePath);

        // 上传文件
        await file.save(fs.readFileSync(localFilePath), {
          metadata: {
            contentType: 'application/pdf',
            metadata: {
              uploadedAt: new Date().toISOString(),
              originalPath: localFilePath,
              attempt: attempt.toString(),
            },
          },
          timeout,
        });

        const attemptDuration = Date.now() - attemptStartTime;
        this.logger.log(`[Firebase Retry] 上传成功，尝试 ${attempt}, 耗时: ${attemptDuration}ms`);

        // 设置文件为公共可读（生成更短的URL）
        await file.makePublic();

        // 获取公共下载URL（更短）
        const publicUrl = `https://storage.googleapis.com/${this.bucket.name}/${firebasePath}`;

        this.logger.log(`[Firebase Retry] 获取公共URL成功: ${publicUrl}`);

        return {
          success: true,
          downloadUrl: publicUrl,
          firebasePath,
        };
      } catch (error) {
        const attemptDuration = Date.now() - attemptStartTime;
        this.logger.warn(`[Firebase Retry] 尝试 ${attempt} 失败，耗时: ${attemptDuration}ms, 错误: ${error.message}`);

        if (attempt === maxRetries) {
          this.logger.error(`[Firebase Retry] 所有重试均失败: ${firebasePath}`);
          return {
            success: false,
            error: `上传失败，已重试${maxRetries}次: ${error.message}`,
          };
        }

        // 等待后重试
        const retryDelay = 1000 * attempt;
        this.logger.log(`[Firebase Retry] 等待 ${retryDelay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    return {
      success: false,
      error: '未知错误',
    };
  }

  /**
   * 检查Firebase服务状态
   */
  getStatus(): { initialized: boolean; enabled: boolean } {
    const firebaseConfig = this.configService.get('firebase');
    return {
      initialized: this.isInitialized,
      enabled: firebaseConfig?.uploadConfig?.enabled || false,
    };
  }

  /**
   * 保存Firebase下载链接到数据库
   * @param documentNo 订单编号
   * @param uploadResult Firebase上传结果
   */
  private async saveFirebaseUrlToDatabase(documentNo: string, uploadResult: FirebaseUploadResult): Promise<void> {
    try {
      this.logger.log(`[Firebase DB] 开始保存下载链接到数据库: ${documentNo}`);

      const updateResult = await this.mainRepository.update(
        { document_no: documentNo },
        {
          firebase_download_url: uploadResult.downloadUrl,
          firebase_file_path: uploadResult.firebasePath,
          firebase_uploaded_at: new Date(),
        }
      );

      if (updateResult.affected > 0) {
        this.logger.log(`[Firebase DB] 下载链接保存成功: ${documentNo} -> ${uploadResult.downloadUrl}`);
      } else {
        this.logger.warn(`[Firebase DB] 未找到订单记录: ${documentNo}`);
      }
    } catch (error) {
      this.logger.error(`[Firebase DB] 保存下载链接失败: ${documentNo} -> ${error.message}`);
      // 不抛出异常，因为这不应该影响主流程
    }
  }

  /**
   * 删除Firebase Storage中的文件
   * @param firebasePath Firebase中的文件路径
   */
  async deleteFile(firebasePath: string): Promise<boolean> {
    if (!this.isInitialized) {
      this.logger.warn('Firebase未初始化，无法删除文件');
      return false;
    }

    try {
      const file = this.bucket.file(firebasePath);
      await file.delete();
      this.logger.log(`文件删除成功: ${firebasePath}`);
      return true;
    } catch (error) {
      this.logger.error(`文件删除失败: ${firebasePath} -> ${error.message}`);
      return false;
    }
  }

  /**
   * 清理超过指定天数的PDF文件
   * @param daysOld 文件超过多少天被认为是过期的（默认90天，即3个月）
   * @param dryRun 是否为试运行（只检查不删除）
   * @returns 清理结果
   */
  async cleanupOldFiles(daysOld: number = 90, dryRun: boolean = false): Promise<{
    success: boolean;
    totalFiles: number;
    expiredFiles: number;
    deletedFiles: number;
    failedDeletes: number;
    savedSpace: number; // 字节
    details: Array<{
      path: string;
      size: number;
      created: Date;
      deleted: boolean;
      error?: string;
    }>;
    error?: string;
  }> {
    if (!this.isInitialized) {
      return {
        success: false,
        totalFiles: 0,
        expiredFiles: 0,
        deletedFiles: 0,
        failedDeletes: 0,
        savedSpace: 0,
        details: [],
        error: 'Firebase未初始化',
      };
    }

    try {
      this.logger.log(`[Firebase Cleanup] 开始清理超过${daysOld}天的文件，试运行: ${dryRun}`);

      const firebaseConfig = this.configService.get('firebase');
      const pdfRootPath = firebaseConfig.uploadConfig.pdfRootPath;

      // 计算过期时间
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      this.logger.log(`[Firebase Cleanup] 删除${cutoffDate.toISOString()}之前的文件`);

      // 获取所有PDF文件
      const [files] = await this.bucket.getFiles({
        prefix: pdfRootPath + '/',
      });

      const result = {
        success: true,
        totalFiles: files.length,
        expiredFiles: 0,
        deletedFiles: 0,
        failedDeletes: 0,
        savedSpace: 0,
        details: [] as Array<{
          path: string;
          size: number;
          created: Date;
          deleted: boolean;
          error?: string;
        }>,
      };

      this.logger.log(`[Firebase Cleanup] 找到${files.length}个PDF文件`);

      for (const file of files) {
        try {
          // 获取文件元数据
          const [metadata] = await file.getMetadata();
          const createdDate = new Date(metadata.timeCreated);
          const fileSize = parseInt(metadata.size || '0', 10);

          // 检查文件是否过期
          const isExpired = createdDate < cutoffDate;

          if (isExpired) {
            result.expiredFiles++;

            let deleted = false;
            let error: string | undefined;

            if (!dryRun) {
              try {
                await file.delete();
                deleted = true;
                result.deletedFiles++;
                result.savedSpace += fileSize;
                this.logger.log(`[Firebase Cleanup] 删除过期文件: ${file.name}`);
              } catch (deleteError) {
                error = deleteError.message;
                result.failedDeletes++;
                this.logger.error(`[Firebase Cleanup] 删除文件失败: ${file.name} -> ${deleteError.message}`);
              }
            } else {
              this.logger.log(`[Firebase Cleanup] 试运行 - 将删除: ${file.name}`);
            }

            result.details.push({
              path: file.name,
              size: fileSize,
              created: createdDate,
              deleted,
              error,
            });
          }
        } catch (metadataError) {
          this.logger.error(`[Firebase Cleanup] 获取文件元数据失败: ${file.name} -> ${metadataError.message}`);
        }
      }

      const savedSpaceMB = (result.savedSpace / 1024 / 1024).toFixed(2);
      this.logger.log(`[Firebase Cleanup] 清理完成 - 总文件: ${result.totalFiles}, 过期: ${result.expiredFiles}, 删除: ${result.deletedFiles}, 失败: ${result.failedDeletes}, 节省空间: ${savedSpaceMB}MB`);

      return result;
    } catch (error) {
      this.logger.error(`[Firebase Cleanup] 清理过程中发生异常: ${error.message}`);
      return {
        success: false,
        totalFiles: 0,
        expiredFiles: 0,
        deletedFiles: 0,
        failedDeletes: 0,
        savedSpace: 0,
        details: [],
        error: error.message,
      };
    }
  }

  /**
   * 获取Firebase Storage使用统计
   */
  async getStorageStats(): Promise<{
    success: boolean;
    totalFiles: number;
    totalSize: number; // 字节
    oldestFile?: { path: string; created: Date };
    newestFile?: { path: string; created: Date };
    filesByMonth: Record<string, { count: number; size: number }>;
    error?: string;
  }> {
    if (!this.isInitialized) {
      return {
        success: false,
        totalFiles: 0,
        totalSize: 0,
        filesByMonth: {},
        error: 'Firebase未初始化',
      };
    }

    try {
      const firebaseConfig = this.configService.get('firebase');
      const pdfRootPath = firebaseConfig.uploadConfig.pdfRootPath;

      const [files] = await this.bucket.getFiles({
        prefix: pdfRootPath + '/',
      });

      const stats = {
        success: true,
        totalFiles: files.length,
        totalSize: 0,
        oldestFile: undefined as { path: string; created: Date } | undefined,
        newestFile: undefined as { path: string; created: Date } | undefined,
        filesByMonth: {} as Record<string, { count: number; size: number }>,
      };

      for (const file of files) {
        try {
          const [metadata] = await file.getMetadata();
          const createdDate = new Date(metadata.timeCreated);
          const fileSize = parseInt(metadata.size || '0', 10);

          stats.totalSize += fileSize;

          // 更新最老和最新文件
          if (!stats.oldestFile || createdDate < stats.oldestFile.created) {
            stats.oldestFile = { path: file.name, created: createdDate };
          }
          if (!stats.newestFile || createdDate > stats.newestFile.created) {
            stats.newestFile = { path: file.name, created: createdDate };
          }

          // 按月统计
          const monthKey = createdDate.toISOString().substring(0, 7); // YYYY-MM
          if (!stats.filesByMonth[monthKey]) {
            stats.filesByMonth[monthKey] = { count: 0, size: 0 };
          }
          stats.filesByMonth[monthKey].count++;
          stats.filesByMonth[monthKey].size += fileSize;
        } catch (metadataError) {
          this.logger.warn(`获取文件元数据失败: ${file.name}`);
        }
      }

      return stats;
    } catch (error) {
      return {
        success: false,
        totalFiles: 0,
        totalSize: 0,
        filesByMonth: {},
        error: error.message,
      };
    }
  }
}
