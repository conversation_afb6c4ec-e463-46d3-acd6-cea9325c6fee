import 'package:dio/dio.dart';
import 'package:queue_system/models/document.dart';
import 'dart:convert';

// 错误代码常量
class ApiErrorCode {
  static const String connectionFailed = 'error_connection';
  static const String syncFailed = 'error_sync';
  static const String syncSuccess = 'success_sync';
}

class ApiService {
  final Dio _dio;
  final String _baseUrl;

  ApiService({required String baseUrl})
      : _dio = Dio(),
        _baseUrl = baseUrl {
    _dio.options.baseUrl = _baseUrl;
    // 增加超时时间
    _dio.options.connectTimeout = const Duration(seconds: 15);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  // 获取PostgreSQL中的详细数据（包含明细）
  Future<List<Document>> getDocumentsWithDetails() async {
    try {
      final response = await _dio.get('/postgres/details');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;

        // 解析文档
        final List<Document> documents = [];
        for (var i = 0; i < data.length; i++) {
          try {
            final json = data[i];
            final document = Document.fromJson(json);
            documents.add(document);
          } catch (e) {
            // 处理单个记录解析错误
          }
        }
        return documents;
      } else {
        throw ApiErrorCode.connectionFailed;
      }
    } catch (e) {
      // 对用户只显示简单的错误消息
      throw ApiErrorCode.connectionFailed;
    }
  }

  // 获取PostgreSQL中的基本记录
  Future<List<Document>> getDocuments() async {
    try {
      final response = await _dio.get('/postgres');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        final List<Document> documents = [];

        for (var i = 0; i < data.length; i++) {
          try {
            final json = data[i];
            final document = Document.fromJson(json);
            documents.add(document);
          } catch (e) {
            // 处理单个记录解析错误
          }
        }
        return documents;
      } else {
        throw ApiErrorCode.connectionFailed;
      }
    } catch (e) {
      // 对用户只显示简单的错误消息
      throw ApiErrorCode.connectionFailed;
    }
  }

  // 手动触发同步
  Future<void> triggerSync() async {
    try {
      final response = await _dio.post('/sync');

      if (response.statusCode != 200) {
        throw ApiErrorCode.syncFailed;
      }
    } catch (e) {
      // 对用户只显示简单的错误消息
      throw ApiErrorCode.syncFailed;
    }
  }

  // 通用GET请求方法
  Future<dynamic> get(String path,
      {Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
      );

      // 接受200、201和204状态码作为成功响应
      if (response.statusCode == 200 ||
          response.statusCode == 201 ||
          response.statusCode == 204) {
        return response.data;
      } else {
        throw Exception('请求失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('请求失败: $e');
    }
  }

  // 通用POST请求方法
  Future<dynamic> post(
    String path, {
    Map<String, dynamic>? queryParameters,
    dynamic data,
  }) async {
    try {
      final response = await _dio.post(
        path,
        queryParameters: queryParameters,
        data: data,
      );

      // 接受200和201状态码作为成功响应
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('请求失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('请求失败: $e');
    }
  }
}
