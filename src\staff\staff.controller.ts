import { Controller, Get, Post, Put, Body, Param, BadRequestException, NotFoundException, UnauthorizedException, UseGuards, HttpCode } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Staff } from '../postgres/entities/staff.entity';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import * as bcrypt from 'bcrypt';

@ApiTags('Staff')
@Controller('staff')
export class StaffController {
  constructor(
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
  ) { }

  @ApiOperation({ summary: '获取所有员工' })
  @ApiResponse({
    status: 200,
    description: '返回所有员工列表，不包含密码'
  })
  @Get()
  async findAll() {
    const staff = await this.staffRepository.find();
    return staff.map(s => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = s;
      return result;
    });
  }

  @ApiOperation({ summary: '根据ID获取员工' })
  @ApiResponse({
    status: 200,
    description: '返回指定ID的员工，不包含密码'
  })
  @Get(':id')
  async findOne(@Param('id') id: number) {
    const staff = await this.staffRepository.findOne({ where: { id } });
    if (!staff) {
      throw new NotFoundException(`Staff with ID ${id} not found`);
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = staff;
    return result;
  }

  @ApiOperation({ summary: '创建新员工' })
  @ApiBody({
    description: '员工信息',
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string', example: 'john.doe' },
        password: { type: 'string', example: 'password123' },
        level: { type: 'string', enum: ['regular', 'senior', 'driver', 'staff_bom'], example: 'regular' },
        floor: {
          type: 'string',
          description: '普通员工必须选择1F、2F、3F之一；主管、司机和BOM工作人员可以使用"ALL"表示所有楼层',
          example: '3F'
        },
        full_name: { type: 'string', example: 'John Doe' }
      },
      required: ['username', 'password', 'level']
    }
  })
  @ApiResponse({
    status: 201,
    description: '成功创建员工，返回创建的员工信息（不含密码）'
  })
  @Post()
  async create(@Body() staffData: Partial<Staff>) {
    // 验证必填字段
    if (!staffData.username || !staffData.password || !staffData.level) {
      throw new BadRequestException('Username, password and level are required');
    }

    // 如果是普通员工，则楼层是必填的
    if (staffData.level === 'regular' && !staffData.floor) {
      throw new BadRequestException('Floor is required for regular staff');
    }

    // 如果是主管、管理员、司机或BOM工作人员级别，floor设置为"ALL"
    if ((staffData.level === 'supervisor' || staffData.level === 'admin' || staffData.level === 'driver' || staffData.level === 'staff_bom') && !staffData.floor) {
      staffData.floor = 'ALL';
    }

    // 验证floor字段值 - 普通员工只允许1F、2F、3F，主管、司机或BOM工作人员可以是ALL
    if (staffData.floor) {
      if (staffData.level === 'regular' && !['1F', '2F', '3F'].includes(staffData.floor)) {
        throw new BadRequestException('Floor must be one of: 1F, 2F, 3F for regular staff');
      } else if ((staffData.level === 'supervisor' || staffData.level === 'admin' || staffData.level === 'driver' || staffData.level === 'staff_bom') &&
        staffData.floor !== 'ALL' && !['1F', '2F', '3F'].includes(staffData.floor)) {
        throw new BadRequestException('Floor must be "ALL" or one of: 1F, 2F, 3F for supervisors, admin, drivers, or staff_bom');
      }
    }

    // 检查用户名是否已存在
    const existingStaff = await this.staffRepository.findOne({
      where: { username: staffData.username }
    });
    if (existingStaff) {
      throw new BadRequestException('Username already exists');
    }

    // 验证员工级别
    if (!['regular', 'supervisor', 'admin', 'driver', 'staff_bom'].includes(staffData.level)) {
      throw new BadRequestException('Level must be one of: "regular", "supervisor", "admin", "driver", or "staff_bom"');
    }

    // 密码加密
    const hashedPassword = await bcrypt.hash(staffData.password, 10);

    // 创建新员工
    const newStaff = this.staffRepository.create({
      ...staffData,
      password: hashedPassword,
      status: 'active'
    });

    const savedStaff = await this.staffRepository.save(newStaff);

    // 返回结果时移除密码
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = savedStaff;
    return result;
  }

  @ApiOperation({ summary: '员工登录' })
  @ApiBody({
    description: '登录信息',
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string', example: 'john.doe' },
        password: { type: 'string', example: 'password123' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '登录成功，返回员工信息（不含密码）'
  })
  @HttpCode(200)
  @Post('login')
  async login(@Body() loginData: { username: string; password: string }) {
    // 验证必填字段
    if (!loginData.username || !loginData.password) {
      throw new BadRequestException('Username and password are required');
    }

    // 查找用户
    const staff = await this.staffRepository.findOne({
      where: { username: loginData.username }
    });
    if (!staff) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(loginData.password, staff.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // 检查账户状态
    if (staff.status !== 'active') {
      throw new UnauthorizedException('Account is inactive');
    }

    // 更新最后登录时间
    staff.last_login = new Date();
    await this.staffRepository.save(staff);

    // 返回结果时移除密码
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = staff;
    return result;
  }

  @ApiOperation({ summary: '修改员工密码' })
  @ApiBody({
    description: '新密码',
    schema: {
      type: 'object',
      properties: {
        password: { type: 'string', example: 'newpassword123' }
      },
      required: ['password']
    }
  })
  @ApiResponse({
    status: 200,
    description: '密码修改成功'
  })
  @Put(':id/password')
  async changePassword(@Param('id') id: string, @Body() passwordData: { password: string }) {
    if (!passwordData.password) {
      throw new BadRequestException('Password is required');
    }

    if (passwordData.password.length < 6) {
      throw new BadRequestException('Password must be at least 6 characters long');
    }

    const staff = await this.staffRepository.findOne({
      where: { id: parseInt(id) }
    });

    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(passwordData.password, 10);

    // 更新密码
    await this.staffRepository.update(parseInt(id), {
      password: hashedPassword
    });

    return { message: 'Password updated successfully' };
  }

  @ApiOperation({ summary: '更新员工状态' })
  @ApiBody({
    description: '员工状态',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['active', 'inactive'], example: 'active' }
      },
      required: ['status']
    }
  })
  @ApiResponse({
    status: 200,
    description: '状态更新成功'
  })
  @Put(':id/status')
  async updateStatus(@Param('id') id: string, @Body() statusData: { status: string }) {
    if (!statusData.status) {
      throw new BadRequestException('Status is required');
    }

    if (!['active', 'inactive'].includes(statusData.status)) {
      throw new BadRequestException('Status must be either active or inactive');
    }

    const staff = await this.staffRepository.findOne({
      where: { id: parseInt(id) }
    });

    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    // 更新状态
    await this.staffRepository.update(parseInt(id), {
      status: statusData.status
    });

    return { message: 'Status updated successfully' };
  }
}