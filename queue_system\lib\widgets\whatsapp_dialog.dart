import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';

class WhatsAppDialog extends StatefulWidget {
  final String documentNo;
  final String? defaultPhoneNumber;
  final String pdfDownloadUrl;

  const WhatsAppDialog({
    super.key,
    required this.documentNo,
    this.defaultPhoneNumber,
    required this.pdfDownloadUrl,
  });

  @override
  State<WhatsAppDialog> createState() => _WhatsAppDialogState();
}

class _WhatsAppDialogState extends State<WhatsAppDialog> {
  late TextEditingController _phoneController;
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // 预填充手机号码，如果有的话
    String initialPhone = widget.defaultPhoneNumber ?? '';
    // 如果手机号码不是以+开头，尝试添加马来西亚国家代码
    if (initialPhone.isNotEmpty && !initialPhone.startsWith('+')) {
      // 移除可能存在的前导0
      if (initialPhone.startsWith('0')) {
        initialPhone = initialPhone.substring(1);
      }
      // 添加马来西亚国家代码
      initialPhone = '+60$initialPhone';
    }
    _phoneController = TextEditingController(text: initialPhone);
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  // 格式化手机号码
  String _formatPhoneNumber(String phone) {
    // 移除所有空格和特殊字符，只保留数字和+号
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // 如果不是以+开头，添加马来西亚国家代码
    if (!cleaned.startsWith('+')) {
      // 移除前导0
      if (cleaned.startsWith('0')) {
        cleaned = cleaned.substring(1);
      }
      cleaned = '+60$cleaned';
    }

    return cleaned;
  }

  // 验证手机号码格式
  bool _isValidPhoneNumber(String phone) {
    // 基本的手机号码格式验证
    final phoneRegex = RegExp(r'^\+\d{10,15}$');
    return phoneRegex.hasMatch(phone);
  }

  // 生成WhatsApp消息内容
  String _generateWhatsAppMessage(BuildContext context) {
    return context
        .t('whatsapp_message_template')
        .replaceAll('{documentNo}', widget.documentNo)
        .replaceAll('{pdfUrl}', widget.pdfDownloadUrl);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.message,
            color: Colors.green[600],
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(context.t('send_to_whatsapp')),
        ],
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${context.t('document_no')}: ${widget.documentNo}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: context.t('phone_number'),
                hintText: '+60123456789',
                prefixIcon: Icon(Icons.phone),
                border: OutlineInputBorder(),
                helperText: context.t('please_enter_phone_number'),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return context.t('please_enter_phone_number');
                }

                final formatted = _formatPhoneNumber(value);
                if (!_isValidPhoneNumber(formatted)) {
                  return context.t('please_enter_valid_phone_format');
                }

                return null;
              },
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.t('message_preview'),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _generateWhatsAppMessage(context),
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text(context.t('cancel')),
        ),
        ElevatedButton.icon(
          onPressed: _isLoading
              ? null
              : () {
                  if (_formKey.currentState!.validate()) {
                    final formattedPhone =
                        _formatPhoneNumber(_phoneController.text);
                    Navigator.of(context).pop({
                      'phoneNumber': formattedPhone,
                      'message': _generateWhatsAppMessage(context),
                    });
                  }
                },
          icon: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Icon(
                  Icons.send,
                  color: Colors.white,
                ),
          label: _isLoading
              ? Text(context.t('processing'))
              : Text(context.t('send_to_whatsapp')),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[600],
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
