import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_en.dart';
import 'package:queue_system/l10n/app_ms.dart';
import 'package:queue_system/l10n/app_zh.dart';

abstract class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  // 根据当前语言获取翻译映射
  Map<String, String> get _localizedValues {
    switch (locale.languageCode) {
      case 'en':
        return AppLocalizationsEn.values;
      case 'zh':
        return AppLocalizationsZh.values;
      case 'ms':
        return AppLocalizationsMs.values;
      default:
        return AppLocalizationsEn.values; // 默认使用英文
    }
  }

  // 获取指定键的翻译文本
  String translate(String key) {
    return _localizedValues[key] ?? key;
  }

  // 通过Localizations获取AppLocalizations实例的辅助方法
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // 本地化代理类
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  String get supervisor;
  String get regular_staff;
  String get bom_specialist;
  String get driver;
}

// 具体实现类
class _AppLocalizations extends AppLocalizations {
  _AppLocalizations(Locale locale) : super(locale);

  @override
  String get supervisor => translate('supervisor');

  @override
  String get regular_staff => translate('regular_staff');

  @override
  String get bom_specialist => translate('bom_specialist');

  @override
  String get driver => translate('driver');
}

// AppLocalizations的代理类，用于创建AppLocalizations实例
class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  // 设置支持的语言列表
  @override
  bool isSupported(Locale locale) {
    return ['en', 'zh', 'ms'].contains(locale.languageCode);
  }

  // 每当Locale改变时，重新加载AppLocalizations
  @override
  Future<AppLocalizations> load(Locale locale) async {
    return _AppLocalizations(locale);
  }

  // 当应用程序的语言设置改变时，是否重新加载
  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

// 扩展BuildContext，方便获取翻译
extension AppLocalizationsExtension on BuildContext {
  AppLocalizations get tr => AppLocalizations.of(this);

  // 更简洁的翻译方法
  String t(String key) => AppLocalizations.of(this).translate(key);
}
