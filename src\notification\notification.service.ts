import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from '../postgres/entities/notification.entity';
import { Staff } from '../postgres/entities/staff.entity';

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification, 'postgresConnection')
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>
  ) { }

  // 创建一个新的通知
  async createNotification(data: {
    type: string;
    message: string;
    recipient_id: number;
    sender_id: number;
    document_no: string;
    line: number;
    detail_id: number;
    reject_reason?: string;
  }): Promise<Notification> {
    const notification = this.notificationRepository.create(data);
    return this.notificationRepository.save(notification);
  }

  // 创建拒绝检查的通知
  async createRejectNotification(
    supervisorId: number,
    staffId: number,
    documentNo: string,
    line: number,
    detailId: number,
    reason: string
  ): Promise<Notification> {
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });

    let message = `Your check for document ${documentNo} line ${line} has been rejected by supervisor ${supervisor.username}`;
    if (reason) {
      message += `. Reason: ${reason}`;
    }

    return this.createNotification({
      type: 'reject',
      message,
      recipient_id: staffId,
      sender_id: supervisorId,
      document_no: documentNo,
      line,
      detail_id: detailId,
      reject_reason: reason || null
    });
  }

  // 获取用户的未读通知
  async getUnreadNotifications(userId: number): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: {
        recipient_id: userId,
        read: false
      },
      order: {
        created_at: 'DESC'
      }
    });
  }

  // 获取用户的所有通知
  async getAllNotifications(userId: number): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: {
        recipient_id: userId
      },
      order: {
        created_at: 'DESC'
      }
    });
  }

  // 标记通知为已读
  async markAsRead(notificationId: number): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id: notificationId }
    });

    if (notification) {
      notification.read = true;
      notification.read_at = new Date();
      return this.notificationRepository.save(notification);
    }

    return null;
  }

  // 标记所有通知为已读
  async markAllAsRead(userId: number): Promise<void> {
    await this.notificationRepository
      .createQueryBuilder()
      .update(Notification)
      .set({
        read: true,
        read_at: new Date()
      })
      .where('recipient_id = :userId AND read = :isRead', {
        userId,
        isRead: false
      })
      .execute();
  }
}