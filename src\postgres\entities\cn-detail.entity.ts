import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { CnMain } from './cn-main.entity';
import { Staff } from './staff.entity';

@Entity('cn_detail')
export class CnDetail {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '文档编号', example: 'CN123456' })
  @Column({ type: 'varchar', length: 20 })
  document_no: string;

  @ApiProperty({ description: '主表ID', example: 1 })
  @Column({ nullable: true })
  main_id: number;

  @ApiProperty({ description: '行号', example: 1 })
  @Column()
  line: number;

  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @Column({ nullable: true, length: 50 })
  stock: string;

  @ApiProperty({ description: '产品描述', example: '办公用品' })
  @Column({ nullable: true, length: 2000 })
  description: string;

  @ApiProperty({ description: '数量', example: 10 })
  @Column('decimal', { precision: 9, scale: 2, nullable: true })
  quantity: number;

  @ApiProperty({ description: '计量单位', example: 'PCS' })
  @Column({ nullable: true, length: 20 })
  uom: string;

  @ApiProperty({ description: '单价', example: 10.5 })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  unit_price: number;

  @ApiProperty({ description: '总金额', example: 105 })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  total_amount: number;

  @ApiProperty({ description: '库存退回确认人ID', example: 1 })
  @Column({ nullable: true })
  checked_id: number;

  @ApiProperty({ description: '库存退回确认时间', example: '2025-04-04T12:00:00Z' })
  @Column({ nullable: true })
  checked_at: Date;

  @ApiProperty({ description: '创建时间', example: '2025-04-04T12:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @ManyToOne(() => CnMain, main => main.details, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  })
  @JoinColumn({ name: 'main_id' })
  main: CnMain;

  @ManyToOne(() => Staff)
  @JoinColumn({ name: 'checked_id' })
  checker: Staff;
}
