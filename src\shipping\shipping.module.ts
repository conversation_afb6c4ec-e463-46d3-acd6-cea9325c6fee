import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Main } from '../postgres/entities/main.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { ShippingController } from './shipping.controller';
import { ShippingService } from './shipping.service';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Main, Staff, Detail], 'postgresConnection'),
    NotificationModule
  ],
  controllers: [ShippingController],
  providers: [ShippingService],
  exports: [ShippingService]
})
export class ShippingModule {} 