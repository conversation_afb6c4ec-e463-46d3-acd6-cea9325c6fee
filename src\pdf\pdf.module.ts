import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Main } from '../postgres/entities/main.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { PdfService } from './pdf.service';
import { HtmlPdfService } from './html-pdf.service';
import { PdfController } from './pdf.controller';
import { FirebaseModule } from '../firebase/firebase.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Main, Detail, Staff], 'postgresConnection'),
    FirebaseModule,
  ],
  controllers: [PdfController],
  providers: [PdfService, HtmlPdfService],
  exports: [PdfService, HtmlPdfService],
})
export class PdfModule { }
