import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';

class NetworkChecker {
  // 检查基本网络连接
  static Future<bool> hasInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // 运行全面诊断
  Future<String> runDiagnostics() async {
    final StringBuffer results = StringBuffer();
    results.writeln('=== Network Diagnostic Results ===\n');

    // 检查互联网连接
    results.writeln('1. Internet Connection:');
    final hasInternet = await hasInternetConnection();
    results.writeln(hasInternet
        ? '   ✓ Internet connection available'
        : '   ✗ No internet connection');
    results.writeln('');

    if (!hasInternet) {
      results.writeln('Further tests skipped due to no internet connection.');
      return results.toString();
    }

    // 检查常用服务器
    results.writeln('2. Common Server Access:');

    try {
      final googleResult =
          await checkServerConnection('https://www.google.com');
      results.writeln(googleResult['success']
          ? '   ✓ Google.com: Accessible'
          : '   ✗ Google.com: ${googleResult['message']}');
    } catch (e) {
      results.writeln('   ✗ Google.com: Error - $e');
    }

    // 检查API服务器
    results.writeln('\n3. API Server:');
    try {
      final apiResult = await checkServerConnection('http://localhost:3000');
      results.writeln(apiResult['success']
          ? '   ✓ API Server: Connected successfully (${apiResult['statusCode']})'
          : '   ✗ API Server: ${apiResult['message']}');

      if (apiResult['success']) {
        results.writeln('\nResponse preview:');
        results.writeln(apiResult['data'].toString().substring(
            0,
            apiResult['data'].toString().length > 500
                ? 500
                : apiResult['data'].toString().length));

        if (apiResult['data'].toString().length > 500) {
          results.writeln('... (response truncated)');
        }
      }
    } catch (e) {
      results.writeln('   ✗ API Server: Error - $e');
    }

    // 尝试检查特定端点
    results.writeln('\n4. Authentication Endpoint:');
    try {
      final authResult =
          await checkServerConnection('http://localhost:3000/staff');
      results.writeln(authResult['success']
          ? '   ✓ Auth Endpoint: Connected successfully (${authResult['statusCode']})'
          : '   ✗ Auth Endpoint: ${authResult['message']}');
    } catch (e) {
      results.writeln('   ✗ Auth Endpoint: Error - $e');
    }

    results.writeln('\n=== End of Diagnostic Report ===');
    return results.toString();
  }

  // 检查是否可以访问特定URL
  static Future<Map<String, dynamic>> checkServerConnection(String url) async {
    final result = <String, dynamic>{
      'success': false,
      'message': '',
      'statusCode': null,
      'data': null,
    };

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 10);

      final response = await dio.get(url);

      result['success'] = true;
      result['statusCode'] = response.statusCode;
      result['message'] = 'Server connection successful';
      result['data'] = response.data;
    } catch (e) {
      result['success'] = false;
      if (e is DioException) {
        final dioError = e;
        result['message'] = _getDioErrorMessage(dioError);
      } else {
        result['message'] = 'Unknown error: $e';
      }
    }

    return result;
  }

  // 尝试ping IP地址
  static Future<Map<String, dynamic>> pingServer(String host) async {
    final result = <String, dynamic>{
      'success': false,
      'message': '',
    };

    try {
      final socket =
          await Socket.connect(host, 3000, timeout: const Duration(seconds: 5));
      socket.destroy();
      result['success'] = true;
      result['message'] = 'Successfully connected to $host:3000';
    } catch (e) {
      result['success'] = false;
      result['message'] = 'Unable to connect to $host:3000: $e';
    }

    return result;
  }

  // 获取详细的Dio错误消息
  static String _getDioErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout, server response took too long';
      case DioExceptionType.sendTimeout:
        return 'Send request timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive response timeout';
      case DioExceptionType.badResponse:
        return 'Server returned error status code: ${error.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error: ${error.message}';
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return 'Unable to connect to server, please check if IP address and port are correct: ${error.message}';
        }
        return 'Unknown error: ${error.message}';
      default:
        return 'Request error: ${error.message}';
    }
  }
}
