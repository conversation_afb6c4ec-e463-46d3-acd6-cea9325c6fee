---
description:
globs:
alwaysApply: false
---
# 状态管理提供者

应用使用 Provider 包进行状态管理，各个 Provider 类负责不同的业务逻辑和状态管理。

## 认证状态提供者 (AuthProvider)

```dart
class AuthProvider with ChangeNotifier {
  AuthCredentialsModel? _credentials;
  final AuthService _authService;
  final SharedPreferences _prefs;
  bool _isLoading = false;
  String? _error;
  
  AuthProvider({required String baseUrl, required SharedPreferences prefs})
      : _authService = AuthService(apiService: ApiService(baseUrl: baseUrl)),
        _prefs = prefs;
  
  AuthCredentialsModel? get credentials => _credentials;
  bool get isAuthenticated => _credentials != null;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<bool> login(String username, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _credentials = await _authService.login(username, password);
      await _saveCredentials(_credentials!);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  Future<void> logout() async {
    _credentials = null;
    await _prefs.remove('auth_credentials');
    notifyListeners();
  }
  
  Future<void> tryAutoLogin() async {
    final String? credentialsJson = _prefs.getString('auth_credentials');
    if (credentialsJson != null) {
      try {
        _credentials = AuthCredentialsModel.fromJson(
          json.decode(credentialsJson) as Map<String, dynamic>
        );
        notifyListeners();
      } catch (e) {
        _error = '自动登录失败: ${e.toString()}';
      }
    }
  }
  
  Future<void> _saveCredentials(AuthCredentialsModel credentials) async {
    await _prefs.setString('auth_credentials', json.encode(credentials.toJson()));
  }
}
```

## 检查列表提供者 (CheckListProvider)

```dart
class CheckListProvider with ChangeNotifier {
  final CheckService _checkService;
  final AuthProvider _authProvider;
  
  List<CheckItemModel> _staffItems = [];
  List<CheckItemModel> _supervisorItems = [];
  List<CheckItemModel> _bomItems = [];
  FloorStatsModel? _floorStats;
  bool _isLoading = false;
  String? _error;
  
  CheckListProvider({required String baseUrl, required AuthProvider authProvider})
      : _checkService = CheckService(apiService: ApiService(baseUrl: baseUrl)),
        _authProvider = authProvider;
  
  List<CheckItemModel> get staffItems => _staffItems;
  List<CheckItemModel> get supervisorItems => _supervisorItems;
  List<CheckItemModel> get bomItems => _bomItems;
  FloorStatsModel? get floorStats => _floorStats;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> fetchCheckList() async {
    if (_authProvider.credentials == null) {
      _error = '用户未登录';
      return;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final items = await _checkService.getCheckList(_authProvider.credentials!.id);
      
      // 根据员工角色和检查状态过滤项目
      _staffItems = [];
      _supervisorItems = [];
      _bomItems = [];
      
      for (var item in items) {
        if (!item.staffChecked) {
          _staffItems.add(item);
        } else if (item.staffChecked && !item.supervisorChecked) {
          if (item.parentCode != null && !item.bomSpecialistChecked) {
            _bomItems.add(item);
          } else if (item.parentCode == null || item.bomSpecialistChecked) {
            _supervisorItems.add(item);
          }
        }
      }
      
      // 获取楼层统计信息
      if (_authProvider.credentials!.floor != 'ALL') {
        _floorStats = await _checkService.getFloorStats(_authProvider.credentials!.floor);
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<bool> staffCheck(String documentNo, int line) async {
    if (_authProvider.credentials == null) {
      _error = '用户未登录';
      return false;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _checkService.staffCheck(documentNo, line, _authProvider.credentials!.id);
      await fetchCheckList(); // 重新获取列表
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  Future<bool> supervisorCheck(String documentNo, int line) async {
    if (_authProvider.credentials == null ||
        (_authProvider.credentials!.level != 'supervisor' &&
         _authProvider.credentials!.level != 'admin' &&
         _authProvider.credentials!.level != 'driver')) {
      _error = '无权执行主管检查';
      return false;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _checkService.supervisorCheck(documentNo, line, _authProvider.credentials!.id);
      await fetchCheckList(); // 重新获取列表
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
```

## 订单提供者 (DocumentProvider)

```dart
class DocumentProvider with ChangeNotifier {
  final CheckService _checkService;
  final AuthProvider _authProvider;
  
  List<DeliveryOrderMainModel> _readyDocuments = [];
  bool _isLoading = false;
  String? _error;
  
  DocumentProvider({required String baseUrl, required AuthProvider authProvider})
      : _checkService = CheckService(apiService: ApiService(baseUrl: baseUrl)),
        _authProvider = authProvider;
  
  List<DeliveryOrderMainModel> get readyDocuments => _readyDocuments;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> fetchReadyDocuments() async {
    if (_authProvider.credentials == null ||
        (_authProvider.credentials!.level != 'supervisor' &&
         _authProvider.credentials!.level != 'admin' &&
         _authProvider.credentials!.level != 'driver')) {
      _error = '无权访问出货文档';
      return;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _readyDocuments = await _checkService.getReadyDocuments(_authProvider.credentials!.id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<bool> completeDocument(String documentNo, [int? driverId]) async {
    if (_authProvider.credentials == null ||
        (_authProvider.credentials!.level != 'supervisor' &&
         _authProvider.credentials!.level != 'admin' &&
         _authProvider.credentials!.level != 'driver')) {
      _error = '无权完成出货';
      return false;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _checkService.completeDocument(
        documentNo, 
        _authProvider.credentials!.id,
        driverId
      );
      await fetchReadyDocuments(); // 重新获取列表
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
```

## 出货提供者 (ShippingProvider)

```dart
class ShippingProvider with ChangeNotifier {
  final ShippingService _shippingService;
  final AuthProvider _authProvider;
  
  List<DeliveryOrderMainModel> _shipments = [];
  List<DeliveryOrderMainModel> _deliveries = [];
  bool _isLoading = false;
  String? _error;
  
  ShippingProvider({required String baseUrl, required AuthProvider authProvider})
      : _shippingService = ShippingService(apiService: ApiService(baseUrl: baseUrl)),
        _authProvider = authProvider;
  
  List<DeliveryOrderMainModel> get shipments => _shipments;
  List<DeliveryOrderMainModel> get deliveries => _deliveries;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> fetchShipments() async {
    if (_authProvider.credentials == null ||
        (_authProvider.credentials!.level != 'supervisor' &&
         _authProvider.credentials!.level != 'admin' &&
         _authProvider.credentials!.level != 'driver')) {
      _error = '无权访问出货单据';
      return;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _shipments = await _shippingService.getShippingList(_authProvider.credentials!.id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<void> fetchDeliveries() async {
    if (_authProvider.credentials == null || 
        _authProvider.credentials!.level != 'drive') {
      _error = '仅司机可查看送货列表';
      return;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _deliveries = await _shippingService.getDeliveryList(_authProvider.credentials!.id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<bool> shipDocument(String documentNo, [int? driverId]) async {
    if (_authProvider.credentials == null ||
        (_authProvider.credentials!.level != 'supervisor' &&
         _authProvider.credentials!.level != 'admin' &&
         _authProvider.credentials!.level != 'driver')) {
      _error = '无权执行出货操作';
      return false;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _shippingService.shipDocument(documentNo, _authProvider.credentials!.id, driverId);
      await fetchShipments(); // 重新获取列表
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
  
  Future<bool> confirmDelivery(String documentNo, String? notes) async {
    if (_authProvider.credentials == null || 
        _authProvider.credentials!.level != 'drive') {
      _error = '仅司机可确认送达';
      return false;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _shippingService.confirmDelivery(documentNo, _authProvider.credentials!.id, notes);
      await fetchDeliveries(); // 重新获取列表
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
```

## 通知提供者 (NotificationProvider)

```dart
class NotificationProvider with ChangeNotifier {
  final NotificationService _notificationService;
  final AuthProvider _authProvider;
  
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;
  
  NotificationProvider({required String baseUrl, required AuthProvider authProvider})
      : _notificationService = NotificationService(apiService: ApiService(baseUrl: baseUrl)),
        _authProvider = authProvider;
  
  List<NotificationModel> get notifications => _notifications;
  int get unreadCount => _notifications.where((n) => !n.read).length;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> fetchNotifications() async {
    if (_authProvider.credentials == null) {
      _error = '用户未登录';
      return;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      _notifications = await _notificationService.getUnreadNotifications(_authProvider.credentials!.id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }
  
  Future<bool> markAsRead(int notificationId) async {
    if (_authProvider.credentials == null) {
      _error = '用户未登录';
      return false;
    }
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _notificationService.markAsRead(notificationId);
      
      // 更新本地通知状态
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final updatedNotification = NotificationModel(
          id: _notifications[index].id,
          type: _notifications[index].type,
          message: _notifications[index].message,
          recipientId: _notifications[index].recipientId,
          senderId: _notifications[index].senderId,
          documentNo: _notifications[index].documentNo,
          line: _notifications[index].line,
          detailId: _notifications[index].detailId,
          read: true,
          readAt: DateTime.now(),
          rejectReason: _notifications[index].rejectReason,
          createdAt: _notifications[index].createdAt,
        );
        
        _notifications[index] = updatedNotification;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
```

## 本地化提供者 (LocaleProvider)

```dart
class LocaleProvider with ChangeNotifier {
  Locale _locale = const Locale('zh', 'CN');
  final SharedPreferences _prefs;
  
  LocaleProvider({required SharedPreferences prefs}) : _prefs = prefs {
    _loadSavedLocale();
  }
  
  Locale get locale => _locale;
  
  void setLocale(Locale locale) {
    _locale = locale;
    _saveLocale(locale);
    notifyListeners();
  }
  
  void _loadSavedLocale() {
    final String? languageCode = _prefs.getString('language_code');
    final String? countryCode = _prefs.getString('country_code');
    
    if (languageCode != null) {
      _locale = Locale(languageCode, countryCode);
    }
  }
  
  Future<void> _saveLocale(Locale locale) async {
    await _prefs.setString('language_code', locale.languageCode);
    if (locale.countryCode != null) {
      await _prefs.setString('country_code', locale.countryCode!);
    }
  }
}
```
