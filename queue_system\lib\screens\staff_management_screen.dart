import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/staff_provider.dart';
import 'package:queue_system/screens/add_staff_screen.dart';
import 'package:queue_system/screens/change_password_screen.dart';
import 'package:queue_system/models/staff.dart';

class StaffManagementScreen extends StatefulWidget {
  const StaffManagementScreen({super.key});

  @override
  State<StaffManagementScreen> createState() => _StaffManagementScreenState();
}

class _StaffManagementScreenState extends State<StaffManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final staffProvider = Provider.of<StaffProvider>(context, listen: false);
      staffProvider.loadStaffList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('staff_management')),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: context.t('add_staff'),
            onPressed: () {
              Navigator.of(context)
                  .push(
                MaterialPageRoute(
                  builder: (context) => const AddStaffScreen(),
                ),
              )
                  .then((_) {
                // 返回时刷新列表
                final staffProvider =
                    Provider.of<StaffProvider>(context, listen: false);
                staffProvider.loadStaffList();
              });
            },
          ),
        ],
      ),
      body: Consumer<StaffProvider>(
        builder: (context, staffProvider, child) {
          if (staffProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (staffProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    context.t('error_loading_staff'),
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => staffProvider.loadStaffList(),
                    child: Text(context.t('retry')),
                  ),
                ],
              ),
            );
          }

          final staffList = staffProvider.staffList;
          if (staffList.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(context.t('no_staff_found')),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => staffProvider.loadStaffList(),
                    child: Text(context.t('refresh')),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => staffProvider.loadStaffList(),
            child: ListView.builder(
              itemCount: staffList.length,
              itemBuilder: (context, index) {
                final staff = staffList[index];
                return _buildStaffCard(context, staff);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildStaffCard(BuildContext context, Staff staff) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentStaff;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getLevelColor(staff.level),
          child: Text(
            staff.fullName.isNotEmpty
                ? staff.fullName[0].toUpperCase()
                : staff.username[0].toUpperCase(),
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title:
            Text(staff.fullName.isNotEmpty ? staff.fullName : staff.username),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('username')}: ${staff.username}'),
            Text(
                '${context.t('level')}: ${_getLevelDisplayName(context, staff.level)}'),
            Text('${context.t('floor')}: ${staff.floor}'),
            Text('${context.t('status')}: ${staff.status}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'change_password':
                _showChangePasswordDialog(context, staff);
                break;
              case 'toggle_status':
                _toggleStaffStatus(context, staff);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'change_password',
              child: Row(
                children: [
                  const Icon(Icons.lock),
                  const SizedBox(width: 8),
                  Text(context.t('change_password')),
                ],
              ),
            ),
            if (currentUser?.id != staff.id) // 不能禁用自己的账户
              PopupMenuItem(
                value: 'toggle_status',
                child: Row(
                  children: [
                    Icon(staff.status == 'active'
                        ? Icons.block
                        : Icons.check_circle),
                    const SizedBox(width: 8),
                    Text(staff.status == 'active'
                        ? context.t('deactivate')
                        : context.t('activate')),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getLevelColor(String level) {
    switch (level) {
      case 'supervisor':
        return Colors.purple;
      case 'admin':
        return Colors.red;
      case 'staff_bom':
        return Colors.blue;
      case 'driver':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getLevelDisplayName(BuildContext context, String level) {
    switch (level) {
      case 'supervisor':
        return context.t('supervisor');
      case 'admin':
        return context.t('admin');
      case 'staff_bom':
        return context.t('bom_specialist');
      case 'driver':
        return context.t('driver');
      default:
        return context.t('regular_staff');
    }
  }

  void _showChangePasswordDialog(BuildContext context, Staff staff) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChangePasswordScreen(staff: staff),
      ),
    );
  }

  void _toggleStaffStatus(BuildContext context, Staff staff) {
    final staffProvider = Provider.of<StaffProvider>(context, listen: false);
    final newStatus = staff.status == 'active' ? 'inactive' : 'active';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('confirm')),
        content: Text(
          staff.status == 'active'
              ? context.t('confirm_deactivate_staff')
              : context.t('confirm_activate_staff'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.t('cancel')),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              staffProvider.updateStaffStatus(staff.id, newStatus);
            },
            child: Text(context.t('confirm')),
          ),
        ],
      ),
    );
  }
}
