import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/shipping_document.dart';
import 'package:queue_system/screens/signature_screen.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/services/pdf_service.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/screens/native_pdf_viewer_screen.dart';
import 'package:queue_system/widgets/email_dialog.dart';
import 'package:queue_system/widgets/pdf_share_options_dialog.dart';

class ToDeliveryTab extends StatefulWidget {
  final int driverId;
  final Function? onDataChanged; // 添加回调函数，用于通知父组件数据变化

  const ToDeliveryTab({
    super.key,
    required this.driverId,
    this.onDataChanged,
  });

  @override
  State<ToDeliveryTab> createState() => _ToDeliveryTabState();
}

class _ToDeliveryTabState extends State<ToDeliveryTab> {
  final _checkService = CheckService(baseUrl: AppConfig.baseUrl);
  final _pdfService = PdfService();
  List<ShippingDocument> _deliveryDocuments = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDeliveryDocuments();
  }

  Future<void> _loadDeliveryDocuments() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final documents =
          await _checkService.getDriverDeliveryList(widget.driverId);

      // 过滤出已指派driver的订单
      final filteredDocuments = documents
          .where(
              (doc) => doc.driverId != null && doc.driverId == widget.driverId)
          .toList();

      setState(() {
        _deliveryDocuments = filteredDocuments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        debugPrint("ToDeliveryTab加载错误: $e");
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(context.t('loading_failed')),
            const SizedBox(height: 8),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDeliveryDocuments,
              child: Text(context.t('retry')),
            ),
          ],
        ),
      );
    }

    if (_deliveryDocuments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.local_shipping_outlined,
                size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(context.t('no_delivery_documents')),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDeliveryDocuments,
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDeliveryDocuments,
      child: ListView.builder(
        itemCount: _deliveryDocuments.length,
        itemBuilder: (context, index) {
          final document = _deliveryDocuments[index];
          return _buildDocumentCard(document);
        },
      ),
    );
  }

  Widget _buildDocumentCard(ShippingDocument document) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LayoutBuilder(
              builder: (context, constraints) {
                // 获取屏幕宽度
                final screenWidth = MediaQuery.of(context).size.width;
                // 根据屏幕宽度调整布局
                final isSmallScreen = screenWidth < 360;

                return Row(
                  children: [
                    Expanded(
                      child: Text(
                        document.documentNo,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: isSmallScreen ? 16 : 18,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 6 : 8,
                          vertical: isSmallScreen ? 2 : 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        context.t('to_deliver'),
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 8),
            Text('${context.t('date')}: ${_formatDate(document.documentDate)}'),
            const SizedBox(height: 4),
            LayoutBuilder(
              builder: (context, constraints) {
                // 获取屏幕宽度
                final screenWidth = MediaQuery.of(context).size.width;
                // 根据屏幕宽度调整布局
                final isSmallScreen = screenWidth < 360;

                return isSmallScreen
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${context.t('customer')}: ${document.customerCode} - ${document.customerName}',
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    minimumSize: const Size(50, 28),
                                  ),
                                  onPressed: () => _showRejectDialog(document),
                                  child: Text(
                                    context.t('Reject'),
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    minimumSize: const Size(50, 28),
                                  ),
                                  onPressed: () =>
                                      _showConfirmDeliveryDialog(document),
                                  child: Text(
                                    context.t('delivered'),
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              '${context.t('customer')}: ${document.customerCode} - ${document.customerName}',
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 4),
                              minimumSize: const Size(60, 30),
                            ),
                            onPressed: () => _showRejectDialog(document),
                            child: Text(context.t('Reject')),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 4),
                              minimumSize: const Size(60, 30),
                            ),
                            onPressed: () =>
                                _showConfirmDeliveryDialog(document),
                            child: Text(context.t('delivered')),
                          ),
                        ],
                      );
              },
            ),
            const SizedBox(height: 4),
            if (document.remarks.isNotEmpty)
              Text('${context.t('remarks')}: ${document.remarks}'),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  // 显示确认送达对话框
  Future<void> _showConfirmDeliveryDialog(ShippingDocument document) async {
    final TextEditingController notesController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('confirm_delivery')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${document.documentNo}'),
            const SizedBox(height: 16),
            Text(context.t('delivery_notes')),
            const SizedBox(height: 8),
            TextField(
              controller: notesController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: context.t('delivery_notes_hint'),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(context.t('confirm')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 打开签名页面
      final signatureBase64 = await Navigator.push<String>(
        context,
        MaterialPageRoute(
          builder: (context) => SignatureScreen(
            documentNo: document.documentNo,
          ),
        ),
      );

      // 签名页面现在总是返回字符串（可能为空），不会返回null
      // 如果用户取消了签名，则不继续处理
      if (signatureBase64 == null || !mounted) {
        return;
      }

      // 显示加载指示器
      if (!mounted) return;

      // 直接显示对话框，不使用 addPostFrameCallback
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          content: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      try {
        // 添加调试信息
        debugPrint("开始上传签名: ${document.documentNo}");

        // 先上传签名并添加到PDF
        final signatureResponse = await _pdfService.addSignatureToPdf(
            document.documentNo, signatureBase64);

        // 添加调试信息
        debugPrint("签名上传成功: $signatureResponse");

        // 关闭加载指示器
        if (mounted) {
          debugPrint("关闭加载对话框");
          // 使用try-catch包装，避免找不到对话框的错误
          try {
            Navigator.of(context).pop();
          } catch (e) {
            debugPrint("关闭对话框失败，可能已经关闭: $e");
          }

          // 立即显示Email选项对话框
          await _showEmailOptionsDialog(document, notesController.text);
        }
      } catch (e) {
        // 这里捕获的是除了API直接错误之外的其他错误
        if (mounted) {
          debugPrint("确认送达过程中发生错误: $e");

          // 关闭加载指示器
          try {
            Navigator.of(context).pop();
          } catch (dialogError) {
            // 对话框可能已经关闭
          }

          // 显示错误提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${context.t('error_processing_signature')}: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // 显示拒绝送达对话框
  Future<void> _showRejectDialog(ShippingDocument document) async {
    final TextEditingController reasonController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('reject_delivery')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${document.documentNo}'),
            const SizedBox(height: 16),
            Text(context.t('rejection_reason')),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: context.t('rejection_reason_hint'),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              context.t('confirm_rejection'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 使用try-catch包装API调用
        Map<String, dynamic>? response;
        try {
          response = await _checkService.rejectDelivery(
              document.documentNo, widget.driverId, reasonController.text);

          // API调用成功，直接从列表中移除该文档
          if (mounted) {
            setState(() {
              _deliveryDocuments
                  .removeWhere((doc) => doc.documentNo == document.documentNo);
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text(response['message'] ?? context.t('delivery_rejected')),
                backgroundColor: Colors.orange,
              ),
            );

            // 通知父组件刷新计数
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // 调用父组件提供的回调函数
              if (widget.onDataChanged != null) {
                widget.onDataChanged!();
              }
              // 重新加载列表确保数据一致
              _loadDeliveryDocuments();
            });
          }
        } catch (apiError) {
          // 检查错误消息是否表明操作实际上是成功的
          String errorMsg = apiError.toString().toLowerCase();
          if (errorMsg.contains('success') ||
              errorMsg.contains('已拒绝') ||
              errorMsg.contains('rejected') ||
              errorMsg.contains('reset to unshipped')) {
            // 虽然抛出异常，但实际上操作成功了
            if (mounted) {
              setState(() {
                _deliveryDocuments.removeWhere(
                    (doc) => doc.documentNo == document.documentNo);
              });

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.t('delivery_rejected')),
                  backgroundColor: Colors.orange,
                ),
              );

              // 通知父组件刷新计数
              WidgetsBinding.instance.addPostFrameCallback((_) {
                // 调用父组件提供的回调函数
                if (widget.onDataChanged != null) {
                  widget.onDataChanged!();
                }
                _loadDeliveryDocuments();
              });
            }
          } else {
            // 真正的错误，需要刷新整个列表
            // 先保存错误信息
            final String errorMessage = apiError.toString();

            if (mounted) {
              // 先获取本地化文本，然后再进行异步操作
              final String localizedMessage = context.t('rejection_failed');

              // 先显示错误消息
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$localizedMessage: $errorMessage'),
                  backgroundColor: Colors.red,
                ),
              );

              // 然后重新加载数据
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                if (mounted) {
                  await _loadDeliveryDocuments();
                }
              });
            }
          }
        }
      } catch (e) {
        // 这里捕获的是除了API直接错误之外的其他错误
        if (mounted) {
          // 先保存错误信息和本地化文本
          final String errorMessage = e.toString();
          final String localizedMessage = context.t('rejection_failed');

          // 先显示错误消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$localizedMessage: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );

          // 然后重新加载数据
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            if (mounted) {
              // 调用父组件提供的回调函数
              if (widget.onDataChanged != null) {
                widget.onDataChanged!();
              }
              await _loadDeliveryDocuments();
            }
          });
        }
      }
    }
  }

  // 显示Email选项对话框
  Future<void> _showEmailOptionsDialog(
      ShippingDocument document, String deliveryNotes) async {
    if (!mounted) return;

    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => PdfShareOptionsDialog(
        documentNo: document.documentNo,
        defaultEmail: document.customerEmail,
        defaultPhoneNumber: document.customerTelephone,
        onSendEmail: (email) async {
          Navigator.of(context).pop(); // 关闭对话框
          await _sendPdfByEmail(document, email);

          // 确认送达并从列表中移除该文档
          await _confirmDeliveryAndRemove(document, deliveryNotes);
        },
        onSkipEmail: () async {
          Navigator.of(context).pop(); // 关闭对话框
          // 显示成功提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('pdf_created_successfully')),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );

          // 确认送达并从列表中移除该文档
          await _confirmDeliveryAndRemove(document, deliveryNotes);
        },
        onViewPdf: (pdfUrl) {
          Navigator.of(context).pop(); // 关闭对话框
          // 打开PDF查看页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NativePdfViewerScreen(
                pdfUrl: pdfUrl,
                title: '${document.documentNo} PDF',
              ),
            ),
          ).then((_) {
            // 当PDF查看页面关闭后，再次显示Email选项对话框
            _showEmailOptionsDialog(document, deliveryNotes);
          });
        },
      ),
    );
  }

  // 确认送达并从列表中移除文档
  Future<void> _confirmDeliveryAndRemove(
      ShippingDocument document, String deliveryNotes) async {
    try {
      // 调用API确认送达
      final response = await _checkService.confirmDelivery(
          document.documentNo, widget.driverId, deliveryNotes);

      // 从列表中移除该文档
      _removeDocumentFromList(document);

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(response['message'] ?? context.t('delivery_confirmed')),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (apiError) {
      // 检查错误消息是否表明操作实际上是成功的
      String errorMsg = apiError.toString().toLowerCase();
      if (errorMsg.contains('already delivered') ||
          errorMsg.contains('已送达') ||
          errorMsg.contains('已被') && errorMsg.contains('确认送达') ||
          errorMsg.contains('has been delivered by driver') ||
          errorMsg.contains('error_confirm_delivery') &&
              (errorMsg.contains('已送达') ||
                  errorMsg.contains('已被') ||
                  errorMsg.contains('已确认')) ||
          errorMsg.contains('success')) {
        // 虽然抛出异常，但实际上操作成功了
        _removeDocumentFromList(document);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('delivery_confirmed')),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // 真正的错误，需要刷新整个列表
        debugPrint("确认送达操作发生错误: $apiError");

        if (mounted) {
          // 显示错误消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${context.t('delivery_confirmation_failed')}: $apiError'),
              backgroundColor: Colors.red,
            ),
          );

          // 重新加载数据
          await _loadDeliveryDocuments();
        }
      }
    }
  }

  // 从列表中移除文档并通知父组件数据变化
  void _removeDocumentFromList(ShippingDocument document) {
    if (mounted) {
      setState(() {
        _deliveryDocuments
            .removeWhere((doc) => doc.documentNo == document.documentNo);
      });

      // 通知父组件数据已变化，需要更新计数
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.onDataChanged != null) {
          widget.onDataChanged!();
        }
      });
    }
  }

  // 通过邮件发送PDF
  Future<void> _sendPdfByEmail(ShippingDocument document,
      [String? prefilledEmail]) async {
    // 如果没有预填充的邮箱，则显示邮件输入对话框
    String? email = prefilledEmail;
    email ??= await showDialog<String>(
      context: context,
      builder: (context) => EmailDialog(
        documentNo: document.documentNo,
        defaultEmail: document.customerEmail,
      ),
    );

    // 如果用户取消了操作，email将为null
    if (email == null) return;

    // 显示加载指示器
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(context.t('sending_email')),
          ],
        ),
      ),
    );

    try {
      // 发送邮件
      final result = await _pdfService.sendPdfEmail(
        document.documentNo,
        email,
        subject: 'Delivery Order: ${document.documentNo}',
        text:
            'Please find the attached delivery order PDF for document ${document.documentNo}.',
      );

      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示结果
      if (mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('email_sent_successfully')),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${context.t('email_sent_failed')}: ${result['message']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('email_sent_failed')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 格式化日期
  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }
}
