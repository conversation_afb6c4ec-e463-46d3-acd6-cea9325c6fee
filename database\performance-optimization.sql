-- 性能优化索引脚本
-- 用于优化 to_confirm 页面 confirm 操作的数据库查询性能

-- 1. 为 delivery_order_detail 表创建复合索引
-- 优化 document_no + line 查询
CREATE INDEX IF NOT EXISTS idx_detail_document_line 
ON delivery_order_detail (document_no, line);

-- 2. 为 staff 表创建索引
-- 优化 supervisor 查询
CREATE INDEX IF NOT EXISTS idx_staff_id_level 
ON staff (id, level);

-- 3. 为 delivery_order_detail 表创建状态查询索引
-- 优化检查状态查询
CREATE INDEX IF NOT EXISTS idx_detail_check_status 
ON delivery_order_detail (staff_checked, supervisor_checked, bom_specialist_checked);

-- 4. 为 BOM 相关查询创建索引
-- 优化 BOM 层级查询
CREATE INDEX IF NOT EXISTS idx_detail_bom_parent_code 
ON delivery_order_detail (parent_code, is_bom_parent);

-- 5. 为楼层查询创建索引
-- 优化按楼层过滤的查询
CREATE INDEX IF NOT EXISTS idx_detail_bin_shelf_floor 
ON delivery_order_detail (bin_shelf_no);

-- 6. 为主表关联查询创建索引
-- 优化 main_id 关联查询
CREATE INDEX IF NOT EXISTS idx_detail_main_id 
ON delivery_order_detail (main_id);

-- 7. 为时间戳查询创建索引
-- 优化按时间排序的查询
CREATE INDEX IF NOT EXISTS idx_detail_created_at 
ON delivery_order_detail (created_at);

-- 8. 为库存状态查询创建索引
-- 优化库存相关查询
CREATE INDEX IF NOT EXISTS idx_detail_have_stock_bin 
ON delivery_order_detail (have_stock, bin_shelf_no);

-- 9. 为 BOM 组查询创建索引
-- 优化 BOM 组相关查询
CREATE INDEX IF NOT EXISTS idx_detail_bom_group 
ON delivery_order_detail (bom_group);

-- 10. 为员工检查状态创建复合索引
-- 优化员工检查相关查询
CREATE INDEX IF NOT EXISTS idx_detail_staff_check_composite 
ON delivery_order_detail (staff_checked, staff_id, staff_checked_at);

-- 11. 为主管检查状态创建复合索引
-- 优化主管检查相关查询
CREATE INDEX IF NOT EXISTS idx_detail_supervisor_check_composite 
ON delivery_order_detail (supervisor_checked, supervisor_id, supervisor_checked_at);

-- 12. 为 BOM 专员检查状态创建复合索引
-- 优化 BOM 专员检查相关查询
CREATE INDEX IF NOT EXISTS idx_detail_bom_specialist_check_composite 
ON delivery_order_detail (bom_specialist_checked, bom_specialist_id, bom_specialist_checked_at);

-- 查询性能分析
-- 使用以下查询来分析索引使用情况：

-- 检查索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('delivery_order_detail', 'delivery_order_main', 'staff')
ORDER BY idx_scan DESC;

-- 检查表统计信息
SELECT 
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_live_tup,
    n_dead_tup,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE tablename IN ('delivery_order_detail', 'delivery_order_main', 'staff');

-- 性能监控查询
-- 监控慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%delivery_order_detail%' 
ORDER BY mean_time DESC 
LIMIT 10;
