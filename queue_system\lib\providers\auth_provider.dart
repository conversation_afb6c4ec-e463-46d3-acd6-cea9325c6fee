import 'package:flutter/material.dart';
import 'package:queue_system/models/staff.dart';
import 'package:queue_system/services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  Staff? _currentStaff;
  bool _isLoading = false;
  String? _error;
  bool _registerSuccess = false;

  Staff? get currentStaff => _currentStaff;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get registerError => _error;
  bool get isAuthenticated => _currentStaff != null;
  bool get isSupervisor =>
      _currentStaff?.level == 'supervisor' || _currentStaff?.level == 'admin';
  bool get isAdmin => _currentStaff?.level == 'admin';
  // 保持向后兼容性
  bool get isSenior => isSupervisor;
  bool get registerSuccess => _registerSuccess;

  final AuthService _authService;

  AuthProvider({required String baseUrl})
      : _authService = AuthService(baseUrl: baseUrl);

  // 处理错误消息，返回本地化键
  String _mapErrorToLocalizationKey(String error) {
    // 从AuthErrorCode类中获取错误代码
    if (error.contains(AuthErrorCode.invalidCredentials)) {
      return 'error_invalid_credentials';
    } else if (error.contains(AuthErrorCode.networkError)) {
      return 'error_network';
    } else if (error.contains(AuthErrorCode.serverEmpty)) {
      return 'error_empty_response';
    } else if (error.contains(AuthErrorCode.loginFailed)) {
      return 'error_login_failed';
    } else if (error.contains(AuthErrorCode.createStaffFailed)) {
      return 'error_create_staff';
    } else if (error.contains(AuthErrorCode.invalidData)) {
      return 'error_invalid_data';
    } else {
      // 如果是服务器返回的错误消息，保留原样
      return error;
    }
  }

  Future<void> login(String username, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _currentStaff = await _authService.login(username, password);
    } catch (e) {
      _error = _mapErrorToLocalizationKey(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> register({
    required String username,
    required String password,
    required String level,
    required String floor,
    required String fullName,
  }) async {
    _isLoading = true;
    _error = null;
    _registerSuccess = false;
    notifyListeners();

    try {
      final staff = await _authService.createStaff(
        username: username,
        password: password,
        level: level,
        floor: floor,
        fullName: fullName,
      );

      if (staff != null) {
        _registerSuccess = true;
      }
    } catch (e) {
      _error = _mapErrorToLocalizationKey(e.toString());
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Staff?> createStaff({
    required String username,
    required String password,
    required String level,
    required String floor,
    required String fullName,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final staff = await _authService.createStaff(
        username: username,
        password: password,
        level: level,
        floor: floor,
        fullName: fullName,
      );
      return staff;
    } catch (e) {
      _error = _mapErrorToLocalizationKey(e.toString());
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void logout() {
    _currentStaff = null;
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearRegisterError() {
    _error = null;
    notifyListeners();
  }

  void clearRegisterSuccess() {
    _registerSuccess = false;
    notifyListeners();
  }
}
