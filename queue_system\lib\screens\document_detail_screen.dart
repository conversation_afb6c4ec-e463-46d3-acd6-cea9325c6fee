import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/document.dart';

class DocumentDetailScreen extends StatelessWidget {
  final Document document;

  const DocumentDetailScreen({
    super.key,
    required this.document,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${context.t('document_title')} ${document.documentNo}'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDocumentHeader(context),
            const SizedBox(height: 24),
            Text(
              context.t('detail_items'),
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            _buildDetailsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentHeader(BuildContext context) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final timeFormat = DateFormat('HH:mm:ss');

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(context, 'document_number', document.documentNo),
            _buildInfoRow(
                context, 'transaction_type', document.transactionType),
            _buildInfoRow(context, 'document_date',
                dateFormat.format(document.documentDate)),
            _buildInfoRow(context, 'customer_code', document.customerCode),
            _buildInfoRow(context, 'customer_name', document.customerName),
            _buildInfoRow(context, 'salesman_code', document.salesmanCode),
            _buildInfoRow(context, 'issuer', document.issueBy),
            _buildInfoRow(
                context, 'issue_date', dateFormat.format(document.issueDate)),
            _buildInfoRow(
                context, 'issue_time', timeFormat.format(document.issueTime)),
            _buildInfoRow(context, 'delivery_address', document.deliverTo),
            _buildInfoRow(context, 'remarks', document.remarks),
            _buildInfoRow(
                context, 'amount', '${document.doAmount.toStringAsFixed(2)}'),
            _buildInfoRow(context, 'created_at',
                DateFormat('yyyy-MM-dd HH:mm:ss').format(document.createdAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String labelKey, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              context.t(labelKey),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsList(BuildContext context) {
    final details = document.details;

    if (details == null || details.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(context.t('no_detail_data')),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: details.length,
      itemBuilder: (context, index) {
        final detail = details[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            title: Text(detail.stockCode),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${context.t('location')}: ${detail.locationCode}'),
                Text(
                    '${context.t('quantity')}: ${detail.doQuantity.toStringAsFixed(2)}'),
              ],
            ),
          ),
        );
      },
    );
  }
}
