import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/stock_return_model.dart';
import 'package:queue_system/models/stock_return_tracking_model.dart';
import 'package:queue_system/providers/stock_return_provider.dart';
import 'package:queue_system/providers/stock_return_tracking_provider.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/widgets/empty_list_placeholder.dart';

class StockReturnScreen extends StatefulWidget {
  const StockReturnScreen({Key? key}) : super(key: key);

  @override
  State<StockReturnScreen> createState() => _StockReturnScreenState();
}

class _StockReturnScreenState extends State<StockReturnScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 加载两个 Provider 的数据
    Future.microtask(() {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.currentStaff != null) {
        // 加载 CN Return 数据
        Provider.of<StockReturnProvider>(context, listen: false)
            .loadPendingReturns();
        // 加载 Stock Return Tracking 数据
        Provider.of<StockReturnTrackingProvider>(context, listen: false)
            .loadPendingTracking(authProvider.currentStaff!.id);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('stock_return')),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.assignment_return),
              text: context.t('cn_return'),
            ),
            Tab(
              icon: const Icon(Icons.track_changes),
              text: context.t('return_tracking'),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final authProvider =
                  Provider.of<AuthProvider>(context, listen: false);
              if (authProvider.currentStaff != null) {
                // 刷新两个 Provider 的数据
                Provider.of<StockReturnProvider>(context, listen: false)
                    .loadPendingReturns();
                Provider.of<StockReturnTrackingProvider>(context, listen: false)
                    .loadPendingTracking(authProvider.currentStaff!.id);
              }
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // CN Return Tab
          _buildCnReturnTab(),
          // Return Tracking Tab
          _buildReturnTrackingTab(),
        ],
      ),
    );
  }

  // 构建 CN Return Tab
  Widget _buildCnReturnTab() {
    return Consumer2<StockReturnProvider, AuthProvider>(
      builder: (context, stockReturnProvider, authProvider, child) {
        if (stockReturnProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (stockReturnProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(stockReturnProvider.error!),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    stockReturnProvider.loadPendingReturns();
                  },
                  child: Text(context.t('retry')),
                ),
              ],
            ),
          );
        }

        if (stockReturnProvider.pendingReturns.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.assignment_return,
                    size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  context.t('no_pending_cn_returns'),
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    stockReturnProvider.loadPendingReturns();
                  },
                  child: Text(context.t('refresh')),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: stockReturnProvider.pendingReturns.length,
          itemBuilder: (context, index) {
            final item = stockReturnProvider.pendingReturns[index];
            return _buildCnReturnCard(
                context, item, authProvider.currentStaff!.id);
          },
        );
      },
    );
  }

  // 构建 Return Tracking Tab
  Widget _buildReturnTrackingTab() {
    return Consumer2<StockReturnTrackingProvider, AuthProvider>(
      builder: (context, trackingProvider, authProvider, child) {
        if (trackingProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (trackingProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(trackingProvider.error!),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (authProvider.currentStaff != null) {
                      trackingProvider
                          .loadPendingTracking(authProvider.currentStaff!.id);
                    }
                  },
                  child: Text(context.t('retry')),
                ),
              ],
            ),
          );
        }

        if (trackingProvider.pendingTracking.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.track_changes, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  context.t('no_pending_stock_returns'),
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (authProvider.currentStaff != null) {
                      trackingProvider
                          .loadPendingTracking(authProvider.currentStaff!.id);
                    }
                  },
                  child: Text(context.t('refresh')),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: trackingProvider.pendingTracking.length,
          itemBuilder: (context, index) {
            final item = trackingProvider.pendingTracking[index];
            return _buildTrackingCard(
                context, item, authProvider.currentStaff!.id);
          },
        );
      },
    );
  }

  // 构建 CN Return 卡片
  Widget _buildCnReturnCard(
      BuildContext context, StockReturnModel item, int staffId) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 文档编号和状态
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${item.documentNo}/${item.line}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: item.checked ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    item.checked
                        ? context.t('completed')
                        : context.t('pending'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 物品信息
            Text(
              '${item.stock} - ${item.description}',
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),

            // 数量和货架
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      const Icon(Icons.inventory, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${context.t('quantity')}: ${item.quantity.toStringAsFixed(0)} ${item.uom}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.blue),
                    const SizedBox(width: 4),
                    Text(
                      item.binShelfNo,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 创建时间
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${context.t('created_at')}: ${dateFormat.format(item.createdAt)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),

            // 如果未完成，显示确认按钮
            if (!item.checked) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () => _confirmCnReturn(context, item, staffId),
                  child: Text(context.t('confirm_return')),
                ),
              ),
            ],

            // 如果已完成，显示完成信息
            if (item.checked && item.checkedAt != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.t('completed_at')}: ${dateFormat.format(item.checkedAt!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green.shade700,
                      ),
                    ),
                    if (item.checkBy != null && item.checkBy!.isNotEmpty)
                      Text(
                        '${context.t('completed_by')}: ${item.checkBy}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green.shade700,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // 构建追踪任务卡片
  Widget _buildTrackingCard(
      BuildContext context, StockReturnTrackingModel item, int staffId) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 文档编号和状态
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${item.documentNo}/${item.line}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: item.isCompleted ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    item.isCompleted
                        ? context.t('completed')
                        : context.t('pending'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 物品信息
            Text(
              item.stockCode,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),

            // 数量和货架
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      const Icon(Icons.inventory, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${context.t('quantity')}: ${item.quantity.toStringAsFixed(0)}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.blue),
                    const SizedBox(width: 4),
                    Text(
                      item.binShelfNo,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 触发原因和时间
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${context.t('reason')}: ${_getTriggerReasonText(context, item.triggerReason)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
                Text(
                  dateFormat.format(item.createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),

            // 如果未完成，显示确认按钮
            if (!item.isCompleted) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () =>
                      _confirmStockReturnTracking(context, item, staffId),
                  child: Text(context.t('confirm_return')),
                ),
              ),
            ],

            // 如果已完成，显示完成信息
            if (item.isCompleted && item.completedAt != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${context.t('completed_at')}: ${dateFormat.format(item.completedAt!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green.shade700,
                      ),
                    ),
                    if (item.notes != null && item.notes!.isNotEmpty)
                      Text(
                        '${context.t('notes')}: ${item.notes}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green.shade700,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getTriggerReasonText(BuildContext context, String reason) {
    switch (reason) {
      case 'order_refresh':
        return context.t('order_refresh');
      case 'order_deleted':
        return context.t('order_deleted');
      case 'quantity_reduced':
        return context.t('quantity_reduced');
      case 'item_rejected':
        return context.t('item_rejected');
      default:
        return reason;
    }
  }

  void _confirmStockReturnTracking(
      BuildContext context, StockReturnTrackingModel item, int staffId) {
    final TextEditingController notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('confirm_return')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                '${context.t('document_no')}: ${item.documentNo}/${item.line}'),
            Text('${context.t('stock_code')}: ${item.stockCode}'),
            Text('${context.t('shelf')}: ${item.binShelfNo}'),
            const SizedBox(height: 16),
            Text(context.t('notes_optional')),
            const SizedBox(height: 8),
            TextField(
              controller: notesController,
              decoration: InputDecoration(
                hintText: context.t('enter_notes'),
                border: const OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final result = await Provider.of<StockReturnTrackingProvider>(
                      context,
                      listen: false)
                  .confirmTracking(item.id, staffId,
                      notes: notesController.text.trim());

              if (result && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(context.t('return_confirmed')),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: Text(context.t('confirm')),
          ),
        ],
      ),
    );
  }

  void _confirmCnReturn(
      BuildContext context, StockReturnModel item, int staffId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('confirm_return')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                '${context.t('document_no')}: ${item.documentNo}/${item.line}'),
            Text('${context.t('stock_code')}: ${item.stock}'),
            Text('${context.t('description')}: ${item.description}'),
            Text('${context.t('shelf')}: ${item.binShelfNo}'),
            Text(
                '${context.t('quantity')}: ${item.quantity.toStringAsFixed(0)} ${item.uom}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final result =
                  await Provider.of<StockReturnProvider>(context, listen: false)
                      .confirmStockReturn(item.id);

              if (result && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(context.t('return_confirmed')),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: Text(context.t('confirm')),
          ),
        ],
      ),
    );
  }
}
