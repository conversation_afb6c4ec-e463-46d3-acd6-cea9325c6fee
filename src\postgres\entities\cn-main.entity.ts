import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { CnDetail } from './cn-detail.entity';

@Entity('cn_main')
export class CnMain {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '文档编号', example: 'CN123456' })
  @Column({ type: 'varchar', length: 20, unique: true })
  document_no: string;

  @ApiProperty({ description: '文档日期', example: '2025-04-04' })
  @Column({ nullable: true })
  document_date: Date;

  @ApiProperty({ description: '客户代码', example: 'CUST001' })
  @Column({ nullable: true, length: 10 })
  customer: string;

  @ApiProperty({ description: '销售员代码', example: 'S001' })
  @Column({ nullable: true, length: 10 })
  salesman: string;

  @ApiProperty({ description: '备注', example: '紧急订单' })
  @Column({ type: 'varchar', length: 4000, nullable: true })
  remarks: string;

  @ApiProperty({ description: '付款条件', example: 'CASH' })
  @Column({ nullable: true, length: 20 })
  terms: string;

  @ApiProperty({ description: '总金额', example: 1000 })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  total_amount: number;

  @ApiProperty({ description: '外币代码', example: 'USD' })
  @Column({ nullable: true, length: 10 })
  forex_code: string;

  @ApiProperty({ description: '创建时间', example: '2025-04-04T12:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @OneToMany(() => CnDetail, detail => detail.main)
  details: CnDetail[];
}
