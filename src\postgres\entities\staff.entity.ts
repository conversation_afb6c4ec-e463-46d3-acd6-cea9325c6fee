import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('staff')
export class Staff {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '用户名', example: 'john.doe' })
  @Column({ unique: true })
  username: string;

  @ApiProperty({ description: '密码哈希', example: '************' })
  @Column()
  password: string;

  @ApiProperty({ description: '员工级别', example: 'regular' })
  @Column()
  level: string; // 'regular', 'staff_bom', 'supervisor', 'admin', 'driver'

  @ApiProperty({ description: '负责楼层', example: '3F', nullable: true })
  @Column({ nullable: true })
  floor: string;

  @ApiProperty({ description: '全名', example: '<PERSON>' })
  @Column({ nullable: true })
  full_name: string;

  @ApiProperty({ description: '账户状态', example: 'active' })
  @Column({ default: 'active' })
  status: string;

  @ApiProperty({ description: '偏好语言', example: 'en' })
  @Column({ default: 'en' })
  preferred_language: string;

  @ApiProperty({ description: '最后登录时间', example: '2025-04-04T12:00:00Z' })
  @Column({ nullable: true })
  last_login: Date;

  @ApiProperty({ description: '创建时间', example: '2025-04-04T12:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;
}