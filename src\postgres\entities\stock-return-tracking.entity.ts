import { <PERSON>ti<PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Staff } from './staff.entity';

@Entity('stock_return_tracking')
export class StockReturnTracking {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '单据编号', example: 'D00001' })
  @Column()
  document_no: string;

  @ApiProperty({ description: '行号', example: 1 })
  @Column()
  line: number;

  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @Column()
  stock_code: string;

  @ApiProperty({ description: '货架位置', example: '2-1-G001-1' })
  @Column()
  bin_shelf_no: string;

  @ApiProperty({ description: '数量', example: 10 })
  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @ApiProperty({ description: '触发原因', example: 'order_refresh' })
  @Column()
  trigger_reason: string;

  @ApiProperty({ description: '触发者ID', example: 2 })
  @Column()
  triggered_by: number;

  @ApiProperty({ description: '负责归还的员工ID', example: 1 })
  @Column()
  assigned_to: number;

  @ApiProperty({ description: '是否已完成归还', example: false })
  @Column({ default: false })
  is_completed: boolean;

  @ApiProperty({ description: '完成归还的员工ID', example: 1, nullable: true })
  @Column({ nullable: true })
  completed_by: number;

  @ApiProperty({ description: '完成时间', example: '2025-01-15T10:30:00Z', nullable: true })
  @Column({ nullable: true })
  completed_at: Date;

  @ApiProperty({ description: '备注', example: '已归还到原位置', nullable: true })
  @Column({ nullable: true })
  notes: string;

  @ApiProperty({ description: '创建时间', example: '2025-01-15T10:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @ManyToOne(() => Staff)
  @JoinColumn({ name: 'triggered_by' })
  trigger_staff: Staff;

  @ManyToOne(() => Staff)
  @JoinColumn({ name: 'assigned_to' })
  assigned_staff: Staff;

  @ManyToOne(() => Staff)
  @JoinColumn({ name: 'completed_by' })
  completed_staff: Staff;
}
