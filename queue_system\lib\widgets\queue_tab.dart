import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/widgets/check_item_card.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';
import 'package:queue_system/utils/search_utils.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/services/check_service.dart';

class QueueTab extends StatefulWidget {
  final List<CheckItem> items;
  final int staffId;

  // 确认操作回调函数
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;

  const QueueTab({
    super.key,
    required this.items,
    required this.staffId,
    required this.onConfirmAction,
  });

  @override
  State<QueueTab> createState() => _QueueTabState();
}

class _QueueTabState extends State<QueueTab> {
  Timer? _searchDebounceTimer;

  @override
  void dispose() {
    // 清理防抖定时器
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 调试日志：记录搜索查询变化
    print('QueueTab._onSearchChanged: "$query"');

    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 获取当前的搜索状态，避免重复搜索
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);

    // 如果查询没有真正改变，不执行搜索
    if (query == checkListProvider.searchQuery) {
      print('QueueTab._onSearchChanged: 查询未改变，跳过搜索');
      return;
    }

    // print('QueueTab._onSearchChanged: 启动防抖定时器');
    // 设置新的防抖定时器
    _searchDebounceTimer = Timer(const Duration(seconds: 1), () {
      print('QueueTab._onSearchChanged: 执行搜索 "$query"');
      // 1秒后执行搜索
      checkListProvider.searchCheckList(query);
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    // 立即清除搜索
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    checkListProvider.clearSearch();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CheckListProvider>(
      builder: (context, checkListProvider, child) {
        return Column(
          children: [
            // 搜索栏
            SearchBarWidget(
              hintText: context.t('search_documents'),
              onSearchChanged: _onSearchChanged,
              onClear: _onSearchCleared,
              initialValue: checkListProvider.searchQuery,
            ),
            // 队列列表
            Expanded(
              child: QueueListWidget(
                items: widget.items,
                staffId: widget.staffId,
                searchQuery: checkListProvider.searchQuery,
                searchDebounceTimer: _searchDebounceTimer,
                onConfirmAction: widget.onConfirmAction,
              ),
            ),
          ],
        );
      },
    );
  }
}

// 独立的队列列表Widget
class QueueListWidget extends StatefulWidget {
  final List<CheckItem> items;
  final int staffId;
  final String searchQuery;
  final Timer? searchDebounceTimer;
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;

  const QueueListWidget({
    super.key,
    required this.items,
    required this.staffId,
    required this.searchQuery,
    required this.searchDebounceTimer,
    required this.onConfirmAction,
  });

  @override
  State<QueueListWidget> createState() => _QueueListWidgetState();
}

class _QueueListWidgetState extends State<QueueListWidget> {
  // 搜索相关状态
  List<CheckItem> _filteredItems = [];
  Timer? _searchDebounceTimer;

  // 简化的数据状态
  List<CheckItem> _allItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _updateFilteredItems();

    // 简化的数据同步
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _syncWithProvider();
    });
  }

  // 同步Provider数据到本地状态
  void _syncWithProvider() {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);

    setState(() {
      _allItems = [
        ...checkListProvider.pendingRegularCheck,
        ...checkListProvider.pendingBomCheck,
        ...checkListProvider.pendingSupervisorCheck,
        ...checkListProvider.waitingList,
      ];
      _isLoading = checkListProvider.isLoading;
    });
  }

  @override
  void didUpdateWidget(QueueListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当items改变时，更新过滤列表
    if (widget.items != oldWidget.items ||
        widget.searchQuery != oldWidget.searchQuery) {
      _updateFilteredItems();
    }
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 更新过滤后的项目列表
  void _updateFilteredItems() {
    setState(() {
      _filteredItems =
          SearchUtils.searchCheckItems(widget.items, widget.searchQuery);
    });
  }

  // 刷新数据方法
  Future<void> _refreshData() async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // 使用简化的刷新方式
    await checkListProvider.loadCheckListPaginated(
      widget.staffId,
      currentStaff: authProvider.currentStaff,
      isRefresh: true,
      searchQuery: widget.searchQuery.isNotEmpty ? widget.searchQuery : null,
    );
  }

  // 显示重新同步订单对话框
  void _showRefreshOrderDialog(BuildContext context, String documentNo) {
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.t('refresh_order')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${context.t('document_no')}: $documentNo'),
              const SizedBox(height: 16),
              const Icon(
                Icons.warning,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                context.t('refresh_order_warning'),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('• ${context.t('refresh_order_warning_1')}'),
              Text('• ${context.t('refresh_order_warning_2')}'),
              Text('• ${context.t('refresh_order_warning_3')}'),
              const SizedBox(height: 16),
              Text(
                '${context.t('reason')} (${context.t('optional')}):',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: reasonController,
                decoration: InputDecoration(
                  hintText: context.t('refresh_order_reason_hint'),
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                maxLines: 2,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.t('cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performRefreshOrder(documentNo, reasonController.text.trim());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: Text(context.t('confirm_refresh')),
            ),
          ],
        );
      },
    );
  }

  // 显示设置紧急状态对话框
  void _showUrgentStatusDialog(
      BuildContext context, String documentNo, int currentPriority) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.t('set_urgent_status')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${context.t('document_no')}: $documentNo'),
              const SizedBox(height: 16),
              Text(
                  '${context.t('current_priority')}: ${currentPriority == 0 ? context.t('priority_normal') : context.t('priority_urgent')}'),
              const SizedBox(height: 16),
              Text('${context.t('select_new_priority')}:'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.t('cancel')),
            ),
            if (currentPriority > 0)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _performSetUrgentStatus(documentNo, 0);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                ),
                child: Text(context.t('set_to_normal')),
              ),
            if (currentPriority == 0)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _performSetUrgentStatus(documentNo, 1);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(context.t('set_to_urgent')),
              ),
          ],
        );
      },
    );
  }

  // 执行设置紧急状态操作
  Future<void> _performSetUrgentStatus(String documentNo, int priority) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final checkService = CheckService(baseUrl: AppConfig.baseUrl);

      await checkService.setUrgentStatus(
        documentNo,
        authProvider.currentStaff!.id,
        priority: priority,
      );

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(priority == 0
                ? context.t('set_to_normal_priority')
                : context.t('set_to_urgent_priority')),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // 刷新数据
      await _refreshData();
    } catch (e) {
      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('failed_to_set_priority')}: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 执行重新同步订单操作
  Future<void> _performRefreshOrder(String documentNo, String reason) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(context.t('refreshing_order')),
              ],
            ),
          );
        },
      );

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final checkListProvider =
          Provider.of<CheckListProvider>(context, listen: false);

      // 调用重新同步 API
      final result = await checkListProvider.refreshOrder(
        documentNo,
        authProvider.currentStaff!.id,
        reason,
      );

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${context.t('order_refreshed_successfully')}\n'
              '${context.t('deleted')}: ${result['statistics']['deletedCount']}\n'
              '${context.t('inserted')}: ${result['statistics']['insertedCount']}\n'
              '${context.t('notifications_sent')}: ${result['statistics']['notificationsSent']}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
      }

      // 刷新数据
      await _refreshData();
    } catch (e) {
      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('refresh_order_failed')}: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用简化的内容构建
    return _buildContent(context);
  }

  // 构建简化的内容
  Widget _buildContent(BuildContext context) {
    if (_filteredItems.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.hourglass_empty, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(context.t('no_pending_items')),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshData,
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 显示搜索结果统计
        if (widget.searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '${context.t('search_results')}: ${_filteredItems.length}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        // 项目列表
        Expanded(child: _buildItemsList()),
      ],
    );
  }

  Widget _buildItemsList() {
    // 使用过滤后的项目列表
    var itemsToShow = _filteredItems.isNotEmpty || widget.searchQuery.isNotEmpty
        ? _filteredItems
        : widget.items;

    // 检查当前用户权限
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isBomSpecialist = authProvider.currentStaff?.level == 'staff_bom';
    final isSupervisor = authProvider.currentStaff?.level == 'supervisor' ||
        authProvider.currentStaff?.level == 'admin';

    print('===== 用户权限调试 =====');
    print('当前用户: ${authProvider.currentStaff?.fullName}');
    print('用户级别: ${authProvider.currentStaff?.level}');
    print('是否为BOM专员: $isBomSpecialist');
    print('是否为主管: $isSupervisor');

    // 简单的数据过滤：现在后端已经正确返回BOM父项和非BOM项目
    if (isBomSpecialist || isSupervisor) {
      print('===== 用户数据过滤调试 =====');
      print('用户类型: ${isBomSpecialist ? "BOM专员" : "主管"}');
      print('原始数据数量: ${itemsToShow.length}');

      for (var item in itemsToShow) {
        print(
            '项目: ${item.documentNo}, isBomParent: ${item.isBomParent}, parentCode: ${item.parentCode}');
      }

      // 后端已经过滤了数据，前端不需要额外过滤
      // 只需要显示后端返回的所有数据
      print('显示数据数量: ${itemsToShow.length}');
    }

    // 如果搜索后没有结果，显示无结果提示
    if (widget.searchQuery.isNotEmpty && _filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              context.t('no_search_results'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              '${context.t('search_keyword')}: "${widget.searchQuery}"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    // 首先按优先级降序排序，然后按创建时间升序排序（紧急订单优先，然后越早的排越前面）
    final sortedItems = List<CheckItem>.from(itemsToShow)
      ..sort((a, b) {
        // 先按优先级降序排序（数值越大优先级越高）
        final priorityComparison = b.priority.compareTo(a.priority);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        // 优先级相同时，按创建时间升序排序
        return a.createdAt.compareTo(b.createdAt);
      });

    // 按document_no和customer分组，而不是按created_at分组
    final documentCustomerGroups = <String, List<CheckItem>>{};
    for (final item in sortedItems) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentCustomerGroups.containsKey(groupKey)) {
        documentCustomerGroups[groupKey] = [];
      }
      documentCustomerGroups[groupKey]!.add(item);
    }

    // 将分组转换为列表并按优先级和创建时间排序（紧急订单优先，然后较早的在前）
    final sortedGroups = documentCustomerGroups.entries.toList()
      ..sort((a, b) {
        // 使用每组中第一个项目的优先级和创建时间进行排序
        final aPriority = a.value.first.priority;
        final bPriority = b.value.first.priority;
        final aTime = a.value.first.createdAt;
        final bTime = b.value.first.createdAt;

        // 先按优先级降序排序
        final priorityComparison = bPriority.compareTo(aPriority);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        // 优先级相同时，按创建时间升序排序
        return aTime.compareTo(bTime);
      });

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: ListView.builder(
        itemCount: sortedGroups.length,
        itemBuilder: (context, index) {
          final groupEntry = sortedGroups[index];
          final groupItems = groupEntry.value;

          // 获取该组的第一个项目的创建时间，用于显示
          final createdAt = _formatDate(groupItems.first.createdAt,
              format: 'yyyy-MM-dd HH:mm');

          // 直接构建文档组，不再需要中间的客户分组
          return _buildDocumentGroup(context, groupItems, createdAt);
        },
      ),
    );
  }

  // 构建文档组
  Widget _buildDocumentGroup(
    BuildContext context,
    List<CheckItem> items,
    String createdAtStr,
  ) {
    // 获取文档信息
    final firstItem = items.first;
    final documentNo = firstItem.documentNo.contains('/')
        ? firstItem.documentNo.split('/').first
        : firstItem.documentNo;
    final documentDate = firstItem.documentDate;
    final customerCode = firstItem.customer;
    final customerName = firstItem.customerName;

    // 检查当前用户权限
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isSupervisor = authProvider.currentStaff?.level == 'supervisor' ||
        authProvider.currentStaff?.level == 'admin';
    final isAdmin = authProvider.currentStaff?.level == 'admin';
    final currentPriority = firstItem.priority;

    // 检查是否为紧急订单
    final isUrgent = items.any((item) => item.priority > 0);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isUrgent ? Colors.red.shade50 : null,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                border: Border(left: BorderSide(color: Colors.red, width: 4)),
                borderRadius: BorderRadius.circular(4),
              )
            : null,
        child: ExpansionTile(
          title: LayoutBuilder(
            builder: (context, constraints) {
              // 获取屏幕宽度
              final screenWidth = MediaQuery.of(context).size.width;
              // 根据屏幕宽度调整布局
              final isSmallScreen = screenWidth < 360;

              return isSmallScreen
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  // 紧急标识
                                  if (currentPriority > 0)
                                    Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        context.t('urgent_label'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  Expanded(
                                    child: Text(
                                      '$customerCode - $customerName',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // 为 admin 用户添加紧急排号按钮
                            if (isAdmin)
                              IconButton(
                                icon: Icon(
                                  currentPriority > 0
                                      ? Icons.priority_high
                                      : Icons.low_priority,
                                  color: currentPriority > 0
                                      ? Colors.red
                                      : Colors.grey,
                                  size: 20,
                                ),
                                onPressed: () => _showUrgentStatusDialog(
                                    context, documentNo, currentPriority),
                                tooltip: currentPriority > 0
                                    ? context.t('cancel_urgent')
                                    : context.t('set_to_urgent'),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(
                                  minWidth: 24,
                                  minHeight: 24,
                                ),
                              ),
                            // 为 admin 用户添加重新同步按钮
                            if (isAdmin)
                              IconButton(
                                icon: const Icon(Icons.refresh,
                                    color: Colors.orange, size: 20),
                                onPressed: () => _showRefreshOrderDialog(
                                    context, documentNo),
                                tooltip: context.t('refresh_order'),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(
                                  minWidth: 24,
                                  minHeight: 24,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${context.t('document_no')}: $documentNo',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500, fontSize: 13),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Created: $createdAtStr',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.blue.shade800,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              // 紧急标识
                              if (currentPriority > 0)
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    context.t('urgent_label'),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '$customerCode - $customerName',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    Text(
                                      '${context.t('document_no')}: $documentNo',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.w500),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // 为 admin 用户添加紧急排号按钮
                        if (isAdmin)
                          IconButton(
                            icon: Icon(
                              currentPriority > 0
                                  ? Icons.priority_high
                                  : Icons.low_priority,
                              color: currentPriority > 0
                                  ? Colors.red
                                  : Colors.grey,
                              size: 24,
                            ),
                            onPressed: () => _showUrgentStatusDialog(
                                context, documentNo, currentPriority),
                            tooltip: currentPriority > 0
                                ? context.t('cancel_urgent')
                                : context.t('set_to_urgent'),
                          ),
                        // 为 admin 用户添加重新同步按钮
                        if (isAdmin)
                          IconButton(
                            icon: const Icon(Icons.refresh,
                                color: Colors.orange, size: 24),
                            onPressed: () =>
                                _showRefreshOrderDialog(context, documentNo),
                            tooltip: context.t('refresh_order'),
                          ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Created: $createdAtStr',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue.shade800,
                            ),
                          ),
                        ),
                      ],
                    );
            },
          ),
          subtitle: Text(
              'Document Date: ${_formatDate(documentDate, format: 'dd/MM/yyyy')} | ${items.length} ${items.length == 1 ? 'item' : context.t('items')}'),
          // 文档组默认不展开
          initiallyExpanded: false,
          children: _buildItemsWithBomHierarchy(context, items, isSupervisor),
        ),
      ),
    );
  }

  // 构建带有BOM层级结构的项目列表
  List<Widget> _buildItemsWithBomHierarchy(
    BuildContext context,
    List<CheckItem> items,
    bool isSupervisor,
  ) {
    // 简化为原有的显示方式，层级功能在CheckItemCard内部实现
    return items
        .map((item) => Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final isBomSpecialist =
                    authProvider.currentStaff?.level == 'staff_bom';
                return CheckItemCard(
                  item: item,
                  staffId: widget.staffId,
                  isSenior: isSupervisor,
                  isStaffCheck: false,
                  isIntegratedMode: true,
                  isBomSpecialist: isBomSpecialist,
                  onConfirmAction: widget.onConfirmAction,
                );
              },
            ))
        .toList();
  }

  // 格式化日期为指定格式
  String _formatDate(DateTime dateTime, {String format = 'yyyy-MM-dd'}) {
    if (format == 'yyyy-MM-dd') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    } else if (format == 'yyyy-MM-dd HH:mm') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (format == 'dd/MM/yyyy') {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } else if (format == 'HH:mm dd/MM/yyyy') {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')} ${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } else {
      return dateTime.toString();
    }
  }
}
