<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{title}}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }

    .header h1 {
      margin: 0;
      color: #2c3e50;
    }

    .time {
      font-size: 14px;
      color: #7f8c8d;
    }

    /* Queue Display Styles */
    .queue-display {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 30px;
      overflow: hidden;
    }

    .queue-header {
      background-color: #2c3e50;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .queue-header h2 {
      margin: 0;
      font-size: 24px;
    }

    .queue-counts {
      display: flex;
      gap: 15px;
    }

    .queue-counts span {
      background-color: rgba(255, 255, 255, 0.2);
      padding: 4px 10px;
      border-radius: 4px;
      font-size: 14px;
    }

    .queue-content {
      display: flex;
      padding: 0;
    }

    .queue-column {
      flex: 1;
      padding: 20px;
    }

    .queue-column:first-child {
      border-right: 1px solid #eee;
    }

    .queue-column h3 {
      margin-top: 0;
      color: #2c3e50;
      font-size: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
    }

    .queue-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .queue-item {
      padding: 12px 15px;
      border-bottom: 1px solid #eee;
      font-size: 18px;
      font-weight: bold;
    }

    .queue-item:last-child {
      border-bottom: none;
    }

    .pending {
      color: #e74c3c;
    }

    .to-ship {
      color: #27ae60;
    }

    .more-items {
      font-style: italic;
      font-size: 14px;
      color: #7f8c8d;
      font-weight: normal;
      text-align: center;
      padding-top: 8px;
      border-top: 1px dashed #ddd;
    }

    /* Original Status Styles */
    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .status-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      transition: transform 0.2s;
    }

    .status-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .status-card h2 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #2c3e50;
      font-size: 18px;
      border-bottom: 2px solid #3498db;
      padding-bottom: 8px;
    }

    .status-value {
      font-size: 32px;
      font-weight: bold;
      color: #3498db;
      margin: 10px 0;
    }

    .status-label {
      font-size: 14px;
      color: #7f8c8d;
    }

    .floor-status {
      background-color: #ecf0f1;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
    }

    .floor-status h2 {
      margin-top: 0;
      color: #2c3e50;
    }

    .recent-activity {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 30px;
    }

    .recent-activity h2 {
      margin-top: 0;
      color: #2c3e50;
      border-bottom: 2px solid #3498db;
      padding-bottom: 8px;
    }

    .activity-list {
      list-style: none;
      padding: 0;
    }

    .activity-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-time {
      font-size: 12px;
      color: #7f8c8d;
    }

    .progress-bar {
      height: 10px;
      background-color: #ecf0f1;
      border-radius: 5px;
      margin-top: 10px;
      overflow: hidden;
    }

    .progress-value {
      height: 100%;
      background-color: #3498db;
      border-radius: 5px;
    }

    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      color: #7f8c8d;
      font-size: 14px;
    }

    .system-status-section {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 2px solid #ddd;
    }

    .system-status-section h2 {
      color: #2c3e50;
      font-size: 24px;
      margin-bottom: 20px;
    }

    @media (max-width: 768px) {
      .status-grid {
        grid-template-columns: 1fr;
      }

      .queue-content {
        flex-direction: column;
      }

      .queue-column:first-child {
        border-right: none;
        border-bottom: 1px solid #eee;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>{{title}}</h1>
      <div class="time">
        <span>Current Time: {{currentTime}}</span>
      </div>
    </div>

    <!-- Queue Display (McDonald's Style) -->
    <div class="queue-display">
      <div class="queue-header">
        <h2>Order Status Board</h2>
        <div class="queue-counts">
          <span>Pending: {{orderQueue.pendingCount}}</span>
          <span>To Ship: {{orderQueue.toShipCount}}</span>
        </div>
      </div>
      <div class="queue-content">
        <div class="queue-column">
          <h3>Pending</h3>
          <ul class="queue-list">
            {{#each orderQueue.pending}}
            <li class="queue-item pending">{{this.documentNo}}</li>
            {{else}}
            <li class="queue-item">No pending orders</li>
            {{/each}}
            {{#if orderQueue.hasPendingMore}}
            <li class="queue-item more-items">More orders not displayed...</li>
            {{/if}}
          </ul>
        </div>
        <div class="queue-column">
          <h3>To Ship</h3>
          <ul class="queue-list">
            {{#each orderQueue.toShip}}
            <li class="queue-item to-ship">{{this.documentNo}}</li>
            {{else}}
            <li class="queue-item">No orders ready to ship</li>
            {{/each}}
            {{#if orderQueue.hasToShipMore}}
            <li class="queue-item more-items">More orders not displayed...</li>
            {{/if}}
          </ul>
        </div>
      </div>
    </div>

    <!-- Original System Status Section -->
    <div class="system-status-section">
      <h2>Detailed System Status</h2>

      <div class="status-grid">
        <div class="status-card">
          <h2>Pending Staff Check</h2>
          <div class="status-value">{{systemStatus.counts.pendingStaffCheck}}</div>
          <div class="status-label">Items to process</div>
        </div>

        <div class="status-card">
          <h2>Pending BOM Check</h2>
          <div class="status-value">{{systemStatus.counts.pendingBomCheck}}</div>
          <div class="status-label">Items to process</div>
        </div>

        <div class="status-card">
          <h2>Pending Supervisor Check</h2>
          <div class="status-value">{{systemStatus.counts.pendingSupervisorCheck}}</div>
          <div class="status-label">Items to process</div>
        </div>

        <div class="status-card">
          <h2>Ready to Ship</h2>
          <div class="status-value">{{systemStatus.counts.readyToShip}}</div>
          <div class="status-label">Order count</div>
        </div>

        <div class="status-card">
          <h2>In Delivery</h2>
          <div class="status-value">{{systemStatus.counts.inDelivery}}</div>
          <div class="status-label">Order count</div>
        </div>

        <div class="status-card">
          <h2>Today's Statistics</h2>
          <div style="display: flex; justify-content: space-between;">
            <div>
              <div class="status-value" style="font-size: 24px;">{{systemStatus.today.checked}}</div>
              <div class="status-label">Checked</div>
            </div>
            <div>
              <div class="status-value" style="font-size: 24px;">{{systemStatus.today.shipped}}</div>
              <div class="status-label">Shipped</div>
            </div>
            <div>
              <div class="status-value" style="font-size: 24px;">{{systemStatus.today.delivered}}</div>
              <div class="status-label">Delivered</div>
            </div>
          </div>
        </div>
      </div>

      {{#if floorStatus}}
      <div class="floor-status">
        <h2>Floor {{floorStatus.floor}} Status</h2>
        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
          <div>
            <div class="status-value">{{floorStatus.counts.pendingStaffCheck}}</div>
            <div class="status-label">Pending Staff Check</div>
          </div>
          <div>
            <div class="status-value">{{floorStatus.counts.pendingBomCheck}}</div>
            <div class="status-label">Pending BOM Check</div>
          </div>
          <div>
            <div class="status-value">{{floorStatus.counts.pendingSupervisorCheck}}</div>
            <div class="status-label">Pending Supervisor Check</div>
          </div>
          <div>
            <div class="status-value">{{floorStatus.counts.total}}</div>
            <div class="status-label">Total Pending</div>
          </div>
        </div>
      </div>
      {{/if}}

      <div class="recent-activity">
        <h2>Recent Check Activities</h2>
        <ul class="activity-list">
          {{#each systemStatus.recent.checks}}
          <li class="activity-item">
            <div><strong>{{this.documentNo}}</strong> - {{this.stockCode}}: {{this.description}}</div>
            <div>Customer: {{this.customer}}</div>
            <div class="activity-time">Checked at: {{this.checkedAt}}</div>
          </li>
          {{/each}}
        </ul>
      </div>

      <div class="recent-activity">
        <h2>Recent Shipping Activities</h2>
        <ul class="activity-list">
          {{#each systemStatus.recent.shipments}}
          <li class="activity-item">
            <div><strong>{{this.documentNo}}</strong> - {{this.customer}}</div>
            <div class="activity-time">Shipped at: {{this.shippedAt}}</div>
          </li>
          {{/each}}
        </ul>
      </div>

      <div class="footer">
        <p>System Status Page - Auto refresh every {{refreshInterval}} seconds</p>
      </div>
    </div>

    <script>
      // Auto refresh page
      setTimeout(function () {
        location.reload();
      }, {{ refreshInterval }} * 1000);
    </script>
</body>

</html>