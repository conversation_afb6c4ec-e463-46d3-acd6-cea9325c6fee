import { Controller, Get, Post, Param, Body, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { NotificationService } from './notification.service';
import { Notification } from '../postgres/entities/notification.entity';

@ApiTags('Notification')
@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @ApiOperation({ summary: '获取用户未读通知' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiResponse({
    status: 200,
    description: '返回用户未读通知列表',
    type: [Notification]
  })
  @Get('unread/:userId')
  async getUnreadNotifications(@Param('userId') userId: number) {
    return this.notificationService.getUnreadNotifications(userId);
  }

  @ApiOperation({ summary: '获取用户所有通知' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiResponse({
    status: 200,
    description: '返回用户所有通知列表',
    type: [Notification]
  })
  @Get('all/:userId')
  async getAllNotifications(@Param('userId') userId: number) {
    return this.notificationService.getAllNotifications(userId);
  }

  @ApiOperation({ summary: '将通知标记为已读' })
  @ApiParam({ name: 'id', description: '通知ID' })
  @ApiResponse({
    status: 200,
    description: '标记成功',
    type: Notification
  })
  @Post('read/:id')
  async markAsRead(@Param('id') id: number) {
    const notification = await this.notificationService.markAsRead(id);
    if (!notification) {
      throw new NotFoundException('Notification not found');
    }
    return notification;
  }

  @ApiOperation({ summary: '将用户所有通知标记为已读' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiResponse({
    status: 200,
    description: '全部标记成功'
  })
  @Post('read-all/:userId')
  async markAllAsRead(@Param('userId') userId: number) {
    await this.notificationService.markAllAsRead(userId);
    return { success: true, message: 'All notifications marked as read' };
  }
} 