import '../lib/l10n/app_zh.dart';
import '../lib/l10n/app_en.dart';
import '../lib/l10n/app_ms.dart';

/// 验证搜索功能的本地化文本是否正确添加
void main() {
  print('🔍 验证搜索功能本地化文本...\n');

  // 验证中文本地化
  print('📝 中文 (zh) 本地化:');
  final zhValues = AppLocalizationsZh.values;
  _verifyLocalization('zh', zhValues, {
    'search_documents': '搜索单据、客户、库存...',
    'search_results': '搜索结果',
    'no_search_results': '未找到匹配的结果',
    'clear_search': '清除搜索',
    'search_keyword': '搜索关键词',
  });

  print('\n📝 英文 (en) 本地化:');
  final enValues = AppLocalizationsEn.values;
  _verifyLocalization('en', enValues, {
    'search_documents': 'Search documents, customers, stock...',
    'search_results': 'Search Results',
    'no_search_results': 'No matching results found',
    'clear_search': 'Clear Search',
    'search_keyword': 'Search Keyword',
  });

  print('\n📝 马来文 (ms) 本地化:');
  final msValues = AppLocalizationsMs.values;
  _verifyLocalization('ms', msValues, {
    'search_documents': 'Cari dokumen, pelanggan, stok...',
    'search_results': 'Hasil Carian',
    'no_search_results': 'Tiada hasil yang sepadan ditemui',
    'clear_search': 'Kosongkan Carian',
    'search_keyword': 'Kata Kunci Carian',
  });

  print('\n🎯 验证搜索关键词格式化:');
  const testQuery = 'D00001';
  print('  中文: ${zhValues['search_keyword']}: "$testQuery"');
  print('  英文: ${enValues['search_keyword']}: "$testQuery"');
  print('  马来文: ${msValues['search_keyword']}: "$testQuery"');

  print('\n✅ 所有搜索功能本地化文本验证完成！');
}

void _verifyLocalization(String language, Map<String, String> values, Map<String, String> expected) {
  bool allPassed = true;
  
  for (final entry in expected.entries) {
    final key = entry.key;
    final expectedValue = entry.value;
    final actualValue = values[key];
    
    if (actualValue == expectedValue) {
      print('  ✅ $key: "$actualValue"');
    } else {
      print('  ❌ $key: 期望 "$expectedValue", 实际 "$actualValue"');
      allPassed = false;
    }
  }
  
  if (allPassed) {
    print('  🎉 $language 本地化验证通过！');
  } else {
    print('  ⚠️  $language 本地化存在问题！');
  }
}
