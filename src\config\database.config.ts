import { registerAs } from '@nestjs/config';

export default registerAs('database', () => ({
  sqlServer: {
    host: process.env.SQL_SERVER_HOST || '127.0.0.1',
    port: parseInt(process.env.SQL_SERVER_PORT || '1433', 10),
    username: process.env.SQL_SERVER_USERNAME || 'sa',
    password: process.env.SQL_SERVER_PASSWORD || '',
    database: process.env.SQL_SERVER_DATABASE || 'master',
    logging: true,
    options: {
      encrypt: false,
      trustServerCertificate: true,
      connectionTimeout: 30000,
    },
  },
  postgres: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    username: process.env.POSTGRES_USERNAME || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DATABASE || 'postgres',
    synchronize: process.env.NODE_ENV !== 'production',
  },
})); 