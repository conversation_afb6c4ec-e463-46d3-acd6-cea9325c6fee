import 'dart:async';
import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/shipping_document.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';

class DeliveryTab extends StatefulWidget {
  final int staffId;
  final bool isDriver;

  const DeliveryTab({
    super.key,
    required this.staffId,
    this.isDriver = false,
  });

  @override
  State<DeliveryTab> createState() => _DeliveryTabState();
}

class _DeliveryTabState extends State<DeliveryTab> {
  // 搜索相关状态
  String _searchQuery = '';
  Timer? _searchDebounceTimer;

  @override
  void dispose() {
    // 清理防抖定时器
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 立即更新搜索查询状态（用于UI显示）
    setState(() {
      _searchQuery = query;
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    setState(() {
      _searchQuery = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 搜索栏
        SearchBarWidget(
          hintText: context.t('search_documents'),
          onSearchChanged: _onSearchChanged,
          onClear: _onSearchCleared,
          initialValue: _searchQuery,
        ),
        // 文档列表
        Expanded(
          child: DeliveryListWidget(
            staffId: widget.staffId,
            isDriver: widget.isDriver,
            searchQuery: _searchQuery,
            searchDebounceTimer: _searchDebounceTimer,
          ),
        ),
      ],
    );
  }
}

// 独立的送货列表Widget
class DeliveryListWidget extends StatefulWidget {
  final int staffId;
  final bool isDriver;
  final String searchQuery;
  final Timer? searchDebounceTimer;

  const DeliveryListWidget({
    super.key,
    required this.staffId,
    required this.isDriver,
    required this.searchQuery,
    required this.searchDebounceTimer,
  });

  @override
  State<DeliveryListWidget> createState() => _DeliveryListWidgetState();
}

class _DeliveryListWidgetState extends State<DeliveryListWidget> {
  final _checkService = CheckService(baseUrl: AppConfig.baseUrl);
  List<ShippingDocument> _shipments = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMore = true;
  static const int _pageSize = 10;
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    _loadShipments(isRefresh: true);
  }

  @override
  void didUpdateWidget(DeliveryListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当搜索查询改变时，执行防抖搜索
    if (widget.searchQuery != oldWidget.searchQuery) {
      _handleSearchQueryChange();
    }
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 处理搜索查询变化
  void _handleSearchQueryChange() {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 设置新的防抖定时器
    _searchDebounceTimer = Timer(const Duration(seconds: 1), () {
      // 1秒后执行搜索
      _performSearch();
    });
  }

  // 执行实际的搜索操作
  void _performSearch() {
    setState(() {
      // 重置分页状态并重新加载数据
      _currentPage = 1;
      _hasMore = true;
      _shipments.clear();
    });

    // 执行服务器端搜索
    _loadShipments(isRefresh: true);
  }

  Future<void> _loadShipments({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _currentPage = 1;
        _hasMore = true;
        _shipments.clear();
        _isLoading = true;
        _error = null;
      });
    }

    try {
      final result = await _checkService.getSupervisorShipments(
        widget.staffId,
        page: _currentPage,
        limit: _pageSize,
        searchQuery: widget.searchQuery.trim().isNotEmpty
            ? widget.searchQuery.trim()
            : null,
      );

      final newShipments = result['data'] as List<ShippingDocument>;
      final hasMore = result['hasMore'] as bool;

      // 过滤掉未指派司机的送货单据
      final filteredShipments = newShipments
          .where((doc) =>
              doc.driverId != null && doc.driverId.toString().isNotEmpty)
          .toList();

      setState(() {
        if (isRefresh) {
          _shipments = filteredShipments;
        } else {
          _shipments.addAll(filteredShipments);
        }
        _hasMore = hasMore;
        _isLoading = false;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
      rethrow;
    }
  }

  Future<void> _loadMoreShipments() async {
    if (_isLoadingMore || !_hasMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    // 先保存当前页码，以便出错时回退
    final originalPage = _currentPage;
    _currentPage++;

    try {
      await _loadShipments();
    } catch (e) {
      // 如果加载失败，回退页码并重置状态
      setState(() {
        _currentPage = originalPage;
        _isLoadingMore = false;
        _error = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(context.t('loading_failed')),
            const SizedBox(height: 8),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadShipments(isRefresh: true),
              child: Text(context.t('retry')),
            ),
          ],
        ),
      );
    }

    if (_shipments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.searchQuery.isNotEmpty
                  ? Icons.search_off
                  : Icons.local_shipping_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              widget.searchQuery.isNotEmpty
                  ? context.t('no_search_results')
                  : context.t('no_shipments'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            if (widget.searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${context.t('search_keyword')}: "${widget.searchQuery}"',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadShipments(isRefresh: true),
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 显示搜索结果统计
        if (widget.searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '${context.t('search_results')}: ${_shipments.length} 条结果',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        // 文档列表
        Expanded(child: _buildShipmentsList()),
      ],
    );
  }

  Widget _buildShipmentsList() {
    return RefreshIndicator(
      onRefresh: () => _loadShipments(isRefresh: true),
      child: ListView.builder(
        itemCount: _shipments.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          // 如果是最后一项且有更多数据，显示Load More按钮
          if (index == _shipments.length && _hasMore) {
            return Container(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: _isLoadingMore
                    ? const CircularProgressIndicator()
                    : ElevatedButton(
                        onPressed: _loadMoreShipments,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                        ),
                        child: Text(context.t('load_more')),
                      ),
              ),
            );
          }

          final document = _shipments[index];
          return _buildShipmentCard(document);
        },
      ),
    );
  }

  Widget _buildShipmentCard(ShippingDocument document) {
    // 司机信息
    final String driverInfo = document.driverUsername != null
        ? '${context.t('driver')}: ${document.driverUsername}'
        : context.t('no_driver_assigned');

    // 送达状态
    final bool isDelivered = document.delivered ?? false;

    // 格式化创建时间
    final createdAt = _formatDate(document.createdAt);

    // 检查是否为紧急订单
    final isUrgent = document.priority > 0;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isUrgent ? Colors.red.shade50 : null,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                border: Border(left: BorderSide(color: Colors.red, width: 4)),
                borderRadius: BorderRadius.circular(4),
              )
            : null,
        child: ExpansionTile(
          title: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${document.customerCode} - ${document.customerName}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${context.t('document_no')}: ${document.documentNo}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Created: $createdAt',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade800,
                  ),
                ),
              ),
            ],
          ),
          subtitle: Row(
            children: [
              Expanded(
                child: Text(
                    'Document Date: ${_formatDate(document.documentDate)}'),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isDelivered ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  isDelivered
                      ? context.t('delivered')
                      : context.t('in_transit'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          // 文档组默认不展开
          initiallyExpanded: false,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(driverInfo),
                  const SizedBox(height: 8),
                  if (isDelivered && document.deliveryNotes != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                          '${context.t('delivery_notes')}: ${document.deliveryNotes}'),
                    ),
                  if (isDelivered && document.deliveredAt != null)
                    Text(
                        '${context.t('delivered_at')}: ${_formatDateTime(document.deliveredAt)}'),

                  // 只有司机才能看到并使用Reject按钮
                  if (widget.isDriver &&
                      !isDelivered &&
                      document.driverId == widget.staffId)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 4),
                              minimumSize: const Size(60, 30),
                            ),
                            onPressed: () => _showRejectDialog(document),
                            child: Text(context.t('reject')),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 格式化日期
  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  // 格式化日期时间
  String _formatDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return '';

    try {
      final dateTime = DateTime.parse(dateTimeStr);
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
          '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTimeStr;
    }
  }

  // 显示拒绝送达对话框
  Future<void> _showRejectDialog(ShippingDocument document) async {
    final TextEditingController reasonController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('reject_delivery')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${document.documentNo}'),
            const SizedBox(height: 16),
            Text(context.t('rejection_reason')),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: context.t('rejection_reason_hint'),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              context.t('confirm_rejection'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 使用try-catch包装API调用
        Map<String, dynamic>? response;
        try {
          response = await _checkService.rejectDelivery(
              document.documentNo, widget.staffId, reasonController.text);

          // API调用成功，直接从列表中移除该文档
          if (mounted) {
            setState(() {
              _shipments
                  .removeWhere((doc) => doc.documentNo == document.documentNo);
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text(response['message'] ?? context.t('delivery_rejected')),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } catch (apiError) {
          // 检查错误消息是否表明操作实际上是成功的
          String errorMsg = apiError.toString().toLowerCase();
          if (errorMsg.contains('success') ||
              errorMsg.contains('已拒绝') ||
              errorMsg.contains('rejected') ||
              errorMsg.contains('reset to unshipped')) {
            // 虽然抛出异常，但实际上操作成功了
            if (mounted) {
              setState(() {
                _shipments.removeWhere(
                    (doc) => doc.documentNo == document.documentNo);
              });

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.t('delivery_rejected')),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          } else {
            // 真正的错误，需要刷新整个列表
            final String errorMessage = apiError.toString();

            if (mounted) {
              // 先获取本地化文本，然后再进行异步操作
              final String localizedMessage = context.t('rejection_failed');

              // 先显示错误消息
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$localizedMessage: $errorMessage'),
                  backgroundColor: Colors.red,
                ),
              );

              // 然后重新加载数据
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                if (mounted) {
                  await _loadShipments();
                }
              });
            }
          }
        }
      } catch (e) {
        // 这里捕获的是除了API直接错误之外的其他错误
        if (mounted) {
          // 先保存错误信息和本地化文本
          final String errorMessage = e.toString();
          final String localizedMessage = context.t('rejection_failed');

          // 先显示错误消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$localizedMessage: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );

          // 然后重新加载数据
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            if (mounted) {
              await _loadShipments();
            }
          });
        }
      }
    }
  }
}
