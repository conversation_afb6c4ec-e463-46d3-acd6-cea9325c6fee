import 'package:flutter/material.dart';
import 'package:queue_system/utils/icon_generator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // 生成Android图标
    await IconGenerator.generateAndroidIcons('assets/images/Icon_TaskPickr-removebg.png');
    print('Android图标生成成功');
    
    // 生成iOS图标
    await IconGenerator.generateIosIcons('assets/images/Icon_TaskPickr-removebg.png');
    print('iOS图标生成成功');
  } catch (e) {
    print('图标生成失败: $e');
  }
}
