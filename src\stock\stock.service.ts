import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SqlServerStock } from './entities/stock.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { CnDetail } from '../postgres/entities/cn-detail.entity';
import { CnMain } from '../postgres/entities/cn-main.entity';

@Injectable()
export class StockService {
  private readonly logger = new Logger(StockService.name);

  constructor(
    @InjectRepository(SqlServerStock, 'sqlServerConnection')
    private readonly stockRepository: Repository<SqlServerStock>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    @InjectRepository(CnDetail, 'postgresConnection')
    private readonly cnDetailRepository: Repository<CnDetail>,
    @InjectRepository(CnMain, 'postgresConnection')
    private readonly cnMainRepository: Repository<CnMain>,
  ) { }

  // 获取待退回库存列表
  async getPendingReturnStocks(staffId: number): Promise<any> {
    this.logger.log(`Getting pending return stocks for staff ID: ${staffId}`);

    try {
      // 1. 验证员工存在
      const staff = await this.staffRepository.findOne({
        where: { id: staffId }
      });

      if (!staff) {
        throw new NotFoundException(`Staff with ID ${staffId} not found`);
      }

      // 2. 检查员工权限 - 只有普通员工可以查看待退回库存
      if (staff.level !== 'regular') {
        throw new ForbiddenException('Only regular staff can view pending return stocks');
      }

      // 3. 获取员工负责的楼层
      const staffFloor = staff.floor;
      if (!staffFloor || staffFloor === 'ALL') {
        throw new BadRequestException('Staff must have a specific floor assignment');
      }

      // 提取楼层数字
      const floorNumber = staffFloor.replace('F', '');

      // 4. 查询待退回的库存
      // 查找所有未确认退回的CN明细记录，且货架号前缀匹配员工楼层
      const pendingReturns = await this.cnDetailRepository
        .createQueryBuilder('detail')
        .leftJoinAndSelect('detail.main', 'main')
        .where('detail.checked_id IS NULL')
        .getMany();

      // 5. 过滤出与员工楼层匹配的记录
      // 这里假设库存信息中有bin_shelf_no字段，格式类似于"2-1-G001-1"
      // 需要查询原始库存信息获取bin_shelf_no
      const filteredReturns = [];

      for (const detail of pendingReturns) {
        // 查询原始库存信息
        const stockInfo = await this.stockRepository.findOne({
          where: { StockCode: detail.stock }
        });

        if (stockInfo && stockInfo.BinShelfNo) {
          const itemFloor = stockInfo.BinShelfNo.split('-')[0];

          // 如果货架楼层与员工楼层匹配，添加到结果中
          if (itemFloor === floorNumber) {
            filteredReturns.push({
              ...detail,
              bin_shelf_no: stockInfo.BinShelfNo
            });
          }
        }
      }

      return {
        total: filteredReturns.length,
        items: filteredReturns
      };
    } catch (error) {
      this.logger.error(`Error getting pending return stocks: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 确认库存已退回
  async confirmStockReturn(detailId: number, staffId: number): Promise<any> {
    this.logger.log(`Confirming stock return for detail ID: ${detailId}, staff ID: ${staffId}`);

    try {
      // 1. 验证员工存在
      const staff = await this.staffRepository.findOne({
        where: { id: staffId }
      });

      if (!staff) {
        throw new NotFoundException(`Staff with ID ${staffId} not found`);
      }

      // 2. 检查员工权限 - 只有普通员工可以确认库存退回
      if (staff.level !== 'regular') {
        throw new ForbiddenException('Only regular staff can confirm stock returns');
      }

      // 3. 查找CN明细记录
      const detail = await this.cnDetailRepository.findOne({
        where: { id: detailId }
      });

      if (!detail) {
        throw new NotFoundException(`CN detail record not found: ${detailId}`);
      }

      // 4. 检查记录是否已经被确认
      if (detail.checked_id) {
        throw new BadRequestException(`Stock return already confirmed by staff ID: ${detail.checked_id}`);
      }

      // 5. 查询原始库存信息，验证楼层权限
      const stockInfo = await this.stockRepository.findOne({
        where: { StockCode: detail.stock }
      });

      if (stockInfo && stockInfo.BinShelfNo) {
        const itemFloor = stockInfo.BinShelfNo.split('-')[0];
        const staffFloor = staff.floor.replace('F', '');

        // 验证员工是否有权限确认该楼层的库存退回
        if (itemFloor !== staffFloor) {
          throw new ForbiddenException(`You don't have permission to confirm stock returns on floor ${itemFloor}`);
        }
      }

      // 6. 更新记录状态
      detail.checked_id = staffId;
      detail.checked_at = new Date();

      // 7. 保存更新
      const updatedDetail = await this.cnDetailRepository.save(detail);

      return {
        success: true,
        message: 'Stock return confirmed successfully',
        detail: updatedDetail
      };
    } catch (error) {
      this.logger.error(`Error confirming stock return: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 库存归位功能
  async returnStock(documentNo: string, line: number, staffId: number): Promise<any> {
    this.logger.log(`Starting stock return for documentNo: ${documentNo}, line: ${line}, staffId: ${staffId}`);

    try {
      // 1. 验证员工存在
      const staff = await this.staffRepository.findOne({
        where: { id: staffId }
      });

      if (!staff) {
        throw new NotFoundException(`Staff with ID ${staffId} not found`);
      }

      // 2. 查找PostgreSQL中的记录
      const detail = await this.detailRepository.findOne({
        where: { document_no: documentNo, line: line }
      });

      if (!detail) {
        throw new NotFoundException(`Detail record not found: ${documentNo}, line: ${line}`);
      }

      this.logger.log(`Found detail record: ${detail.id}, stock: ${detail.stock}`);

      // 3. 重置检查状态
      detail.staff_checked = false;
      detail.staff_checked_at = null;
      detail.staff_id = null;

      // 如果是BOM项目，也重置BOM专员检查状态
      if (detail.parent_code) {
        detail.bom_specialist_checked = false;
        detail.bom_specialist_checked_at = null;
        detail.bom_specialist_id = null;
      }

      // 重置主管检查状态
      detail.supervisor_checked = false;
      detail.supervisor_checked_at = null;
      detail.supervisor_id = null;

      // 4. 保存更新后的记录
      await this.detailRepository.save(detail);

      this.logger.log(`Stock returned successfully for detail: ${detail.id}`);

      // 5. 返回结果
      return {
        success: true,
        message: 'Stock returned successfully',
        detail: {
          id: detail.id,
          document_no: detail.document_no,
          line: detail.line,
          stock: detail.stock,
          status: 'returned'
        }
      };
    } catch (error) {
      this.logger.error(`Error returning stock: ${error.message}`, error.stack);
      throw error;
    }
  }
}
