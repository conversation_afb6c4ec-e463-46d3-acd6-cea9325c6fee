import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/widgets/check_item_card.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';
import 'package:queue_system/utils/search_utils.dart';

class ToCheckTab extends StatefulWidget {
  final List<CheckItem> items;
  final int staffId;
  final bool isSenior;
  final bool isBomSpecialist;
  final bool isBomTab; // 新增：是否为BOM标签页

  // 确认操作回调函数
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;

  const ToCheckTab({
    super.key,
    required this.items,
    required this.staffId,
    required this.isSenior,
    required this.isBomSpecialist,
    required this.onConfirmAction,
    this.isBomTab = false, // 默认为普通标签页
  });

  @override
  State<ToCheckTab> createState() => _ToCheckTabState();
}

class _ToCheckTabState extends State<ToCheckTab> {
  // 用于保持滚动位置
  final ScrollController _scrollController = ScrollController();

  // 用于保存当前展开的文档组
  final Set<String> _expandedGroups = <String>{};

  // 用于保存精确的位置信息
  double? _savedScrollOffset;
  String? _currentOperatingItemKey;
  bool _isRestoring = false;

  // 搜索相关状态
  String _searchQuery = '';
  List<CheckItem> _filteredItems = [];
  Timer? _searchDebounceTimer;

  // 本地项目列表状态，用于局部更新
  List<CheckItem> _localItems = [];

  // 简化的数据状态（移除未使用的变量）

  @override
  void initState() {
    super.initState();
    // 初始化本地项目列表
    _localItems = List.from(widget.items);
    _updateFilteredItems();

    // 简化的初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 主动加载当前标签页的数据
      _refreshData();
    });
  }

  @override
  void didUpdateWidget(ToCheckTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      // 同步本地项目列表
      _localItems = List.from(widget.items);
      _updateFilteredItems();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 更新过滤后的项目列表
  void _updateFilteredItems() {
    setState(() {
      _filteredItems = SearchUtils.searchCheckItems(_localItems, _searchQuery);
    });
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 立即更新搜索查询状态（用于UI显示）
    setState(() {
      _searchQuery = query;
    });

    // 设置防抖定时器执行本地搜索
    _searchDebounceTimer = Timer(const Duration(seconds: 1), () {
      _updateFilteredItems();
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    // 更新本地搜索查询状态
    setState(() {
      _searchQuery = '';
    });

    // 执行本地搜索
    _updateFilteredItems();
  }

  // 刷新数据方法
  Future<void> _refreshData() async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // 使用简化的刷新方式
    await checkListProvider.loadCheckList(
      widget.staffId,
      currentStaff: authProvider.currentStaff,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 使用Consumer监听Provider变化，自动同步数据
    return Consumer<CheckListProvider>(
      builder: (context, provider, child) {
        // 检查是否需要同步数据
        // 特别处理空列表的情况，确保数据同步
        if (widget.items != _localItems ||
            (widget.items.isEmpty && _localItems.isNotEmpty)) {
          // 使用WidgetsBinding确保在build完成后更新状态
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _localItems = List.from(widget.items);
                _updateFilteredItems();
              });
            }
          });
        }

        // 使用简化的内容构建
        return _buildContent(context);
      },
    );
  }

  // 构建简化的内容
  Widget _buildContent(BuildContext context) {
    return Consumer<CheckListProvider>(
      builder: (context, provider, child) {
        // 显示全局加载状态
        if (provider.isLoading && _localItems.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载数据...'),
              ],
            ),
          );
        }

        if (_localItems.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.isBomTab
                      ? Icons.account_tree_outlined
                      : Icons.check_circle_outline,
                  size: 48,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(widget.isBomTab
                    ? context.t('no_bom_items')
                    : context.t('no_pending_items')),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: provider.isLoading ? null : _refreshData,
                  child: provider.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(context.t('refresh')),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // 搜索栏
            SearchBarWidget(
              hintText: context.t('search_documents'),
              onSearchChanged: _onSearchChanged,
              onClear: _onSearchCleared,
              initialValue: _searchQuery,
            ),
            // 显示搜索结果统计
            if (_searchQuery.isNotEmpty)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Icon(Icons.info_outline,
                        size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 8),
                    Text(
                      '${context.t('search_results')}: ${_filteredItems.length} / ${_localItems.length}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            // 项目列表
            Expanded(child: _buildItemsList()),
          ],
        );
      },
    );
  }

  Widget _buildItemsList() {
    // 使用过滤后的项目列表
    final itemsToShow = _filteredItems.isNotEmpty || _searchQuery.isNotEmpty
        ? _filteredItems
        : _localItems;

    // 如果搜索后没有结果，显示无结果提示
    if (_searchQuery.isNotEmpty && _filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              context.t('no_search_results'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              '${context.t('search_keyword')}: "$_searchQuery"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    // 首先按优先级降序排序，然后按创建时间升序排序（紧急订单优先，然后越早的排越前面）
    final sortedItems = List<CheckItem>.from(itemsToShow)
      ..sort((a, b) {
        // 先按优先级降序排序（数值越大优先级越高）
        final priorityComparison = b.priority.compareTo(a.priority);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        // 优先级相同时，按创建时间升序排序
        return a.createdAt.compareTo(b.createdAt);
      });

    // 按document_no和customer分组，而不是按created_at分组
    final documentCustomerGroups = <String, List<CheckItem>>{};
    for (final item in sortedItems) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentCustomerGroups.containsKey(groupKey)) {
        documentCustomerGroups[groupKey] = [];
      }
      documentCustomerGroups[groupKey]!.add(item);
    }

    // 将分组转换为列表并按优先级和创建时间排序（紧急订单优先，然后较早的在前）
    final sortedGroups = documentCustomerGroups.entries.toList()
      ..sort((a, b) {
        // 使用每组中第一个项目的优先级和创建时间进行排序
        final aPriority = a.value.first.priority;
        final bPriority = b.value.first.priority;
        final aTime = a.value.first.createdAt;
        final bTime = b.value.first.createdAt;

        // 先按优先级降序排序
        final priorityComparison = bPriority.compareTo(aPriority);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        // 优先级相同时，按创建时间升序排序
        return aTime.compareTo(bTime);
      });

    // 监听数据变化，恢复滚动位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _restoreScrollPosition();
    });

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: sortedGroups.length,
        itemBuilder: (context, index) {
          final groupEntry = sortedGroups[index];
          final groupItems = groupEntry.value;

          // 获取该组的第一个项目的创建时间，用于显示
          final createdAt = _formatDate(groupItems.first.createdAt,
              format: 'yyyy-MM-dd HH:mm');

          // 直接构建文档组，不再需要中间的客户分组
          return _buildDocumentGroup(context, groupItems, createdAt);
        },
      ),
    );
  }

  // 构建文档组
  Widget _buildDocumentGroup(
    BuildContext context,
    List<CheckItem> items,
    String createdAtStr,
  ) {
    // 获取文档信息
    final firstItem = items.first;
    final documentNo = firstItem.documentNo.contains('/')
        ? firstItem.documentNo.split('/').first
        : firstItem.documentNo;
    final documentDate = firstItem.documentDate;
    final customerCode = firstItem.customer;
    final customerName = firstItem.customerName;

    // 检查是否为紧急订单
    final isUrgent = items.any((item) => item.priority > 0);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isUrgent ? Colors.red.shade50 : null,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                border: Border(left: BorderSide(color: Colors.red, width: 4)),
                borderRadius: BorderRadius.circular(4),
              )
            : null,
        child: ExpansionTile(
          title: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$customerCode - $customerName',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${context.t('document_no')}: $documentNo',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Created: $createdAtStr',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade800,
                  ),
                ),
              ),
            ],
          ),
          subtitle: Text(
              'Document Date: ${_formatDate(documentDate, format: 'dd/MM/yyyy')} | ${items.length} ${items.length == 1 ? 'item' : context.t('items')}'),
          // 使用保存的展开状态
          initiallyExpanded: _expandedGroups.contains(documentNo),
          onExpansionChanged: (isExpanded) {
            setState(() {
              if (isExpanded) {
                _expandedGroups.add(documentNo);
              } else {
                _expandedGroups.remove(documentNo);
              }
            });
          },
          children: items
              .map((item) => CheckItemCard(
                    item: item,
                    staffId: widget.staffId,
                    isSenior: widget.isSenior,
                    isStaffCheck: true,
                    isBomSpecialist: widget.isBomSpecialist,
                    onConfirmAction: _createCustomConfirmAction(item),
                  ))
              .toList(),
        ),
      ),
    );
  }

  // 恢复滚动位置
  void _restoreScrollPosition() {
    if (_savedScrollOffset != null &&
        _scrollController.hasClients &&
        mounted &&
        !_isRestoring) {
      _isRestoring = true;

      // 使用多次尝试的策略，确保滚动位置能够正确恢复
      _attemptScrollRestore(0);
    }
  }

  // 尝试恢复滚动位置（带重试机制）
  void _attemptScrollRestore(int attemptCount) {
    const maxAttempts = 5;

    if (attemptCount >= maxAttempts || !mounted || _savedScrollOffset == null) {
      _isRestoring = false;
      return;
    }

    final delay = 100 + (attemptCount * 100);

    Future.delayed(Duration(milliseconds: delay), () {
      if (!mounted || !_scrollController.hasClients) {
        _attemptScrollRestore(attemptCount + 1);
        return;
      }

      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final targetOffset = _savedScrollOffset!.clamp(0.0, maxScrollExtent);

      _scrollController
          .animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      )
          .then((_) {
        if (mounted) {
          _isRestoring = false;
          _savedScrollOffset = null;
          _currentOperatingItemKey = null;
        }
      }).catchError((error) {
        if (mounted && _scrollController.hasClients) {
          try {
            _scrollController.jumpTo(targetOffset);
          } catch (e) {
            // 静默处理跳转失败
          }
        }
        _isRestoring = false;
        _savedScrollOffset = null;
        _currentOperatingItemKey = null;
      });
    });
  }

  // 处理员工check操作，包含局部更新
  Future<void> _handleStaffCheck(CheckItem item) async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);

    try {
      // 执行员工检查
      await checkListProvider.staffCheck(
        item.documentNo,
        item.line,
        widget.staffId,
      );

      // 立即从本地列表中移除已检查的项目
      _removeItemFromLocalList(item.documentNo, item.line);
    } catch (e) {
      // 错误处理由Provider层处理
      rethrow;
    }
  }

  // 处理BOM专员check操作，包含局部更新
  Future<void> _handleBomSpecialistCheck(CheckItem item) async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);

    try {
      // 执行BOM专员检查
      await checkListProvider.bomCheck(
        item.documentNo,
        item.line,
        widget.staffId,
      );

      // 立即从本地列表中移除已检查的项目
      _removeItemFromLocalList(item.documentNo, item.line);
    } catch (e) {
      // 错误处理由Provider层处理
      rethrow;
    }
  }

  // 从本地列表中移除项目
  void _removeItemFromLocalList(String documentNo, int line) {
    if (mounted) {
      setState(() {
        // 从本地项目列表中移除
        _localItems.removeWhere(
            (item) => item.documentNo == documentNo && item.line == line);

        // 更新过滤后的列表
        _updateFilteredItems();
      });
    }
  }

  // 创建自定义的确认操作回调，保持当前视图位置
  Function(BuildContext, String, String, Future<void> Function())
      _createCustomConfirmAction(CheckItem item) {
    return (BuildContext context, String title, String content,
        Future<void> Function() action) {
      // 保存当前滚动位置和操作的项目信息
      if (_scrollController.hasClients) {
        _savedScrollOffset = _scrollController.offset;
        _currentOperatingItemKey = '${item.documentNo}_${item.line}';
      }

      // 检查操作类型并使用相应的处理方法
      if (title.contains('检查确认') ||
          title.contains('Check Confirmation') ||
          title.contains('确认BOM检查') ||
          title.contains('Confirm BOM Check')) {
        // 根据是否为BOM标签页选择处理方法
        if (widget.isBomTab || title.contains('BOM') || title.contains('bom')) {
          // BOM检查操作
          widget.onConfirmAction(
            context,
            title,
            content,
            () => _handleBomSpecialistCheck(item),
          );
        } else {
          // 员工检查操作
          widget.onConfirmAction(
            context,
            title,
            content,
            () => _handleStaffCheck(item),
          );
        }
      } else if (title.contains('BOM检查确认') ||
          title.contains('BOM Check Confirmation')) {
        // BOM检查操作（备用检测）
        widget.onConfirmAction(
          context,
          title,
          content,
          () => _handleBomSpecialistCheck(item),
        );
      } else {
        // 其他操作使用原始的确认操作
        widget.onConfirmAction(
          context,
          title,
          content,
          action,
        );
      }
    };
  }

  // 格式化日期为指定格式
  String _formatDate(DateTime dateTime, {String format = 'yyyy-MM-dd'}) {
    if (format == 'yyyy-MM-dd') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    } else if (format == 'yyyy-MM-dd HH:mm') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (format == 'yyyy-MM-dd HH:mm:ss') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
    } else if (format == 'dd/MM/yyyy') {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } else if (format == 'HH:mm dd/MM/yyyy') {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')} ${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } else {
      return dateTime.toString();
    }
  }
}
