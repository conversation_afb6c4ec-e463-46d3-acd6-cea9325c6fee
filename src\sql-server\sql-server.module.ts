import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SqlServerMain } from './entities/main.entity';
import { SqlServerDetail } from './entities/detail.entity';
import { SqlServerStock } from '../stock/entities/stock.entity';
import { SqlServerCustomer } from './entities/customer.entity';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const config = configService.get('database.sqlServer');
        return {
          ...config,
          name: 'sqlServer',
          autoLoadEntities: true,
        };
      },
    }),
    TypeOrmModule.forFeature([SqlServerMain, SqlServerDetail, SqlServerStock, SqlServerCustomer], 'sqlServerConnection'),
  ],
  exports: [TypeOrmModule],
})
export class SqlServerModule { }