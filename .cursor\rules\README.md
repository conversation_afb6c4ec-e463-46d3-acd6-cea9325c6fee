---
title: "队列系统文档中心"
description: "队列系统的文档索引和导航"
version: "1.0.0"
last_updated: "2025-05-19"
---

# 队列系统文档中心

本文档中心包含队列系统的所有技术文档，分为后端（NestJS）和前端（Flutter）两部分。

## 文档目录

### 通用文档
- [项目概述](project-overview.md) - 队列系统的整体介绍和架构
- [业务流程说明](business-flow.md) - 核心业务流程和规则
- [系统安全指南](security-guidelines.md) - 系统安全实践和指南

### 后端文档
- [后端项目概述](backend/project-overview.md) - NestJS 后端项目的总体介绍
- [API 端点规范](backend/api-endpoints.md) - 所有 API 端点的详细说明
- [API 参考文档](backend/api-reference.md) - API 的完整参考文档
- [数据模型定义](backend/data-models.md) - 系统数据模型和实体关系
- [错误处理规范](backend/error-handling.md) - API 错误处理和响应格式
- [开发规则](backend/development-rules.md) - NestJS 后端项目开发规范

### 前端文档
- [前端项目概述](frontend/project-overview.md) - Flutter 移动应用的总体介绍
- [状态管理说明](frontend/state-management.md) - 应用状态管理方案
- [UI 组件说明](frontend/ui-components.md) - 可复用 UI 组件的说明
- [API 服务实现](frontend/api-services.md) - 网络请求和 API 服务实现

## 文档组织结构

```
.cursor/
  ├── rules/
  │   ├── README.md                  # 本文档（统一索引）
  │   ├── project-overview.md        # 项目整体概述
  │   ├── business-flow.md           # 业务流程说明
  │   ├── security-guidelines.md     # 系统安全指南
  │   ├── backend/                   # 后端文档目录
  │   │   ├── project-overview.md    # 后端项目概述
  │   │   ├── api-endpoints.md       # API 端点规范
  │   │   ├── api-reference.md       # API 完整参考文档（合并后）
  │   │   ├── data-models.md         # 数据模型定义
  │   │   ├── error-handling.md      # 错误处理规范
  │   │   └── development-rules.md   # 开发规则
  │   └── frontend/                  # 前端文档目录
  │       ├── project-overview.md    # 前端项目概述
  │       ├── state-management.md    # 状态管理说明
  │       ├── ui-components.md       # UI 组件说明
  │       └── api-services.md        # API 服务实现
```

## 文档使用指南

### 如何查找信息
1. 从本索引页开始，找到您感兴趣的主题
2. 点击相应的链接进入详细文档
3. 使用文档内的目录和链接进一步导航

### 文档更新规则
1. 所有文档使用 Markdown 格式
2. 每个文档都应包含标准的元数据区域（标题、描述、版本、最后更新日期）
3. 文档更新后应更新元数据中的版本和日期
4. 重大更新应在本索引页的版本历史中记录

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-05-19 | 初始版本：重组文档结构，创建统一索引 |
