import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/locale_provider.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/widgets/info_chip.dart';

class CheckItemCard extends StatefulWidget {
  final CheckItem item;
  final int staffId;
  final bool isSenior;
  final bool isStaffCheck;
  final bool isBomReview;
  final bool isIntegratedMode; // 新增：是否为整合模式（waiting页面整合to_confirm功能）
  final bool isBomSpecialist; // 新增：是否为BOM专员
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;

  CheckItemCard({
    super.key,
    required this.item,
    required this.staffId,
    required this.isSenior,
    required this.isStaffCheck,
    this.isBomReview = false,
    this.isIntegratedMode = false, // 默认为false，保持向后兼容
    this.isBomSpecialist = false, // 默认为false
    required this.onConfirmAction,
  });

  @override
  State<CheckItemCard> createState() => _CheckItemCardState();
}

class _CheckItemCardState extends State<CheckItemCard> {
  // 添加CheckService实例
  late final CheckService _checkService;

  // BOM子项展开状态
  bool _isExpanded = false;
  List<CheckItem> _bomChildren = [];
  bool _isLoadingChildren = false;

  @override
  void initState() {
    super.initState();
    _checkService = CheckService(baseUrl: AppConfig.baseUrl);
  }

  // 加载BOM子项
  Future<void> _loadBomChildren() async {
    if (!widget.item.isBomParent || _isLoadingChildren) return;

    print('===== 加载BOM子项调试 =====');
    print('父项: ${widget.item.documentNo}, stock: ${widget.item.stock}');
    print('bomGroup: ${widget.item.bomGroup}');

    setState(() {
      _isLoadingChildren = true;
    });

    try {
      final checkListProvider =
          Provider.of<CheckListProvider>(context, listen: false);
      final bomGroup =
          widget.item.bomGroup ?? widget.item.documentNo.split('/').first;
      final parentStock = widget.item.stock;
      final bomParentId = widget.item.id; // 使用父项的ID

      print('===== BOM子项加载调试 =====');
      print('父项信息:');
      print('  id: ${widget.item.id}');
      print('  documentNo: ${widget.item.documentNo}');
      print('  stock: ${widget.item.stock}');
      print('  bomGroup: ${widget.item.bomGroup}');
      print('  isBomParent: ${widget.item.isBomParent}');
      print('使用的bomGroup: $bomGroup');
      print('使用的parentStock: $parentStock');
      print('使用的bomParentId: $bomParentId');

      final children = await checkListProvider.getBomChildrenStatus(
          bomGroup, parentStock, bomParentId);
      print('获取到的子项数量: ${children.length}');

      for (var child in children) {
        print(
            '子项: ${child.documentNo}, stock: ${child.stock}, parentCode: ${child.parentCode}, bomGroup: ${child.bomGroup}, bomParentId: ${child.bomParentId}');
      }
      print('===== BOM子项加载调试结束 =====');

      if (mounted) {
        setState(() {
          _bomChildren = children;
          _isLoadingChildren = false;
        });
        print('子项加载完成，状态已更新');
      }
    } catch (e) {
      print('加载子项时出错: $e');
      if (mounted) {
        setState(() {
          _isLoadingChildren = false;
        });
      }
    }
  }

  // 切换展开状态
  void _toggleExpansion() {
    if (!widget.item.isBomParent) return;

    print('===== BOM展开调试 =====');
    print('父项: ${widget.item.documentNo}, stock: ${widget.item.stock}');
    print('isBomParent: ${widget.item.isBomParent}');
    print('当前展开状态: $_isExpanded');
    print('子项数量: ${_bomChildren.length}');

    setState(() {
      _isExpanded = !_isExpanded;
    });

    print('新展开状态: $_isExpanded');

    if (_isExpanded && _bomChildren.isEmpty) {
      print('开始加载子项...');
      _loadBomChildren();
    }
  }

  // 显示简化的 BOM Reject 对话框，与 driver 页面保持一致
  Future<void> _showSimpleBomRejectDialog(
      BuildContext context, CheckListProvider checkListProvider) async {
    final TextEditingController reasonController = TextEditingController();

    // 预先获取本地化文本，避免异步使用 context
    final noReasonProvided = context.t('no_reason_provided');
    final confirmRejectText = context.t('confirm_reject');
    final confirmRejectMessageText = context.t('confirm_reject_message');

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('reject_staff_check')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${widget.item.documentNo}'),
            Text('${context.t('line')}: ${widget.item.line}'),
            const SizedBox(height: 16),
            Text(context.t('rejection_reason')),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                border: const OutlineInputBorder(),
                hintText: context.t('enter_reject_reason'),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              context.t('confirm'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final reason = reasonController.text.isEmpty
          ? noReasonProvided
          : reasonController.text;

      widget.onConfirmAction(
        context,
        confirmRejectText,
        confirmRejectMessageText,
        () => checkListProvider.rejectStaffCheck(
          widget.item.documentNo,
          widget.item.line,
          widget.staffId,
          reason,
        ),
      );
    }
  }

  // 【已修改为简化版本】显示拒绝对话框 - 现在与 driver 页面保持一致
  Future<void> _showRejectDialog(BuildContext context) async {
    final TextEditingController reasonController = TextEditingController();

    // 预先获取本地化文本和 Provider，避免异步使用 context
    final noReasonProvided = context.t('no_reason_provided');
    final confirmRejectText = context.t('confirm_reject');
    final confirmRejectMessageText = context.t('confirm_reject_message');
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('reject_staff_check')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${widget.item.documentNo}'),
            Text('${context.t('line')}: ${widget.item.line}'),
            const SizedBox(height: 16),
            Text(context.t('rejection_reason')),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                border: const OutlineInputBorder(),
                hintText: context.t('enter_reject_reason'),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              context.t('confirm'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final reason = reasonController.text.isEmpty
          ? noReasonProvided
          : reasonController.text;

      widget.onConfirmAction(
        context,
        confirmRejectText,
        confirmRejectMessageText,
        () => checkListProvider.rejectStaffCheck(
          widget.item.documentNo,
          widget.item.line,
          widget.staffId,
          reason,
        ),
      );
    }
  }

  /*
  // 【已废弃】显示拒绝对话框 - 旧的双选项版本
  // 保留此代码以备后续需要恢复双选项功能
  void _showRejectDialog_OLD(BuildContext context) {
    // 创建一个文本控制器用于输入拒绝原因
    final TextEditingController reasonController = TextEditingController();

    // 默认选择"修改/取消"选项
    bool useCustomReason = false;

    // 预先获取所有需要的文本，避免在异步操作中使用context
    final String documentText = context.t('document');
    final String documentNo = widget.item.documentNo;
    final String rejectStaffCheckText = context.t('reject_staff_check');
    final String selectRejectTypeText = context.t('select_reject_type');
    final String modifyCancelText = context.t('modify_cancel');
    final String customReasonText = context.t('custom_reason');
    final String enterRejectReasonText = context.t('enter_reject_reason');
    final String rejectShipmentWarningText =
        context.t('reject_shipment_warning');
    final String cancelText = context.t('cancel');
    final String confirmText = context.t('confirm');
    final String noReasonProvidedText = context.t('no_reason_provided');
    final String operationSuccessText = context.t('operation_success');
    final String rejectFailedText = context.t('reject_failed');

    // 设置默认文本
    reasonController.text = modifyCancelText;

    // 显示一个对话框，让用户选择拒绝原因类型
    showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(rejectStaffCheckText),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('$documentText: $documentNo'),
                  const SizedBox(height: 16),
                  // 下拉选单标签
                  Text(
                    selectRejectTypeText,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // 下拉选单
                  DropdownButton<bool>(
                    isExpanded: true,
                    value: useCustomReason,
                    items: [
                      DropdownMenuItem(
                        value: false,
                        child: Text(modifyCancelText),
                      ),
                      DropdownMenuItem(
                        value: true,
                        child: Text(customReasonText),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        useCustomReason = value!;
                        if (!useCustomReason) {
                          reasonController.text = modifyCancelText;
                        } else {
                          reasonController.text = '';
                        }
                      });
                    },
                  ),
                  const SizedBox(height: 8),
                  // 如果选择自定义原因，显示文本输入框
                  if (useCustomReason)
                    TextField(
                      controller: reasonController,
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        hintText: enterRejectReasonText,
                      ),
                      maxLines: 3,
                    ),
                  const SizedBox(height: 8),
                  Text(
                    rejectShipmentWarningText,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.red.shade700,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(null),
                  child: Text(cancelText),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  onPressed: () {
                    // 返回一个包含所有需要信息的Map
                    Navigator.of(dialogContext).pop({
                      'confirmed': true,
                      'useCustomReason': useCustomReason,
                      'reason': useCustomReason
                          ? (reasonController.text.isEmpty
                              ? noReasonProvidedText
                              : reasonController.text)
                          : modifyCancelText,
                      'modifyCancel': !useCustomReason,
                    });
                  },
                  child: Text(
                    confirmText,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            );
          },
        );
      },
    ).then((result) {
      // 如果用户取消或关闭对话框，result将为null
      if (result == null || result['confirmed'] != true) {
        return;
      }

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 获取对话框返回的数据
      final bool modifyCancel = result['modifyCancel'] as bool;
      final String reason = result['reason'] as String;

      // 显示加载对话框
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext loadingContext) {
            return const AlertDialog(
              backgroundColor: Colors.transparent,
              elevation: 0,
              content: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        );

        // 使用异步函数处理后续操作
        _handleRejectAction(
          modifyCancel: modifyCancel,
          reason: reason,
          operationSuccessText: operationSuccessText,
          rejectFailedText: rejectFailedText,
          modifyCancelText: modifyCancelText,
        );
      }
    });
  }

  // 处理拒绝操作的异步方法
  Future<void> _handleRejectAction({
    required bool modifyCancel,
    required String reason,
    required String operationSuccessText,
    required String rejectFailedText,
    required String modifyCancelText,
  }) async {
    // 关闭加载对话框
    if (mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }

    // 使用 onConfirmAction 回调来处理拒绝操作，这样可以保持视图位置
    widget.onConfirmAction(
      context,
      context.t('confirm_reject'),
      context.t('confirm_reject_message'),
      () async {
        final checkListProvider =
            Provider.of<CheckListProvider>(context, listen: false);

        // 如果是"修改/取消"选项，执行增强的同步逻辑
        if (modifyCancel) {
          // 1. 首先拒绝检查
          await checkListProvider.rejectStaffCheck(
            widget.item.documentNo,
            widget.item.line,
            widget.staffId,
            modifyCancelText,
          );

          // 注释掉从SQL Server同步最新数据的逻辑 - 2025-06-18
          /*
          // 2. 从SQL Server同步最新数据
          try {
            final syncResult = await _checkService.syncDetail(
              widget.item.documentNo,
              widget.item.line,
            );

            // 3. 检查是否需要库存归位
            if (syncResult['needStockReturn'] == true) {
              try {
                await _checkService.returnStock(
                  widget.item.documentNo,
                  widget.item.line,
                  widget.staffId,
                );
              } catch (returnError) {
                // 库存归位失败不影响整体流程
              }
            }
          } catch (syncError) {
            // 同步失败不影响整体流程，继续执行
          }
          */
        } else {
          // 普通拒绝流程
          await checkListProvider.rejectStaffCheck(
            widget.item.documentNo,
            widget.item.line,
            widget.staffId,
            reason,
          );
        }
      },
    );
  }
  */

  // 辅助方法：将UTC时间转换为马来西亚时区（UTC+8）
  DateTime _toMalaysiaTime(DateTime? utcTime) {
    if (utcTime == null) return DateTime.now();
    // 转换为马来西亚时区（UTC+8）
    return utcTime.add(const Duration(hours: 8));
  }

  // 格式化日期时间为指定格式
  String _formatDateTime(BuildContext context, DateTime? dateTime,
      {String format = 'MM-dd HH:mm'}) {
    if (dateTime == null) return context.t('unknown_time');
    return DateFormat(format).format(_toMalaysiaTime(dateTime));
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        // 移除BOM父项的点击展开功能，统一使用主单据展开方式
        onTap: null,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Text(
                                '${context.t('line_no')}: ${widget.item.documentNo}/${widget.item.line} - ${widget.item.stock}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ],
                        ),
                        Text(
                          widget.item.description,
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                        // 显示零件号（如果有）
                        if (widget.item.partNo != null &&
                            widget.item.partNo!.isNotEmpty)
                          Text(
                            'Part No: ${widget.item.partNo}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      // 根据用户权限和项目类型显示不同的按钮
                      if (widget.item.isBomParent) ...[
                        // BOM父项的按钮逻辑
                        if (widget.isBomSpecialist) ...[
                          // BOM专员：显示展开按钮，无检查按钮
                          IconButton(
                            onPressed: () {
                              print('===== 展开按钮点击调试 =====');
                              print('按钮被点击了！');
                              _toggleExpansion();
                            },
                            icon: Icon(
                              _isExpanded
                                  ? Icons.expand_less
                                  : Icons.expand_more,
                              color: Colors.blue,
                            ),
                            tooltip: _isExpanded
                                ? context.t('collapse')
                                : context.t('expand'),
                          ),
                        ] else if (widget.isSenior) ...[
                          // 主管/管理员：简化的BOM检查逻辑，仅检查 bomChildrenCompleted
                          if (widget.item.bomChildrenCompleted == true) ...[
                            // BOM子项全部完成，显示检查按钮
                            _buildActionButton(context),
                          ] else ...[
                            // BOM子项未全部完成，显示提示
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                context.t('waiting_for_staff_bom'),
                                style: TextStyle(
                                  color: Colors.orange.shade800,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ] else ...[
                        // 非BOM父项显示操作按钮
                        _buildActionButton(context),
                      ],
                    ],
                  ),
                ],
              ),
              const Divider(),
              // 货物详情
              Wrap(
                spacing: 16,
                runSpacing: 4,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Wrap(
                        spacing: 16,
                        children: [
                          // QTY
                          InfoChip(
                            icon: Icons.shopping_cart,
                            label: context.t('quantity_short'),
                            value: '${widget.item.quantity} ${widget.item.uom}',
                            color: Colors.blue,
                          ),
                          // Shelf
                          InfoChip(
                            icon: Icons.location_on,
                            label: context.t('shelf_location'),
                            value: widget.item.binShelfNo,
                            color: Colors.green,
                            isEmpty: widget
                                .item.binShelfNo.isEmpty, // 添加这一行，检查bin是否为空
                          ),
                          // BOM (如果有)
                          if (widget.item.parentCode != null &&
                              widget.item.parentCode!.isNotEmpty)
                            InfoChip(
                              icon: Icons.account_tree,
                              label: context.t('parent_code'),
                              value: widget.item.parentCode!,
                              color: Colors.orange,
                            ),
                        ],
                      ),
                      // 如果没有库存，显示No Stock标签
                      if (!widget.item.haveStock)
                        Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'No Stock',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (widget.item.staffChecked)
                    InfoChip(
                      icon: Icons.check_circle,
                      label: context.t('checked_status'),
                      value:
                          _formatDateTime(context, widget.item.staffCheckedAt),
                      color: Colors.green,
                    ),
                  if (widget.item.remark != null &&
                      widget.item.remark!.isNotEmpty)
                    InfoChip(
                      icon: Icons.note,
                      label: context.t('note'),
                      value: widget.item.remark!,
                      color: Colors.blue,
                    ),
                ],
              ),
              // BOM子项展开区域（仅BOM专员可见）
              if (widget.item.isBomParent &&
                  widget.isBomSpecialist &&
                  _isExpanded)
                _buildBomChildrenSection(),
            ],
          ),
        ),
      ),
    );
  }

  // 构建BOM子项展开区域
  Widget _buildBomChildrenSection() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.account_tree, size: 16, color: Colors.orange),
              const SizedBox(width: 4),
              Text(
                context.t('bom_items'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade700,
                ),
              ),
              if (_isLoadingChildren) ...[
                const SizedBox(width: 8),
                const SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          if (_isLoadingChildren)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(context.t('loading'),
                    style: const TextStyle(color: Colors.grey)),
              ),
            )
          else if (_bomChildren.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(context.t('no_bom_children_found'),
                    style: const TextStyle(color: Colors.grey)),
              ),
            )
          else
            ..._bomChildren.map((child) => _buildBomChildItem(child)),
        ],
      ),
    );
  }

  // 构建单个BOM子项
  Widget _buildBomChildItem(CheckItem child) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8, left: 16), // 添加左边距实现缩进
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 子项标题行
          Row(
            children: [
              // 缩进指示器
              Container(
                width: 3,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${child.documentNo}/${child.line} - ${child.stock}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      child.description,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // 子项的检查按钮
              _buildChildActionButton(child),
            ],
          ),
          const SizedBox(height: 8),
          // 子项详情
          Padding(
            padding: const EdgeInsets.only(left: 11), // 与缩进指示器对齐
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildChildInfoChip(
                  Icons.shopping_cart,
                  context.t('quantity_short'),
                  '${child.quantity} ${child.uom}',
                  Colors.blue,
                ),
                _buildChildInfoChip(
                  Icons.location_on,
                  context.t('shelf_location'),
                  child.binShelfNo.isEmpty
                      ? context.t('unassigned_shelf')
                      : child.binShelfNo,
                  child.binShelfNo.isEmpty ? Colors.orange : Colors.green,
                ),
                if (!child.haveStock)
                  _buildChildInfoChip(
                    Icons.warning,
                    context.t('stock'),
                    context.t('no_stock'),
                    Colors.red,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建子项信息标签
  Widget _buildChildInfoChip(
      IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 2),
          Text(
            '$label: $value',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 处理BOM子项确认操作，包含状态更新
  Future<void> _handleBomChildConfirm(CheckItem child) async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);

    try {
      // 执行BOM检查
      await checkListProvider.bomCheck(
        child.documentNo,
        child.line,
        widget.staffId,
      );

      // 立即更新本地子项状态
      _updateChildStatusLocally(child.documentNo, child.line, true);

      // 重新加载子项数据以确保同步
      await _loadBomChildren();
    } catch (e) {
      // 错误处理由Provider层处理
      rethrow;
    }
  }

  // 局部更新子项状态
  void _updateChildStatusLocally(
      String documentNo, int line, bool bomSpecialistChecked) {
    if (mounted) {
      setState(() {
        final index = _bomChildren
            .indexWhere((c) => c.documentNo == documentNo && c.line == line);
        if (index != -1) {
          // 使用copyWith方法简化更新
          final updatedChild = _bomChildren[index].copyWith(
            bomSpecialistChecked: bomSpecialistChecked,
            bomSpecialistCheckedAt:
                bomSpecialistChecked ? DateTime.now() : null,
            bomSpecialistId: bomSpecialistChecked ? widget.staffId : null,
          );
          _bomChildren[index] = updatedChild;
        }
      });
    }
  }

  // 构建子项操作按钮
  Widget _buildChildActionButton(CheckItem child) {
    // 检查是否为特殊条件的BOM子项目（无货架位置或无库存）
    bool isSpecialBomItem = child.binShelfNo.trim().isEmpty || !child.haveStock;

    // 检查子项的状态
    if (!child.staffChecked && !isSpecialBomItem) {
      // 未被普通员工检查且不是特殊条件，显示提示
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange.shade100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          context.t('waiting_for_staff'),
          style: TextStyle(
            color: Colors.orange.shade800,
            fontSize: 10,
          ),
        ),
      );
    }

    if (child.bomSpecialistChecked) {
      // 已被BOM专员检查，显示状态
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.shade100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          context.t('checked'),
          style: TextStyle(
            color: Colors.green.shade800,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // 可以检查，显示检查和拒绝按钮
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 如果是特殊条件的BOM子项目，显示提示信息
        if (isSpecialBomItem)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            margin: const EdgeInsets.only(bottom: 4),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Text(
              context.t('special_item'),
              style: TextStyle(
                color: Colors.orange.shade800,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        // Reject按钮
        SizedBox(
          width: 60,
          height: 28,
          child: ElevatedButton(
            onPressed: () {
              _showChildRejectDialog(child);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              textStyle: const TextStyle(fontSize: 10),
            ),
            child: Text(context.t('reject')),
          ),
        ),
        const SizedBox(height: 4),
        // 检查按钮
        SizedBox(
          width: 60,
          height: 28,
          child: ElevatedButton(
            onPressed: () {
              widget.onConfirmAction(
                context,
                context.t('confirm_bom_check'),
                context.t('confirm_bom_child_check_message'),
                () => _handleBomChildConfirm(child),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              textStyle: const TextStyle(fontSize: 10),
            ),
            child: Text(context.t('check')),
          ),
        ),
      ],
    );
  }

  // 显示子项拒绝对话框
  Future<void> _showChildRejectDialog(CheckItem child) async {
    final TextEditingController reasonController = TextEditingController();

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.t('reject_reason')),
          content: TextField(
            controller: reasonController,
            decoration: InputDecoration(
              hintText: context.t('enter_reject_reason'),
              border: const OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.t('cancel')),
            ),
            ElevatedButton(
              onPressed: () async {
                if (reasonController.text.trim().isEmpty) {
                  return;
                }

                Navigator.of(context).pop();

                final checkListProvider =
                    Provider.of<CheckListProvider>(context, listen: false);
                await checkListProvider.rejectStaffCheck(
                  child.documentNo,
                  child.line,
                  widget.staffId,
                  reasonController.text.trim(),
                );

                // 重新加载子项数据
                _loadBomChildren();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: Text(context.t('confirm_reject'),
                  style: const TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // 检查是否为BOM专员
    final isBomSpecialist = authProvider.currentStaff?.level == 'staff_bom';

    if (widget.isStaffCheck) {
      // BOM专员模式 - 但Supervisor优先使用supervisorCheck
      if (widget.isBomReview && isBomSpecialist && !widget.isSenior) {
        return Row(
          children: [
            // Reject按钮
            ElevatedButton.icon(
              onPressed: () {
                _showSimpleBomRejectDialog(context, checkListProvider);
              },
              icon: const Icon(Icons.cancel),
              label: Text(context.t('reject')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 8), // 添加间距
            // 确认按钮
            ElevatedButton.icon(
              onPressed: () {
                widget.onConfirmAction(
                  context,
                  context.t('confirm_bom_check'),
                  context.t('confirm_bom_check_message'),
                  () => checkListProvider.bomCheck(
                      widget.item.documentNo, widget.item.line, widget.staffId),
                );
              },
              icon: const Icon(Icons.biotech),
              label: Text(context.t('bom_check')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      }

      // Supervisor在BOM Review模式下使用supervisorCheck
      if (widget.isBomReview && widget.isSenior) {
        return Row(
          children: [
            // Reject按钮
            ElevatedButton.icon(
              onPressed: () {
                _showRejectDialog(context);
              },
              icon: const Icon(Icons.cancel),
              label: Text(context.t('reject')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 8), // 添加间距
            // 确认按钮 - 使用supervisorCheck统一方法
            ElevatedButton.icon(
              onPressed: () {
                widget.onConfirmAction(
                  context,
                  context.t('confirm_verify'),
                  context.t('confirm_verify_message'),
                  () => checkListProvider.supervisorCheck(
                      widget.item.documentNo, widget.item.line, widget.staffId),
                );
              },
              icon: const Icon(Icons.verified),
              label: Text(context.t('confirm')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      }

      // 主管不需要看到检查按钮，只显示状态
      if (widget.isSenior || (isBomSpecialist && !widget.isBomReview)) {
        if (widget.item.staffChecked) {
          return Text(
            context.t('already_checked'),
            style: const TextStyle(
              color: Colors.green,
              fontWeight: FontWeight.bold,
            ),
          );
        } else {
          return Text(
            context.t('not_checked'),
            style: const TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
            ),
          );
        }
      }

      // 员工检查按钮 - 添加防重复点击机制
      return Consumer<CheckListProvider>(
        builder: (context, provider, child) {
          // 检查当前项目是否正在处理中
          final isItemProcessing = provider.isItemProcessing(
              widget.item.documentNo, widget.item.line);
          // 移除全局loading检查，只检查当前项目的处理状态
          // final isGlobalLoading = provider.isLoading;
          final isProcessing = isItemProcessing; // 只使用项目级别的处理状态

          return ElevatedButton.icon(
            onPressed: isProcessing
                ? null
                : () {
                    widget.onConfirmAction(
                      context,
                      context.t('confirm_check'),
                      context.t('confirm_check_message'),
                      () => checkListProvider.staffCheck(widget.item.documentNo,
                          widget.item.line, widget.staffId),
                    );
                  },
            icon: isProcessing
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.check_circle_outline),
            label: Text(
                isProcessing ? context.t('processing') : context.t('check')),
            style: ElevatedButton.styleFrom(
              backgroundColor: isProcessing ? Colors.grey : Colors.green,
              foregroundColor: Colors.white,
            ),
          );
        },
      );
    } else {
      // 获取当前用户的角色
      final isDriver = authProvider.currentStaff?.level == 'driver';
      final isSupervisor = authProvider.currentStaff?.level == 'supervisor' ||
          authProvider.currentStaff?.level == 'admin';

      // 主管确认按钮 - 只有主管和司机才能确认
      if ((!isSupervisor && !isDriver) || isBomSpecialist) {
        return Text(
          context.t('waiting_for_supervisor'),
          style: const TextStyle(
            color: Colors.orange,
            fontWeight: FontWeight.bold,
          ),
        );
      }

      // 检查是否为跳过staff检查的条件：无库存或无货架位置
      bool shouldSkipStaffCheck =
          !widget.item.haveStock || widget.item.binShelfNo.trim().isEmpty;

      // 在整合模式下，根据staff检查状态显示不同的UI
      if (widget.isIntegratedMode) {
        // 整合模式：waiting页面包含to_confirm功能
        if (!widget.item.staffChecked && !shouldSkipStaffCheck) {
          // 未被staff检查且不满足跳过条件，显示等待状态
          return Text(
            context.t('waiting_for_staff'),
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          );
        }
        // 已被staff检查或满足跳过条件，显示confirm按钮（继续执行下面的逻辑）
      } else {
        // 原有逻辑：非整合模式
        if (shouldSkipStaffCheck) {
          // 无库存或无货架位置的物品，允许Supervisor直接确认，不需要等待staff检查
        } else if (!widget.item.staffChecked) {
          // 如果有库存且有货架位置但未被staff检查，显示等待staff检查的提示
          return Text(
            context.t('waiting_for_staff'),
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          );
        }
      }

      // 为Supervisor和Driver添加Confirm和Reject按钮
      return Row(
        children: [
          // Reject按钮
          ElevatedButton.icon(
            onPressed: () {
              _showRejectDialog(context);
            },
            icon: const Icon(Icons.cancel),
            label: Text(context.t('reject')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8), // 添加间距
          // Confirm按钮
          ElevatedButton.icon(
            onPressed: () {
              widget.onConfirmAction(
                context,
                context.t('confirm_verify'),
                context.t('confirm_verify_message'),
                () {
                  return checkListProvider.supervisorCheck(
                      widget.item.documentNo, widget.item.line, widget.staffId);
                },
              );
            },
            icon: const Icon(Icons.verified),
            label: Text(context.t('confirm')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      );
    }
  }
}
