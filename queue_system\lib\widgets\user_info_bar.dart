import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/staff.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/check_list_provider.dart';

class UserInfoBar extends StatelessWidget {
  const UserInfoBar({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final staff = authProvider.currentStaff;

    if (staff == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12.0),
      color: Colors.blue.shade50,
      child: Row(
        children: [
          const Icon(Icons.person, color: Colors.blue),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                staff.fullName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                _getStaffLevelText(context, staff),
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const Spacer(),
          Consumer<CheckListProvider>(
            builder: (context, provider, _) {
              if (provider.isLoading) {
                return const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  String _getStaffLevelText(BuildContext context, Staff staff) {
    switch (staff.level) {
      case 'admin':
        return '${context.t('admin')} | ${staff.floor}';
      case 'supervisor':
        return '${context.t('supervisor')} | ${staff.floor}';
      case 'senior':
        return '${context.t('supervisor')} | ${staff.floor}';
      case 'staff_bom':
        return '${context.t('bom_specialist')} | ${staff.floor}';
      case 'driver':
        return '${context.t('driver')} | ${staff.floor}';
      case 'drive':
        return '${context.t('driver')} | ${staff.floor}';
      case 'regular':
      default:
        return '${context.t('regular_staff')} | ${staff.floor}';
    }
  }
}
