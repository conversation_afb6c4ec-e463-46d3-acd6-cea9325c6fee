---
title: "错误处理及API响应规范"
description: "队列系统后端的错误处理和API响应格式规范"
version: "1.0.0"
last_updated: "2025-05-19"
---

# 错误处理及API响应规范

## 标准API响应格式

### 成功响应
所有成功的API响应应当遵循以下格式：
```json
{
  "success": true,
  "message": "操作成功信息",
  "data/detail": { ... } // 可选，返回的数据
}
```

### 错误响应
所有错误响应应当遵循以下格式：
```json
{
  "success": false,
  "message": "错误描述信息",
  "error": { ... } // 可选，详细错误信息
}
```

## 常见错误类型

### 1. 认证错误 (401)
- 未提供有效的凭据
- 登录失败
- 凭据过期

### 2. 权限错误 (403)
- 尝试访问没有权限的资源
- 尝试操作不在自己负责楼层的货物
- 以普通员工身份执行主管操作

### 3. 资源未找到 (404)
- 查询不存在的货物
- 查询不存在的员工
- 查询不存在的订单

### 4. 业务逻辑错误 (400)
- 尝试检查已被检查的货物
- 尝试出货未完全检查的订单
- 尝试拒绝未检查的货物
- 重复提交相同的请求

### 5. 服务器错误 (500)
- 数据库连接错误
- 同步过程中的错误
- 其他未预期的服务器错误

## 处理特殊文档编号

由于文档编号现在支持格式如 `D00001/1`，斜杠在URL中需要特殊处理：

### 1. 推荐方法：使用查询参数
```
POST /check/staff?documentNo=D00001/1&line=3
```

### 2. 替代方法：使用请求体
```
POST /check/item
Content-Type: application/json

{
  "documentNo": "D00001/1",
  "line": 3,
  "staffId": 1
}
```

### 3. 不推荐方法：URL编码
```
POST /check/staff/D00001%2F1/3
```

## 异常处理规范

1. 使用全局异常过滤器捕获所有异常
2. 对已知的业务异常，返回适当的HTTP状态码和错误信息
3. 对未预期的异常，返回500状态码，并记录详细错误日志
4. 敏感的错误信息不应返回给客户端
5. 数据验证错误应返回400状态码和详细的验证错误信息
