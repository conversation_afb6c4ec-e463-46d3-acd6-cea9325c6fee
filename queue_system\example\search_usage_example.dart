import 'package:flutter/material.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';
import 'package:queue_system/utils/search_utils.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/l10n/app_localizations.dart';

/// 搜索功能使用示例
///
/// 这个示例展示了如何在页面中集成搜索功能
class SearchUsageExample extends StatefulWidget {
  final List<CheckItem> items;

  const SearchUsageExample({
    super.key,
    required this.items,
  });

  @override
  State<SearchUsageExample> createState() => _SearchUsageExampleState();
}

class _SearchUsageExampleState extends State<SearchUsageExample> {
  // 搜索相关状态
  String _searchQuery = '';
  List<CheckItem> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _updateFilteredItems();
  }

  @override
  void didUpdateWidget(SearchUsageExample oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _updateFilteredItems();
    }
  }

  // 更新过滤后的项目列表
  void _updateFilteredItems() {
    setState(() {
      _filteredItems = SearchUtils.searchCheckItems(widget.items, _searchQuery);
    });
  }

  // 处理搜索查询变化
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _updateFilteredItems();
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    setState(() {
      _searchQuery = '';
      _updateFilteredItems();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('搜索功能示例'),
      ),
      body: Column(
        children: [
          // 搜索栏
          SearchBarWidget(
            hintText: context.t('search_documents'),
            onSearchChanged: _onSearchChanged,
            onClear: _onSearchCleared,
            initialValue: _searchQuery,
          ),

          // 显示搜索结果统计
          if (_searchQuery.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Icon(Icons.info_outline,
                      size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 8),
                  Text(
                    '${context.t('search_results')}: ${_filteredItems.length} / ${widget.items.length}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

          // 项目列表
          Expanded(child: _buildItemsList()),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    // 使用过滤后的项目列表
    final itemsToShow = _filteredItems.isNotEmpty || _searchQuery.isNotEmpty
        ? _filteredItems
        : widget.items;

    // 如果搜索后没有结果，显示无结果提示
    if (_searchQuery.isNotEmpty && _filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              context.t('no_search_results'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              '${context.t('search_keyword')}: "$_searchQuery"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    if (itemsToShow.isEmpty) {
      return const Center(
        child: Text('没有数据'),
      );
    }

    return ListView.builder(
      itemCount: itemsToShow.length,
      itemBuilder: (context, index) {
        final item = itemsToShow[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            title: Text(item.documentNo),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('客户: ${item.customerName}'),
                Text('库存: ${item.stock}'),
                Text('描述: ${item.description}'),
                if (item.binShelfNo.isNotEmpty) Text('货架: ${item.binShelfNo}'),
              ],
            ),
            trailing: Text(
              '数量: ${item.quantity}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        );
      },
    );
  }
}

/// 搜索功能集成步骤：
/// 
/// 1. 添加搜索状态变量：
///    ```dart
///    String _searchQuery = '';
///    List<ItemType> _filteredItems = [];
///    ```
/// 
/// 2. 实现搜索方法：
///    ```dart
///    void _onSearchChanged(String query) {
///      setState(() {
///        _searchQuery = query;
///        _updateFilteredItems();
///      });
///    }
///    
///    void _updateFilteredItems() {
///      setState(() {
///        _filteredItems = SearchUtils.searchCheckItems(widget.items, _searchQuery);
///      });
///    }
///    ```
/// 
/// 3. 在UI中添加搜索栏：
///    ```dart
///    SearchBarWidget(
///      hintText: context.t('search_documents'),
///      onSearchChanged: _onSearchChanged,
///      onClear: _onSearchCleared,
///      initialValue: _searchQuery,
///    )
///    ```
/// 
/// 4. 使用过滤后的数据：
///    ```dart
///    final itemsToShow = _filteredItems.isNotEmpty || _searchQuery.isNotEmpty 
///        ? _filteredItems 
///        : widget.items;
///    ```
/// 
/// 5. 处理无搜索结果的情况：
///    ```dart
///    if (_searchQuery.isNotEmpty && _filteredItems.isEmpty) {
///      return Center(child: Text('无搜索结果'));
///    }
///    ```
