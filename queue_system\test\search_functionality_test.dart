import 'package:flutter_test/flutter_test.dart';
import 'package:queue_system/utils/search_utils.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/models/shipping_document.dart';

void main() {
  group('SearchUtils Tests', () {
    late List<CheckItem> testCheckItems;
    late List<ShippingDocument> testShippingDocuments;

    setUp(() {
      // 创建测试用的CheckItem数据
      testCheckItems = [
        CheckItem(
          documentNo: 'D00001',
          line: 1,
          stock: 'ABC123',
          description: 'Test Product 1',
          quantity: 10,
          uom: 'PCS',
          binShelfNo: 'A1-01',
          staffChecked: false,
          supervisorChecked: false,
          bomSpecialistChecked: false,
          documentDate: DateTime(2024, 1, 1),
          createdAt: DateTime(2024, 1, 1),
          customer: 'CUST001',
          customerName: 'Customer One',
        ),
        CheckItem(
          documentNo: 'D00002',
          line: 1,
          stock: 'XYZ789',
          description: 'Another Product',
          quantity: 5,
          uom: 'PCS',
          binShelfNo: 'B2-05',
          staffChecked: false,
          supervisorChecked: false,
          bomSpecialistChecked: false,
          documentDate: DateTime(2024, 1, 2),
          createdAt: DateTime(2024, 1, 2),
          customer: 'CUST002',
          customerName: 'Customer Two',
        ),
      ];

      // 创建测试用的ShippingDocument数据
      testShippingDocuments = [
        ShippingDocument(
          id: 1,
          documentNo: 'D00001',
          documentDate: DateTime(2024, 1, 1),
          customerCode: 'CUST001',
          customerName: 'Customer One',
          customerEmail: '<EMAIL>',
          salesmanCode: 'SALES001',
          issueBy: 'John Doe',
          transporterName: 'Express Transport',
          remarks: 'Test remarks',
          createdAt: DateTime(2024, 1, 1),
          isShipped: false,
          delivered: false,
          driverUsername: 'driver1',
        ),
        ShippingDocument(
          id: 2,
          documentNo: 'D00002',
          documentDate: DateTime(2024, 1, 2),
          customerCode: 'CUST002',
          customerName: 'Customer Two',
          customerEmail: '<EMAIL>',
          salesmanCode: 'SALES002',
          issueBy: 'Jane Smith',
          transporterName: 'Fast Delivery',
          remarks: 'Another test',
          createdAt: DateTime(2024, 1, 2),
          isShipped: false,
          delivered: false,
          driverUsername: 'driver2',
        ),
      ];
    });

    test('searchCheckItems - 空查询返回所有项目', () {
      final result = SearchUtils.searchCheckItems(testCheckItems, '');
      expect(result.length, equals(testCheckItems.length));
    });

    test('searchCheckItems - 按单据编号搜索', () {
      final result = SearchUtils.searchCheckItems(testCheckItems, 'D00001');
      expect(result.length, equals(1));
      expect(result.first.documentNo, equals('D00001'));
    });

    test('searchCheckItems - 按库存编码搜索', () {
      final result = SearchUtils.searchCheckItems(testCheckItems, 'ABC123');
      expect(result.length, equals(1));
      expect(result.first.stock, equals('ABC123'));
    });

    test('searchCheckItems - 按客户名称搜索', () {
      final result =
          SearchUtils.searchCheckItems(testCheckItems, 'Customer One');
      expect(result.length, equals(1));
      expect(result.first.customerName, equals('Customer One'));
    });

    test('searchCheckItems - 按描述搜索', () {
      final result =
          SearchUtils.searchCheckItems(testCheckItems, 'Test Product');
      expect(result.length, equals(1));
      expect(result.first.description, equals('Test Product 1'));
    });

    test('searchCheckItems - 按货架位置搜索', () {
      final result = SearchUtils.searchCheckItems(testCheckItems, 'A1-01');
      expect(result.length, equals(1));
      expect(result.first.binShelfNo, equals('A1-01'));
    });

    test('searchCheckItems - 不区分大小写搜索', () {
      final result = SearchUtils.searchCheckItems(testCheckItems, 'abc123');
      expect(result.length, equals(1));
      expect(result.first.stock, equals('ABC123'));
    });

    test('searchCheckItems - 没有匹配结果', () {
      final result = SearchUtils.searchCheckItems(testCheckItems, 'NOTFOUND');
      expect(result.length, equals(0));
    });

    test('searchShippingDocuments - 空查询返回所有文档', () {
      final result =
          SearchUtils.searchShippingDocuments(testShippingDocuments, '');
      expect(result.length, equals(testShippingDocuments.length));
    });

    test('searchShippingDocuments - 按单据编号搜索', () {
      final result =
          SearchUtils.searchShippingDocuments(testShippingDocuments, 'D00001');
      expect(result.length, equals(1));
      expect(result.first.documentNo, equals('D00001'));
    });

    test('searchShippingDocuments - 按客户编码搜索', () {
      final result =
          SearchUtils.searchShippingDocuments(testShippingDocuments, 'CUST001');
      expect(result.length, equals(1));
      expect(result.first.customerCode, equals('CUST001'));
    });

    test('searchShippingDocuments - 按客户名称搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'Customer Two');
      expect(result.length, equals(1));
      expect(result.first.customerName, equals('Customer Two'));
    });

    test('searchShippingDocuments - 按备注搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'Test remarks');
      expect(result.length, equals(1));
      expect(result.first.remarks, equals('Test remarks'));
    });

    test('searchShippingDocuments - 不区分大小写搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'customer one');
      expect(result.length, equals(1));
      expect(result.first.customerName, equals('Customer One'));
    });

    test('searchShippingDocuments - 没有匹配结果', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'NOTFOUND');
      expect(result.length, equals(0));
    });

    test('searchShippingDocuments - 按邮箱搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, '<EMAIL>');
      expect(result.length, equals(1));
      expect(result.first.customerEmail, equals('<EMAIL>'));
    });

    test('searchShippingDocuments - 按销售员编码搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'SALES002');
      expect(result.length, equals(1));
      expect(result.first.salesmanCode, equals('SALES002'));
    });

    test('searchShippingDocuments - 按发行人搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'John Doe');
      expect(result.length, equals(1));
      expect(result.first.issueBy, equals('John Doe'));
    });

    test('searchShippingDocuments - 按司机用户名搜索', () {
      final result =
          SearchUtils.searchShippingDocuments(testShippingDocuments, 'driver2');
      expect(result.length, equals(1));
      expect(result.first.driverUsername, equals('driver2'));
    });

    test('searchShippingDocuments - 按运输商名称搜索', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, 'Express Transport');
      expect(result.length, equals(1));
      expect(result.first.transporterName, equals('Express Transport'));
    });

    test('searchShippingDocuments - PDF搜索字段不区分大小写', () {
      final result = SearchUtils.searchShippingDocuments(
          testShippingDocuments, '<EMAIL>');
      expect(result.length, equals(1));
      expect(result.first.customerEmail, equals('<EMAIL>'));
    });
  });
}
