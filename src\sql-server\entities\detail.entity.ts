import { Entity, Column, PrimaryC<PERSON>umn, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('AR_DO_Detail_tbl')
export class SqlServerDetail {
  @ApiProperty({ description: '文档编号', example: 'DO123456' })
  @Column({ name: 'Document No', primary: true })
  DocumentNo: string;

  @ApiProperty({ description: '行号', example: 1 })
  @Column({ name: 'Line', primary: true })
  Line: number;

  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @Column({ name: 'Stock' })
  Stock: string;

  @ApiProperty({ description: '产品描述', example: '办公用品' })
  @Column({ name: 'Description' })
  Description: string;

  @ApiProperty({ description: '数量', example: 10 })
  @Column({ name: 'Quantity' })
  Quantity: number;

  @ApiProperty({ description: '计量单位', example: 'PCS' })
  @Column({ name: 'UOM' })
  UOM: string;

  @ApiProperty({ description: 'BOM标识', example: 'Y', nullable: true })
  @Column({ name: 'BOM', nullable: true })
  BOM: string;

  @ApiProperty({ description: '生产编号', example: 'D00001/1', nullable: true })
  @Column({ name: 'Production No', nullable: true })
  ProductionNo: string;

  @ApiProperty({ description: '品牌代码', example: 'BRAND001', nullable: true })
  @Column({ name: 'Brand Code', nullable: true })
  BrandCode: string;

  @ApiProperty({ description: '单价', example: 10.5, nullable: true })
  @Column({ name: 'Unit Price', nullable: true })
  UnitPrice: number;

  @ApiProperty({ description: '总金额', example: 105, nullable: true })
  @Column({ name: 'Total Amount', nullable: true })
  TotalAmount: number;

  @ApiProperty({ description: '零件号', example: 'PART001', nullable: true })
  @Column({ name: 'Part No', nullable: true })
  PartNo: string;

  @ManyToOne('SqlServerMain', 'details')
  @JoinColumn({ name: 'Document No', referencedColumnName: 'DocumentNo' })
  main: any;
}