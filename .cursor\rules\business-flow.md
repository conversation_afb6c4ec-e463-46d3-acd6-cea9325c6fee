---
title: "业务流程说明"
description: "队列系统的核心业务流程和规则"
version: "1.0.0"
last_updated: "2025-05-19"
---

# 业务流程说明

## 核心业务流程

### 1. 员工角色与权限
系统定义了四种员工角色：
- **普通员工(regular)**: 只能检查自己负责楼层的货物
- **BOM管理员(staff_bom)**: 拥有与主管相同的楼层权限，负责BOM物品的二次检查
- **司机(driver)**: 拥有与主管相同的楼层权限，负责送货和确认送达
- **主管(senior)**: 可以检查所有楼层的货物，验证员工检查，确认出货

### 2. 货物检查流程
- **非BOM物品**:
  1. 普通员工检查(staff_checked = true)
  2. 主管确认(supervisor_checked = true)

- **BOM物品**:
  1. 普通员工初步检查(staff_checked = true)
  2. BOM管理员二次检查(bom_specialist_checked = true)
  3. 主管确认(supervisor_checked = true)

- **特殊情况**:
  - 对于bin_shelf_no为空的记录，跳过普通员工检查，直接由主管确认
  - 对于have_stock=false的记录，同样跳过普通员工检查，直接由主管确认
  - 这些特殊记录在主管的待确认列表中会有特殊标识

### 3. 订单出货流程
1. 检查订单是否可以出货(所有物品都已被主管确认)
2. 标记订单为已出货(is_shipped = true)，可选择指派司机
3. 若指派司机，等待司机确认送达(delivered = true)
4. 若未指派司机，订单在出货后直接完成

### 4. 拒绝检查流程
1. 主管或司机拒绝员工的检查(staff_checked = false)
2. 系统生成通知给相关员工
3. 员工收到通知，重新检查货物
4. 员工再次提交检查结果

## 楼层权限控制

### 货架位置编码格式
`楼层-区域-货架-位置`，例如 `2-1-G001-1`

### 楼层权限规则
- 楼层格式限定为: `1F`, `2F`, `3F`
- 主管、司机和BOM管理员可以使用`ALL`值表示有权操作所有楼层
- 系统根据货架位置编码第一个数字识别楼层

### 权限判断逻辑
- 如果用户的floor为"2F"，则只能操作bin_shelf_no以"2-"开头的货物
- 如果用户的floor为"ALL"，则可以操作所有楼层的货物

## 数据同步流程

### 同步机制
- 系统基于DocumentNo顺序进行同步(不基于时间)
- 系统启动时检查PostgreSQL中最新记录的DocumentNo
- 所有大于最后同步DocumentNo的记录都将被同步

### 同步过滤规则
- 系统不会跳过货架号(bin_shelf_no)为空或空白的记录，而是将它们保存到PostgreSQL中
- 对于bin_shelf_no为空的记录，系统会将它们直接分配给主管(Supervisor)处理，无需经过普通员工检查
- 这些记录会在日志中记录警告信息，但不会影响其他记录的同步
- 在前端UI中，这些记录会显示为"未分配货架"并使用橙色标记

### 自动同步
- 系统每分钟自动检查一次SQL Server中的新记录
- 所有日期操作都考虑时区差异(马来西亚时区UTC+8)

### 处理特殊格式
- 支持Document No格式如"D00001/1"，表示BOM分组
- BOM物品会有parent_code字段，非BOM物品该字段为NULL

## 特殊记录处理

### 1. 空货架位置(bin_shelf_no)记录处理
- 当bin_shelf_no为空或null时，系统将其统一处理为空字符串('')
- 这些记录会被正确保存到PostgreSQL数据库中
- 系统会自动将这些记录直接分配给主管(Supervisor)处理，跳过普通员工检查流程
- 主管可以在待确认列表中看到这些记录，并直接进行确认操作
- 前端UI中，这些记录的货架位置会显示为"未分配货架"并使用橙色标记

### 2. 无库存(have_stock=false)记录处理
- 当库存数量为0或找不到库存信息时，系统会将have_stock设置为false
- 这些记录同样会直接分配给主管处理，跳过普通员工检查流程
- 前端UI中，这些记录会显示"No Stock"标签

### 3. 权限处理特例
- 对于bin_shelf_no为空的记录，系统会跳过楼层权限检查
- 主管、司机和BOM管理员可以处理所有这类记录
- 普通员工不会看到这些记录，因为它们直接分配给主管处理
