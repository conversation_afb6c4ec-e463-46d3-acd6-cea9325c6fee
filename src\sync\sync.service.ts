import { Injectable, Logger, OnModuleInit, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON>Than, Equal, In, Not, Like, DataSource } from 'typeorm';
import { SqlServerMain } from '../sql-server/entities/main.entity';
import { SqlServerDetail } from '../sql-server/entities/detail.entity';
import { SqlServerScTranDetail } from '../sql-server/entities/sc-tran-detail.entity';
import { SqlServerStock } from '../stock/entities/stock.entity';
import { SqlServerCustomer } from '../sql-server/entities/customer.entity';
import { SqlServerCnMain } from '../sql-server/entities/cn-main.entity';
import { SqlServerCnDetail } from '../sql-server/entities/cn-detail.entity';
import { Main } from '../postgres/entities/main.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { CnMain } from '../postgres/entities/cn-main.entity';
import { CnDetail } from '../postgres/entities/cn-detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { Interval } from '@nestjs/schedule';
import { NotificationService } from '../notification/notification.service';
import { StockReturnTrackingService } from '../stock/stock-return-tracking.service';

@Injectable()
export class SyncService implements OnModuleInit {
  private readonly logger = new Logger(SyncService.name);
  private lastSyncedDocNo: string = '';
  private isEmptyDatabase: boolean = false;

  private lastSyncedCnDocNo: string = '';
  private isEmptyCnDatabase: boolean = false;

  constructor(
    @InjectRepository(SqlServerMain, 'sqlServerConnection')
    private sqlServerMainRepository: Repository<SqlServerMain>,
    @InjectRepository(SqlServerDetail, 'sqlServerConnection')
    private sqlServerDetailRepository: Repository<SqlServerDetail>,
    @InjectRepository(SqlServerScTranDetail, 'sqlServerConnection')
    private sqlServerScTranDetailRepository: Repository<SqlServerScTranDetail>,
    @InjectRepository(SqlServerStock, 'sqlServerConnection')
    private sqlServerStockRepository: Repository<SqlServerStock>,
    @InjectRepository(SqlServerCustomer, 'sqlServerConnection')
    private sqlServerCustomerRepository: Repository<SqlServerCustomer>,
    @InjectRepository(SqlServerCnMain, 'sqlServerConnection')
    private sqlServerCnMainRepository: Repository<SqlServerCnMain>,
    @InjectRepository(SqlServerCnDetail, 'sqlServerConnection')
    private sqlServerCnDetailRepository: Repository<SqlServerCnDetail>,
    @InjectRepository(Main, 'postgresConnection')
    private postgresMainRepository: Repository<Main>,
    @InjectRepository(Detail, 'postgresConnection')
    private postgresDetailRepository: Repository<Detail>,
    @InjectRepository(CnMain, 'postgresConnection')
    private postgresCnMainRepository: Repository<CnMain>,
    @InjectRepository(CnDetail, 'postgresConnection')
    private postgresCnDetailRepository: Repository<CnDetail>,
    @InjectRepository(Staff, 'postgresConnection')
    private staffRepository: Repository<Staff>,
    private notificationService: NotificationService,
    private stockReturnTrackingService: StockReturnTrackingService,
  ) {
    this.logger.log(`Sync service initialized`);
  }

  // 格式化日期为 YYYY-MM-DD 格式
  private formatDate(date: Date): string {
    return date.getFullYear() + '-' +
      String(date.getMonth() + 1).padStart(2, '0') + '-' +
      String(date.getDate()).padStart(2, '0');
  }

  async onModuleInit() {
    try {
      // 检查PostgreSQL中是否已有delivery_order_main记录
      const latestRecord = await this.postgresMainRepository.findOne({
        where: {},
        order: { document_no: 'DESC' },
      });

      if (latestRecord) {
        // 如果数据库中已有记录，使用最新记录的DocumentNo作为同步起点
        this.lastSyncedDocNo = latestRecord.document_no;
        this.logger.log(`Found existing delivery_order_main record, last synced DocumentNo: ${this.lastSyncedDocNo}`);
      } else {
        // 如果是空数据库,设置标志但不设置lastSyncedDocNo
        this.logger.log('Empty delivery_order_main database detected, will sync the latest record');
        this.isEmptyDatabase = true;
      }

      // 检查PostgreSQL中是否已有cn_main记录
      const latestCnRecord = await this.postgresCnMainRepository.findOne({
        where: {},
        order: { document_no: 'DESC' },
      });

      if (latestCnRecord) {
        // 如果数据库中已有记录，使用最新记录的DocumentNo作为同步起点
        this.lastSyncedCnDocNo = latestCnRecord.document_no;
        this.logger.log(`Found existing cn_main record, last synced DocumentNo: ${this.lastSyncedCnDocNo}`);
      } else {
        // 如果是空数据库,设置标志但不设置lastSyncedCnDocNo
        this.logger.log('Empty cn_main database detected, will sync the latest record');
        this.isEmptyCnDatabase = true;
      }

      // 启动时运行同步
      await this.syncData();
      await this.syncCnData();
    } catch (error) {
      this.logger.error('Error during initialization', error.stack);
    }
  }

  @Interval(60000) // 每分钟运行一次同步
  async scheduledSync() {
    this.logger.log('Starting scheduled sync...');
    await this.syncData();
    await this.syncCnData();
    this.logger.log('Scheduled sync completed');
  }

  // 同步CN表数据
  async syncCnData(): Promise<void> {
    try {
      this.logger.log('Starting CN data synchronization');
      this.logger.log(`Current last synced CN DocumentNo: ${this.lastSyncedCnDocNo || 'None'}`);
      this.logger.log(`Is empty CN database: ${this.isEmptyCnDatabase}`);

      // 获取当前日期，只保留日期部分
      const now = new Date();
      const malaysiaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
      const today = new Date(malaysiaTime);
      today.setHours(0, 0, 0, 0);

      let cnMainRecords = [];

      if (this.isEmptyCnDatabase) {
        // 如果数据库为空，只同步最后一笔Document No的记录
        this.logger.log(`Empty CN database, syncing only the latest record`);

        // 查询最新的一条记录
        const latestRecord = await this.sqlServerCnMainRepository
          .createQueryBuilder('main')
          .orderBy('main.[Document No]', 'DESC')
          .take(1)
          .getMany();

        cnMainRecords = latestRecord;
        this.logger.log(`Found ${cnMainRecords.length} latest CN record in SQL Server`);

        // 第一次同步后修改状态
        this.isEmptyCnDatabase = false;
      } else {
        // 如果数据库不为空，直接查询比lastSyncedCnDocNo更大的记录
        this.logger.log(`Normal sync mode, querying CN records newer than DocumentNo: ${this.lastSyncedCnDocNo}`);

        cnMainRecords = await this.sqlServerCnMainRepository
          .createQueryBuilder('main')
          .where('main.[Document No] > :lastDocNo', { lastDocNo: this.lastSyncedCnDocNo })
          .orderBy('main.[Document No]', 'ASC')
          .getMany();

        this.logger.log(`Found ${cnMainRecords.length} new CN records in SQL Server`);
      }

      // 获取已同步的记录，用于检测可能的重复数据
      const syncedRecords = await this.postgresCnMainRepository.find({
        select: ['document_no']
      });

      const syncedDocNos = new Set(syncedRecords.map(record => record.document_no));
      this.logger.log(`Found ${syncedDocNos.size} already synced CN records in PostgreSQL`);

      // 确保不会重复同步
      let recordsToSync = cnMainRecords.filter(record =>
        !syncedDocNos.has(record.DocumentNo)
      );

      this.logger.log(`Need to sync ${recordsToSync.length} CN records`);

      // 处理需要同步的记录
      for (const main of recordsToSync) {
        try {
          this.logger.log(`Processing CN main record: ${main.DocumentNo}, date: ${this.formatDate(main.DocumentDate)}`);

          // 在保存前再次检查是否存在相同document_no的记录
          const existingRecord = await this.postgresCnMainRepository.findOne({
            where: { document_no: main.DocumentNo }
          });

          // 声明变量以便在条件分支外部使用
          let savedMain: CnMain;

          if (existingRecord) {
            this.logger.log(`CN Record with DocumentNo ${main.DocumentNo} already exists in database, using existing record`);

            // 使用已存在的记录继续处理
            savedMain = existingRecord;
            this.logger.log(`Using existing CN main record with DocumentNo: ${main.DocumentNo}, ID: ${savedMain.id}`);

            // 确保savedMain已经被赋值
            if (!savedMain) {
              this.logger.error(`Failed to get existing CN main record for DocumentNo: ${main.DocumentNo}, skipping details processing`);
              continue; // 跳过此记录的后续处理
            }
          } else {
            // 创建并保存CnMain记录
            const postgresCnMain = new CnMain();
            postgresCnMain.document_no = main.DocumentNo;
            postgresCnMain.document_date = main.DocumentDate;
            postgresCnMain.customer = main.Customer;
            postgresCnMain.salesman = main.Salesman;
            postgresCnMain.remarks = main.Remarks;
            postgresCnMain.terms = main.Terms;
            postgresCnMain.total_amount = main.TotalAmount;
            postgresCnMain.forex_code = main.ForexCode;

            try {
              savedMain = await this.postgresCnMainRepository.save(postgresCnMain);
              this.logger.log(`Inserted CN main record with DocumentNo: ${main.DocumentNo}, ID: ${savedMain.id}`);
            } catch (saveError) {
              // 处理可能的唯一约束冲突
              if (saveError.code === '23505') { // PostgreSQL唯一约束违反的错误代码
                this.logger.warn(`Duplicate key error detected for CN DocumentNo: ${main.DocumentNo}, error: ${saveError.message}, detail: ${saveError.detail || 'No detail'}, trying to fetch existing record`);

                // 尝试再次获取记录（可能是并发同步导致的）
                const retryRecord = await this.postgresCnMainRepository.findOne({
                  where: { document_no: main.DocumentNo }
                });

                if (retryRecord) {
                  savedMain = retryRecord;
                  this.logger.log(`Successfully retrieved existing CN record for DocumentNo: ${main.DocumentNo}, ID: ${savedMain.id}`);
                } else {
                  // 如果仍然找不到记录，则记录更详细的错误并抛出原始错误
                  this.logger.error(`Failed to retrieve existing CN record after duplicate key error for DocumentNo: ${main.DocumentNo}, original error: ${saveError.message}, stack: ${saveError.stack}`);
                  throw saveError;
                }
              } else {
                // 如果是其他类型的错误，重新抛出
                throw saveError;
              }
            }

            // 确保savedMain已经被赋值
            if (!savedMain) {
              this.logger.error(`Failed to get or create CN main record for DocumentNo: ${main.DocumentNo}, skipping details processing`);
              continue; // 跳过此记录的后续处理
            }
          }

          try {
            // 查询AR_CN_Stock_Detail表中的记录
            const cnDetails = await this.sqlServerCnDetailRepository
              .createQueryBuilder('detail')
              .where('detail.[Document No] = :docNo', { docNo: main.DocumentNo })
              .orderBy('detail.Line')
              .getMany();

            this.logger.log(`Found ${cnDetails.length} AR_CN_Stock_Detail records for ${main.DocumentNo}`);

            // 处理AR_CN_Stock_Detail表中的记录
            for (const detail of cnDetails) {
              try {
                // 创建并保存CnDetail记录
                const postgresCnDetail = new CnDetail();
                postgresCnDetail.document_no = detail.DocumentNo;
                postgresCnDetail.main_id = savedMain.id;
                postgresCnDetail.line = detail.Line;
                postgresCnDetail.stock = detail.Stock;
                postgresCnDetail.description = detail.Description || '';
                postgresCnDetail.quantity = Math.abs(detail.Quantity || 0);
                postgresCnDetail.uom = detail.UOM || '';
                postgresCnDetail.unit_price = detail.UnitPrice;
                postgresCnDetail.total_amount = detail.TotalAmount;

                try {
                  const savedDetail = await this.postgresCnDetailRepository.save(postgresCnDetail);
                  if (savedDetail && savedDetail.id) {
                    this.logger.log(`Inserted CN detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}, Main ID: ${savedMain.id}, Detail ID: ${savedDetail.id}`);
                  } else {
                    this.logger.warn(`Failed to insert CN detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}`);
                  }
                } catch (detailSaveError) {
                  this.logger.error(`Error saving CN detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}:`, detailSaveError.stack);
                }
              } catch (detailError) {
                this.logger.error(`Error processing AR_CN_Stock_Detail record for ${detail.DocumentNo}, Line: ${detail.Line}:`, detailError.stack);
              }
            }
          } catch (detailsError) {
            this.logger.error(`Error fetching CN details for ${main.DocumentNo}:`, detailsError.stack);
          }

          // 检查是否成功插入了Detail记录
          const detailCount = await this.postgresCnDetailRepository.count({
            where: { main_id: savedMain.id }
          });

          if (detailCount === 0) {
            this.logger.warn(`Warning: No CN detail records were inserted for DocumentNo: ${main.DocumentNo}, Main ID: ${savedMain.id}. This may indicate a problem with the synchronization.`);
          } else {
            this.logger.log(`Successfully inserted ${detailCount} CN detail records for DocumentNo: ${main.DocumentNo}, Main ID: ${savedMain.id}`);
          }

          // 更新最后同步的DocumentNo
          if (main.DocumentNo > this.lastSyncedCnDocNo) {
            this.lastSyncedCnDocNo = main.DocumentNo;
            this.logger.log(`Updated last synced CN DocumentNo to: ${this.lastSyncedCnDocNo}`);
          }
        } catch (recordError) {
          this.logger.error(`Error processing CN record: ${main.DocumentNo}`, recordError.stack);
        }
      }

      this.logger.log(`CN data synchronization completed. Last synced CN DocumentNo: ${this.lastSyncedCnDocNo}`);
    } catch (error) {
      this.logger.error('Error during CN data synchronization', error.stack);
    }
  }

  // 从DocumentNo (如 D00001/1) 中提取基本文档编号 (D00001)
  private extractBaseDocNo(documentNo: string): string {
    if (documentNo && documentNo.includes('/')) {
      return documentNo.split('/')[0];
    }
    return documentNo;
  }

  // 注释掉同步单个明细记录方法 - 2025-06-18
  /*
  async syncDetail(documentNo: string, line: number): Promise<any> {
    this.logger.log(`Starting detail synchronization for documentNo: ${documentNo}, line: ${line}`);
  
    try {
      // 1. 查找PostgreSQL中的记录
      const pgDetail = await this.postgresDetailRepository.findOne({
        where: { document_no: documentNo, line: line }
      });
  
      if (!pgDetail) {
        throw new NotFoundException(`Detail record not found in PostgreSQL: ${documentNo}, line: ${line}`);
      }
  
      this.logger.log(`Found PostgreSQL detail record: ${pgDetail.id}, stock: ${pgDetail.stock}`);
  
      // 2. 查找SQL Server中的记录
      let sqlDetail;
      let changes = {
        quantityChanged: false,
        oldQuantity: pgDetail.quantity,
        newQuantity: pgDetail.quantity,
        isRemoved: false
      };
  
      // 检查是否是BOM子项记录（有parent_code的是BOM子项）
      const isBomChild = pgDetail.parent_code !== null;
  
      if (isBomChild) {
        // 如果是BOM子项记录，从SC_Tran_Detail表查询
        sqlDetail = await this.sqlServerScTranDetailRepository.findOne({
          where: { DocumentNo: documentNo, Line: line }
        });
  
        if (sqlDetail) {
          this.logger.log(`Found SQL Server SC_Tran_Detail record: ${sqlDetail.DocumentNo}, line: ${sqlDetail.Line}`);
  
          // 检查数量是否变化
          if (sqlDetail.Quantity !== pgDetail.quantity) {
            changes.quantityChanged = true;
            changes.newQuantity = sqlDetail.Quantity;
          }
        } else {
          this.logger.log(`SQL Server SC_Tran_Detail record not found, marking as removed`);
          changes.isRemoved = true;
        }
      } else {
        // 如果不是BOM子项记录，从AR_DO_Detail_tbl表查询（包括普通记录和BOM父项）
        sqlDetail = await this.sqlServerDetailRepository.findOne({
          where: { DocumentNo: documentNo, Line: line }
        });
  
        if (sqlDetail) {
          this.logger.log(`Found SQL Server AR_DO_Detail_tbl record: ${sqlDetail.DocumentNo}, line: ${sqlDetail.Line}`);
  
          // 检查数量是否变化
          if (sqlDetail.Quantity !== pgDetail.quantity) {
            changes.quantityChanged = true;
            changes.newQuantity = sqlDetail.Quantity;
          }
        } else {
          this.logger.log(`SQL Server AR_DO_Detail_tbl record not found, marking as removed`);
          changes.isRemoved = true;
        }
      }
  
      // 3. 更新PostgreSQL记录
      if (sqlDetail && !changes.isRemoved) {
        let needUpdate = changes.quantityChanged;
  
        // 检查是否是BOM父项（通过检查AR_DO_Detail_tbl中的BOM字段）
        let isBomParent = false;
        if (!isBomChild) {
          // 只有非BOM子项的记录才可能是BOM父项
          // 检查sqlDetail是否来自AR_DO_Detail_tbl表（有BOM字段）
          if (sqlDetail && typeof sqlDetail.BOM !== 'undefined') {
            isBomParent = sqlDetail.BOM === 'Y';
            this.logger.log(`Checked BOM parent status for ${documentNo}, line ${line}: ${isBomParent} (BOM field: ${sqlDetail.BOM})`);
          } else {
            this.logger.log(`No BOM field found in sqlDetail for ${documentNo}, line ${line}, treating as regular record`);
          }
        }
  
        // 更新BOM相关字段
        if (pgDetail.is_bom_parent !== isBomParent) {
          pgDetail.is_bom_parent = isBomParent;
          needUpdate = true;
          this.logger.log(`Updated PostgreSQL detail is_bom_parent to ${isBomParent}`);
        }
  
        // 处理bom_parent_id字段
        if (isBomParent) {
          // BOM父项的bom_parent_id应该为null
          if (pgDetail.bom_parent_id !== null) {
            pgDetail.bom_parent_id = null;
            needUpdate = true;
            this.logger.log(`Updated PostgreSQL detail bom_parent_id to null for BOM parent`);
          }
        } else if (isBomChild && sqlDetail.ParentCode) {
          // BOM子项需要查找父项ID
          try {
            // 提取父项document_no
            const parentDocNo = this.extractBaseDocNo(documentNo);
  
            // 查找BOM父项
            const bomParent = await this.postgresDetailRepository.findOne({
              where: {
                document_no: parentDocNo,
                stock: sqlDetail.ParentCode,
                is_bom_parent: true
              }
            });
  
            const expectedParentId = bomParent ? bomParent.id : null;
  
            if (pgDetail.bom_parent_id !== expectedParentId) {
              pgDetail.bom_parent_id = expectedParentId;
              needUpdate = true;
              this.logger.log(`Updated PostgreSQL detail bom_parent_id to ${expectedParentId} for BOM child`);
  
              if (!bomParent) {
                this.logger.warn(`BOM parent not found for child ${documentNo}, parent_doc_no: ${parentDocNo}, parent_code: ${sqlDetail.ParentCode}`);
              }
            }
          } catch (parentSearchError) {
            this.logger.error(`Error searching for BOM parent for ${documentNo}, line ${line}:`, parentSearchError.message);
          }
        } else if (!isBomChild && !isBomParent) {
          // 普通记录的bom_parent_id应该为null
          if (pgDetail.bom_parent_id !== null) {
            pgDetail.bom_parent_id = null;
            needUpdate = true;
            this.logger.log(`Updated PostgreSQL detail bom_parent_id to null for regular stock`);
          }
        }
  
        // 如果是普通记录或BOM父项（从AR_DO_Detail_tbl表查询的），更新其他字段
        if (!isBomChild && 'BrandCode' in sqlDetail) {
          // 更新品牌代码
          if (pgDetail.brand_code !== sqlDetail.BrandCode) {
            pgDetail.brand_code = sqlDetail.BrandCode || null;
            needUpdate = true;
            this.logger.log(`Updated PostgreSQL detail brand_code to ${pgDetail.brand_code}`);
          }
  
          // 更新单价
          if (pgDetail.unit_price !== sqlDetail.UnitPrice) {
            pgDetail.unit_price = sqlDetail.UnitPrice || null;
            needUpdate = true;
            this.logger.log(`Updated PostgreSQL detail unit_price to ${pgDetail.unit_price}`);
          }
  
          // 更新总金额
          if (pgDetail.total_amount !== sqlDetail.TotalAmount) {
            pgDetail.total_amount = sqlDetail.TotalAmount || null;
            needUpdate = true;
            this.logger.log(`Updated PostgreSQL detail total_amount to ${pgDetail.total_amount}`);
          }
        }
  
        // 更新数量
        if (changes.quantityChanged) {
          pgDetail.quantity = Math.abs(changes.newQuantity || 0);
          this.logger.log(`Updated PostgreSQL detail quantity from ${changes.oldQuantity} to ${Math.abs(changes.newQuantity || 0)}`);
        }
  
        // 如果有任何字段需要更新，保存记录
        if (needUpdate) {
          await this.postgresDetailRepository.save(pgDetail);
          this.logger.log(`Saved updated PostgreSQL detail record: ${pgDetail.id}`);
        }
      }
  
      // 4. 确定是否需要库存归位
      const needStockReturn = changes.isRemoved ||
        (changes.quantityChanged && changes.newQuantity < changes.oldQuantity);
  
      // 5. 返回结果
      return {
        success: true,
        detail: pgDetail,
        changes,
        needStockReturn
      };
    } catch (error) {
      this.logger.error(`Error synchronizing detail: ${error.message}`, error.stack);
      throw error;
    }
  }
  */

  async syncData(): Promise<void> {
    try {
      this.logger.log('Starting data synchronization');
      this.logger.log(`Current last synced DocumentNo: ${this.lastSyncedDocNo || 'None'}`);
      this.logger.log(`Is empty database: ${this.isEmptyDatabase}`);

      // 创建BOM父项映射，用于关联子项
      let bomParentMap = new Map<string, { id: number, productionNo?: string }>();

      // 获取当前日期，只保留日期部分
      const now = new Date();
      const malaysiaTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
      const today = new Date(malaysiaTime);
      today.setHours(0, 0, 0, 0);

      // 调试输出SQL Server连接状态
      try {
        const anyRecord = await this.sqlServerMainRepository.findOne({
          where: {
            DocumentNo: Not(Like('NC%'))
          },
          order: { DocumentNo: 'DESC' }
        });
        if (anyRecord) {
          this.logger.log(`SQL Server connection test successful. Found record: ${anyRecord.DocumentNo}, date: ${this.formatDate(anyRecord.DocumentDate)}`);
        } else {
          this.logger.log('SQL Server connection test: No records found in database');
        }
      } catch (connErr) {
        this.logger.error('SQL Server connection test failed:', connErr.message);
      }

      let mainRecords = [];

      if (this.isEmptyDatabase) {
        // 如果数据库为空，只同步最后一笔Document No的记录
        this.logger.log(`Empty database, syncing only the latest record`);

        // 查询最新的一条记录，排除NC开头的DocumentNo
        const latestRecord = await this.sqlServerMainRepository
          .createQueryBuilder('main')
          .where('main.[Document No] NOT LIKE :ncPattern', { ncPattern: 'NC%' })
          .orderBy('main.[Document No]', 'DESC')
          .take(1)
          .getMany();

        mainRecords = latestRecord;
        this.logger.log(`Found ${mainRecords.length} latest record in SQL Server`);

        // 第一次同步后修改状态
        this.isEmptyDatabase = false;
      } else {
        // 如果数据库不为空，直接查询比lastSyncedDocNo更大的记录
        this.logger.log(`Normal sync mode, querying records newer than DocumentNo: ${this.lastSyncedDocNo}`);

        mainRecords = await this.sqlServerMainRepository
          .createQueryBuilder('main')
          .where('main.[Document No] > :lastDocNo', { lastDocNo: this.lastSyncedDocNo })
          .andWhere('main.[Document No] NOT LIKE :ncPattern', { ncPattern: 'NC%' })
          .orderBy('main.[Document No]', 'ASC')
          .getMany();

        this.logger.log(`Found ${mainRecords.length} new records in SQL Server`);
      }

      // 获取已同步的记录，用于检测可能的重复数据
      const syncedRecords = await this.postgresMainRepository.find({
        select: ['document_no']
      });

      const syncedDocNos = new Set(syncedRecords.map(record => record.document_no));
      this.logger.log(`Found ${syncedDocNos.size} already synced records in PostgreSQL`);

      // 确保不会重复同步，同时排除NC开头的记录
      let recordsToSync = mainRecords.filter(record =>
        !syncedDocNos.has(record.DocumentNo) &&
        !record.DocumentNo.startsWith('NC')
      );

      this.logger.log(`Need to sync ${recordsToSync.length} records`);

      // 处理需要同步的记录
      for (const main of recordsToSync) {
        try {
          this.logger.log(`Processing main record: ${main.DocumentNo}, date: ${this.formatDate(main.DocumentDate)}`);

          // 在保存前再次检查是否存在相同document_no的记录
          const existingRecord = await this.postgresMainRepository.findOne({
            where: { document_no: main.DocumentNo }
          });

          // 声明变量以便在条件分支外部使用
          let savedMain: Main;

          if (existingRecord) {
            this.logger.log(`Record with DocumentNo ${main.DocumentNo} already exists in database, using existing record`);

            // 使用已存在的记录继续处理
            savedMain = existingRecord;
            this.logger.log(`Using existing main record with DocumentNo: ${main.DocumentNo}, ID: ${savedMain.id}`);

            // 确保savedMain已经被赋值
            if (!savedMain) {
              this.logger.error(`Failed to get existing main record for DocumentNo: ${main.DocumentNo}, skipping details processing`);
              continue; // 跳过此记录的后续处理
            }
          } else {
            // 创建并保存Main记录
            const postgresMain = new Main();
            postgresMain.document_no = main.DocumentNo;
            postgresMain.document_date = main.DocumentDate;
            postgresMain.customer = main.Customer;
            postgresMain.customer_name = main.CustomerName;
            postgresMain.remarks = main.Remarks;
            postgresMain.transporter_code = main.TransporterCode;
            postgresMain.transporter_name = main.TransporterName;
            postgresMain.issue_by = main.IssueBy;
            postgresMain.po_no = main.PONo;

            // 查询客户信息
            try {
              const customerInfo = await this.sqlServerCustomerRepository.findOne({
                where: {
                  CustomerCode: main.Customer
                }
              });

              if (customerInfo) {
                // 同步客户邮箱
                if (customerInfo.CustomerEmail) {
                  postgresMain.customer_email = customerInfo.CustomerEmail;
                  this.logger.log(`Found customer email for ${main.Customer}: ${customerInfo.CustomerEmail}`);
                } else {
                  this.logger.log(`No customer email found for ${main.Customer}`);
                  postgresMain.customer_email = null;
                }

                // 同步客户送货地址
                if (customerInfo.CustomerDeliverAddress) {
                  postgresMain.customer_deliver_address = customerInfo.CustomerDeliverAddress;
                  this.logger.log(`Found customer deliver address for ${main.Customer}: ${customerInfo.CustomerDeliverAddress}`);
                }

                // 同步客户电话
                if (customerInfo.CustomerTelephone) {
                  postgresMain.customer_telephone = customerInfo.CustomerTelephone;
                  this.logger.log(`Found customer telephone for ${main.Customer}: ${customerInfo.CustomerTelephone}`);
                }

                // 同步客户传真
                if (customerInfo.CustomerFax) {
                  postgresMain.customer_fax = customerInfo.CustomerFax;
                  this.logger.log(`Found customer fax for ${main.Customer}: ${customerInfo.CustomerFax}`);
                }

                // 同步销售员代码
                if (customerInfo.SalesmanCode) {
                  postgresMain.salesman_code = customerInfo.SalesmanCode;
                  this.logger.log(`Found salesman code for ${main.Customer}: ${customerInfo.SalesmanCode}`);
                }

                // 同步付款条件
                if (customerInfo.Term) {
                  postgresMain.term = customerInfo.Term;
                  this.logger.log(`Found term for ${main.Customer}: ${customerInfo.Term}`);
                } else {
                  postgresMain.term = 'CASH'; // 默认值
                  this.logger.log(`No term found for ${main.Customer}, using default: CASH`);
                }
              } else {
                this.logger.log(`No customer info found for ${main.Customer}`);
                postgresMain.customer_email = null;
              }
            } catch (customerError) {
              this.logger.error(`Error fetching customer info for ${main.Customer}:`, customerError.stack);
              postgresMain.customer_email = null;
            }

            try {
              savedMain = await this.postgresMainRepository.save(postgresMain);
              this.logger.log(`Inserted main record with DocumentNo: ${main.DocumentNo}, ID: ${savedMain.id}`);
            } catch (saveError) {
              // 处理可能的唯一约束冲突
              if (saveError.code === '23505') { // PostgreSQL唯一约束违反的错误代码
                this.logger.warn(`Duplicate key error detected for DocumentNo: ${main.DocumentNo}, error: ${saveError.message}, detail: ${saveError.detail || 'No detail'}, trying to fetch existing record`);

                // 尝试再次获取记录（可能是并发同步导致的）
                const retryRecord = await this.postgresMainRepository.findOne({
                  where: { document_no: main.DocumentNo }
                });

                if (retryRecord) {
                  savedMain = retryRecord;
                  this.logger.log(`Successfully retrieved existing record for DocumentNo: ${main.DocumentNo}, ID: ${savedMain.id}`);
                } else {
                  // 如果仍然找不到记录，则记录更详细的错误并抛出原始错误
                  this.logger.error(`Failed to retrieve existing record after duplicate key error for DocumentNo: ${main.DocumentNo}, original error: ${saveError.message}, stack: ${saveError.stack}`);
                  throw saveError;
                }
              } else {
                // 如果是其他类型的错误，重新抛出
                throw saveError;
              }
            }

            // 确保savedMain已经被赋值
            if (!savedMain) {
              this.logger.error(`Failed to get or create main record for DocumentNo: ${main.DocumentNo}, skipping details processing`);
              continue; // 跳过此记录的后续处理
            }
          }

          try {
            // 先查询AR_DO_Detail_tbl表中的记录
            const arDoDetails = await this.sqlServerDetailRepository
              .createQueryBuilder('detail')
              .where('detail.[Document No] = :docNo', { docNo: main.DocumentNo })
              .orderBy('detail.Line')
              .getMany();

            this.logger.log(`Found ${arDoDetails.length} AR_DO_Detail records for ${main.DocumentNo}`);

            // 处理AR_DO_Detail_tbl表中的记录
            for (const detail of arDoDetails) {
              try {
                // 检查是否是NC开头的DocumentNo，如果是则跳过
                if (detail.DocumentNo.startsWith('NC')) {
                  this.logger.log(`Skipping NC record: ${detail.DocumentNo}`);
                  continue;
                }

                // 查找对应的库存信息
                let binShelfNo = '';
                let uom = '';
                let haveStock = false;
                try {
                  const stockInfo = await this.sqlServerStockRepository.findOne({
                    where: {
                      StockCode: detail.Stock
                    }
                  });

                  if (stockInfo) {
                    binShelfNo = stockInfo.BinShelfNo;
                    uom = stockInfo.UOM || detail.UOM || '';
                    // 检查库存数量是否大于0
                    haveStock = stockInfo.CurrentQuantity > 0;
                    this.logger.log(`Found bin/shelf info for stock ${detail.Stock}: ${binShelfNo}, UOM: ${uom}, CurrentQuantity: ${stockInfo.CurrentQuantity}, haveStock: ${haveStock}`);
                  } else {
                    this.logger.warn(`Stock info not found for: ${detail.Stock}`);
                    // 如果找不到库存信息，使用detail中的UOM
                    uom = detail.UOM || '';
                    haveStock = false;
                  }
                } catch (stockError) {
                  this.logger.error(`Error fetching stock info for ${detail.Stock}:`, stockError.stack);
                  // 如果出错，使用detail中的UOM
                  uom = detail.UOM || '';
                  haveStock = false;
                }

                // 即使库位为空也继续处理
                if (!binShelfNo || binShelfNo.trim() === '') {
                  this.logger.log(`Warning: Empty bin/shelf for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock} - continuing anyway`);
                  binShelfNo = ''; // 确保binShelfNo是空字符串而不是undefined
                }

                // 检查BOM字段，确定是普通stock还是BOM
                const isBom = detail.BOM === 'Y';
                this.logger.log(`Record ${detail.DocumentNo}, Line: ${detail.Line} is ${isBom ? 'BOM' : 'regular stock'}`);

                if (isBom) {
                  // 如果是BOM父项，保存父项记录
                  const postgresBomParent = new Detail();
                  postgresBomParent.document_no = detail.DocumentNo;
                  postgresBomParent.main_id = savedMain.id;
                  postgresBomParent.line = detail.Line;
                  postgresBomParent.stock = detail.Stock;
                  postgresBomParent.description = detail.Description || '';
                  postgresBomParent.part_no = detail.PartNo || null; // 添加零件号字段
                  postgresBomParent.quantity = Math.abs(detail.Quantity || 0);
                  postgresBomParent.uom = uom;
                  postgresBomParent.bin_shelf_no = binShelfNo;
                  postgresBomParent.parent_code = null; // BOM父项没有parent_code
                  postgresBomParent.brand_code = detail.BrandCode || null;
                  postgresBomParent.unit_price = detail.UnitPrice || null;
                  postgresBomParent.total_amount = detail.TotalAmount || null;
                  postgresBomParent.staff_checked = false;
                  postgresBomParent.supervisor_checked = false;
                  postgresBomParent.have_stock = haveStock;
                  postgresBomParent.is_bom_parent = true; // 标记为BOM父项
                  postgresBomParent.bom_parent_id = null;
                  postgresBomParent.bom_group = detail.DocumentNo; // 使用自身的document_no作为bom_group

                  try {
                    const savedBomParent = await this.postgresDetailRepository.save(postgresBomParent);
                    if (savedBomParent && savedBomParent.id) {
                      this.logger.log(`Inserted BOM parent record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}, Main ID: ${savedMain.id}, Detail ID: ${savedBomParent.id}`);

                      // 将BOM父项ID存储在Map中，以便后续处理子项时使用
                      // 使用更精确的键值：DocumentNo + Stock，这样可以区分同一文档下的不同BOM父项
                      if (!bomParentMap) {
                        bomParentMap = new Map<string, { id: number, productionNo?: string }>();
                      }
                      const bomParentKey = `${detail.DocumentNo}|${detail.Stock}`;
                      bomParentMap.set(bomParentKey, {
                        id: savedBomParent.id,
                        productionNo: detail.ProductionNo
                      });
                      this.logger.log(`Added BOM parent to map: key=${bomParentKey}, id=${savedBomParent.id}`);
                    } else {
                      this.logger.warn(`Possible issue with BOM parent insertion: No ID returned for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}`);
                    }
                  } catch (error) {
                    this.logger.error(`Error saving BOM parent record for ${detail.DocumentNo}:`, error.stack);
                  }
                } else {
                  // 如果是普通stock，直接同步到delivery_order_detail
                  const postgresDetail = new Detail();
                  postgresDetail.document_no = detail.DocumentNo;
                  postgresDetail.main_id = savedMain.id;
                  postgresDetail.line = detail.Line;
                  postgresDetail.stock = detail.Stock;
                  postgresDetail.description = detail.Description || '';
                  postgresDetail.part_no = detail.PartNo || null; // 添加零件号字段
                  postgresDetail.quantity = Math.abs(detail.Quantity || 0);
                  postgresDetail.uom = uom;
                  postgresDetail.bin_shelf_no = binShelfNo;
                  postgresDetail.parent_code = null; // 普通stock没有parent_code
                  postgresDetail.brand_code = detail.BrandCode || null; // 添加品牌代码
                  postgresDetail.unit_price = detail.UnitPrice || null; // 添加单价
                  postgresDetail.total_amount = detail.TotalAmount || null; // 添加总金额
                  postgresDetail.staff_checked = false;
                  postgresDetail.supervisor_checked = false;
                  postgresDetail.have_stock = haveStock;
                  postgresDetail.is_bom_parent = false; // 标记为非BOM父项
                  postgresDetail.bom_parent_id = null;
                  postgresDetail.bom_group = null; // 普通库存项没有bom_group

                  try {
                    const savedDetail = await this.postgresDetailRepository.save(postgresDetail);
                    if (savedDetail && savedDetail.id) {
                      this.logger.log(`Inserted regular stock detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}, Main ID: ${savedMain.id}, Detail ID: ${savedDetail.id}`);
                    } else {
                      this.logger.warn(`Possible issue with detail insertion: No ID returned for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}`);
                    }
                  } catch (saveDetailError) {
                    // 详细记录Detail保存错误
                    this.logger.error(`Failed to save detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.Stock}, Error: ${saveDetailError.message}, Code: ${saveDetailError.code || 'N/A'}, Detail: ${saveDetailError.detail || 'N/A'}`);

                    // 检查是否是唯一约束冲突
                    if (saveDetailError.code === '23505') {
                      this.logger.warn(`Duplicate key error for detail record: ${detail.DocumentNo}, Line: ${detail.Line}, trying to fetch existing detail`);

                      // 尝试获取已存在的记录
                      const existingDetail = await this.postgresDetailRepository.findOne({
                        where: {
                          document_no: detail.DocumentNo,
                          line: detail.Line
                        }
                      });

                      if (existingDetail) {
                        this.logger.log(`Found existing detail record: ID ${existingDetail.id}, DocumentNo: ${existingDetail.document_no}, Line: ${existingDetail.line}`);
                      } else {
                        this.logger.error(`Could not find existing detail record after duplicate key error: ${detail.DocumentNo}, Line: ${detail.Line}`);
                      }
                    }

                    // 继续处理其他记录，不抛出异常
                  }
                }
              } catch (detailError) {
                this.logger.error(`Error processing AR_DO_Detail record for ${detail.DocumentNo}, Line: ${detail.Line}:`, detailError.stack);
              }
            }

            // 对于BOM数据，使用SC_Tran_Detail表
            const bomDetails = arDoDetails.filter(d => d.BOM === 'Y');
            if (bomDetails.length > 0) {
              this.logger.log(`Found ${bomDetails.length} BOM records for ${main.DocumentNo}, processing SC_Tran_Detail records`);

              // 收集所有Production No，用于查询SC_Tran_Detail
              const productionNos = bomDetails
                .filter(d => d.ProductionNo && d.ProductionNo.trim() !== '')
                .map(d => d.ProductionNo);

              if (productionNos.length > 0) {
                this.logger.log(`Found ${productionNos.length} Production No values: ${productionNos.join(', ')}`);

                // 使用Production No精确匹配SC_Tran_Detail记录
                const scTranDetails = await this.sqlServerScTranDetailRepository
                  .createQueryBuilder('detail')
                  .where('detail.[Document No] IN (:...productionNos)', { productionNos })
                  .orderBy('detail.[Document No]')
                  .addOrderBy('detail.Line')
                  .getMany();

                this.logger.log(`Found ${scTranDetails.length} SC_Tran_Detail records matching Production No values`);

                // 处理每条SC_Tran_Detail记录
                for (const detail of scTranDetails) {
                  try {
                    // 查找对应的库存信息
                    let binShelfNo = '';
                    let uom = '';
                    let description = '';
                    let haveStock = false;
                    try {
                      const stockInfo = await this.sqlServerStockRepository.findOne({
                        where: {
                          StockCode: detail.StockCode
                        }
                      });

                      if (stockInfo) {
                        binShelfNo = stockInfo.BinShelfNo;
                        uom = stockInfo.UOM || '';
                        // 检查库存数量是否大于0
                        haveStock = stockInfo.CurrentQuantity > 0;
                        this.logger.log(`Found bin/shelf info for stock ${detail.StockCode}: ${binShelfNo}, UOM: ${uom}, CurrentQuantity: ${stockInfo.CurrentQuantity}, haveStock: ${haveStock}`);
                      } else {
                        this.logger.warn(`Stock info not found for: ${detail.StockCode}`);
                        haveStock = false;
                      }
                    } catch (stockError) {
                      this.logger.error(`Error fetching stock info for ${detail.StockCode}:`, stockError.stack);
                      haveStock = false;
                    }

                    // 尝试从AR_DO_Detail_tbl表获取Description和Part No
                    let partNo = null;
                    try {
                      const detailInfo = await this.sqlServerDetailRepository.findOne({
                        where: {
                          Stock: detail.StockCode
                        }
                      });

                      if (detailInfo) {
                        if (detailInfo.Description) {
                          description = detailInfo.Description;
                          this.logger.log(`Found description for stock ${detail.StockCode}: ${description}`);
                        } else {
                          this.logger.warn(`Description not found for stock: ${detail.StockCode}`);
                          description = detail.StockCode; // 使用StockCode作为fallback
                        }

                        if (detailInfo.PartNo) {
                          partNo = detailInfo.PartNo;
                          this.logger.log(`Found part number for stock ${detail.StockCode}: ${partNo}`);
                        }
                      } else {
                        this.logger.warn(`Detail info not found for stock: ${detail.StockCode}`);
                        description = detail.StockCode; // 使用StockCode作为fallback
                      }
                    } catch (descError) {
                      this.logger.error(`Error fetching detail info for ${detail.StockCode}:`, descError.stack);
                      description = detail.StockCode; // 使用StockCode作为fallback
                    }

                    // 即使库位为空也继续处理
                    if (!binShelfNo || binShelfNo.trim() === '') {
                      this.logger.log(`Warning: Empty bin/shelf for SC_Tran_Detail record DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode} - continuing anyway`);
                      binShelfNo = ''; // 确保binShelfNo是空字符串而不是undefined
                    }

                    // 记录日志
                    this.logger.log(`Processing BOM detail with DocumentNo: ${detail.DocumentNo}`);

                    const postgresDetail = new Detail();
                    postgresDetail.document_no = detail.DocumentNo; // 使用SC_Tran_Detail的原始DocumentNo (D00001/1)
                    postgresDetail.main_id = savedMain.id; // 设置关联的main记录ID
                    postgresDetail.line = detail.Line; // 使用原始Line值
                    postgresDetail.stock = detail.StockCode;
                    postgresDetail.description = description || ''; // 描述可能为空
                    postgresDetail.part_no = partNo; // 添加零件号字段
                    postgresDetail.quantity = Math.abs(detail.Quantity || 0);
                    postgresDetail.uom = uom;
                    postgresDetail.bin_shelf_no = binShelfNo;
                    postgresDetail.parent_code = detail.ParentCode; // 保存BOM的parent code
                    postgresDetail.brand_code = null; // BOM记录暂不处理品牌代码
                    postgresDetail.unit_price = 0; // BOM记录单价设为0
                    postgresDetail.total_amount = 0; // BOM记录总金额设为0
                    postgresDetail.staff_checked = false;
                    postgresDetail.supervisor_checked = false;
                    postgresDetail.have_stock = haveStock;
                    postgresDetail.is_bom_parent = false; // 标记为BOM子项

                    // 提取父项document_no (如从"D00001/1"提取"D00001")
                    const parentDocNo = this.extractBaseDocNo(detail.DocumentNo);
                    postgresDetail.bom_group = parentDocNo; // 设置BOM分组

                    // 设置父项ID（如果存在）
                    // 根据parent_code查找对应的BOM父项
                    let foundParentId = null;
                    for (const [key, value] of bomParentMap.entries()) {
                      const [docNo, stock] = key.split('|');
                      if (docNo === parentDocNo && stock === detail.ParentCode) {
                        foundParentId = value.id;
                        break;
                      }
                    }

                    if (foundParentId) {
                      postgresDetail.bom_parent_id = foundParentId;
                      this.logger.log(`Set BOM parent ID ${foundParentId} for child ${detail.DocumentNo} with parent_code ${detail.ParentCode}`);
                    } else {
                      this.logger.warn(`BOM parent not found for ${detail.DocumentNo}, parent_doc_no: ${parentDocNo}, parent_code: ${detail.ParentCode}`);
                    }

                    try {
                      const savedDetail = await this.postgresDetailRepository.save(postgresDetail);
                      if (savedDetail && savedDetail.id) {
                        this.logger.log(`Inserted BOM detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode}, Parent Code: ${detail.ParentCode || 'NULL'}, Main ID: ${savedMain.id}, Detail ID: ${savedDetail.id}`);
                      } else {
                        this.logger.warn(`Possible issue with BOM detail insertion: No ID returned for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode}`);
                      }
                    } catch (saveBomDetailError) {
                      // 详细记录BOM Detail保存错误
                      this.logger.error(`Failed to save BOM detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode}, Error: ${saveBomDetailError.message}, Code: ${saveBomDetailError.code || 'N/A'}, Detail: ${saveBomDetailError.detail || 'N/A'}`);

                      // 检查是否是唯一约束冲突
                      if (saveBomDetailError.code === '23505') {
                        this.logger.warn(`Duplicate key error for BOM detail record: ${detail.DocumentNo}, Line: ${detail.Line}, trying to fetch existing detail`);

                        // 尝试获取已存在的记录
                        const existingBomDetail = await this.postgresDetailRepository.findOne({
                          where: {
                            document_no: detail.DocumentNo,
                            line: detail.Line
                          }
                        });

                        if (existingBomDetail) {
                          this.logger.log(`Found existing BOM detail record: ID ${existingBomDetail.id}, DocumentNo: ${existingBomDetail.document_no}, Line: ${existingBomDetail.line}`);
                        } else {
                          this.logger.error(`Could not find existing BOM detail record after duplicate key error: ${detail.DocumentNo}, Line: ${detail.Line}`);
                        }
                      }

                      // 继续处理其他记录，不抛出异常
                    }
                  } catch (detailError) {
                    this.logger.error(`Error saving SC_Tran_Detail record for ${detail.DocumentNo}:`, detailError.stack);
                  }
                }
              } else {
                this.logger.warn(`No valid Production No values found for BOM records in ${main.DocumentNo}`);

                // 如果没有有效的Production No，回退到使用模糊查询
                this.logger.log(`Falling back to LIKE query for ${main.DocumentNo}`);
                const scTranDetails = await this.sqlServerScTranDetailRepository
                  .createQueryBuilder('detail')
                  .where('detail.[Document No] LIKE :docNo', { docNo: `${main.DocumentNo}/%` })
                  .orderBy('detail.[Document No]')
                  .addOrderBy('detail.Line')
                  .getMany();

                this.logger.log(`Found ${scTranDetails.length} SC_Tran_Detail records using fallback query`);

                // 处理每条SC_Tran_Detail记录
                for (const detail of scTranDetails) {
                  try {
                    // 查找对应的库存信息
                    let binShelfNo = '';
                    let uom = '';
                    let description = '';
                    let haveStock = false;
                    try {
                      const stockInfo = await this.sqlServerStockRepository.findOne({
                        where: {
                          StockCode: detail.StockCode
                        }
                      });

                      if (stockInfo) {
                        binShelfNo = stockInfo.BinShelfNo;
                        uom = stockInfo.UOM || '';
                        // 检查库存数量是否大于0
                        haveStock = stockInfo.CurrentQuantity > 0;
                        this.logger.log(`Found bin/shelf info for stock ${detail.StockCode}: ${binShelfNo}, UOM: ${uom}, CurrentQuantity: ${stockInfo.CurrentQuantity}, haveStock: ${haveStock}`);
                      } else {
                        this.logger.warn(`Stock info not found for: ${detail.StockCode}`);
                        haveStock = false;
                      }
                    } catch (stockError) {
                      this.logger.error(`Error fetching stock info for ${detail.StockCode}:`, stockError.stack);
                      haveStock = false;
                    }

                    // 尝试从AR_DO_Detail_tbl表获取Description和Part No
                    let partNo = null;
                    try {
                      const detailInfo = await this.sqlServerDetailRepository.findOne({
                        where: {
                          Stock: detail.StockCode
                        }
                      });

                      if (detailInfo) {
                        if (detailInfo.Description) {
                          description = detailInfo.Description;
                          this.logger.log(`Found description for stock ${detail.StockCode}: ${description} (fallback)`);
                        } else {
                          this.logger.warn(`Description not found for stock: ${detail.StockCode} (fallback)`);
                          description = detail.StockCode; // 使用StockCode作为fallback
                        }

                        if (detailInfo.PartNo) {
                          partNo = detailInfo.PartNo;
                          this.logger.log(`Found part number for stock ${detail.StockCode}: ${partNo} (fallback)`);
                        }
                      } else {
                        this.logger.warn(`Detail info not found for stock: ${detail.StockCode} (fallback)`);
                        description = detail.StockCode; // 使用StockCode作为fallback
                      }
                    } catch (descError) {
                      this.logger.error(`Error fetching detail info for ${detail.StockCode}:`, descError.stack);
                      description = detail.StockCode; // 使用StockCode作为fallback
                    }

                    // 即使库位为空也继续处理
                    if (!binShelfNo || binShelfNo.trim() === '') {
                      this.logger.log(`Warning: Empty bin/shelf for SC_Tran_Detail record (fallback) DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode} - continuing anyway`);
                      binShelfNo = ''; // 确保binShelfNo是空字符串而不是undefined
                    }

                    // 记录日志
                    this.logger.log(`Processing BOM detail (fallback) with DocumentNo: ${detail.DocumentNo}`);

                    const postgresDetail = new Detail();
                    postgresDetail.document_no = detail.DocumentNo;
                    postgresDetail.main_id = savedMain.id;
                    postgresDetail.line = detail.Line;
                    postgresDetail.stock = detail.StockCode;
                    postgresDetail.description = description || '';
                    postgresDetail.part_no = partNo; // 添加零件号字段
                    postgresDetail.quantity = Math.abs(detail.Quantity || 0);
                    postgresDetail.uom = uom;
                    postgresDetail.bin_shelf_no = binShelfNo;
                    postgresDetail.parent_code = detail.ParentCode;
                    postgresDetail.brand_code = null; // BOM记录暂不处理品牌代码
                    postgresDetail.unit_price = null; // BOM记录暂不处理单价
                    postgresDetail.total_amount = null; // BOM记录暂不处理总金额
                    postgresDetail.staff_checked = false;
                    postgresDetail.supervisor_checked = false;
                    postgresDetail.have_stock = haveStock;
                    postgresDetail.is_bom_parent = false; // 标记为BOM子项

                    // 提取父项document_no (如从"D00001/1"提取"D00001")
                    const parentDocNo = this.extractBaseDocNo(detail.DocumentNo);
                    postgresDetail.bom_group = parentDocNo; // 设置BOM分组

                    // 设置父项ID（如果存在）
                    // 根据parent_code查找对应的BOM父项
                    let foundParentId = null;
                    for (const [key, value] of bomParentMap.entries()) {
                      const [docNo, stock] = key.split('|');
                      if (docNo === parentDocNo && stock === detail.ParentCode) {
                        foundParentId = value.id;
                        break;
                      }
                    }

                    if (foundParentId) {
                      postgresDetail.bom_parent_id = foundParentId;
                      this.logger.log(`Set BOM parent ID ${foundParentId} for child ${detail.DocumentNo} with parent_code ${detail.ParentCode} (fallback)`);
                    } else {
                      this.logger.warn(`BOM parent not found for ${detail.DocumentNo}, parent_doc_no: ${parentDocNo}, parent_code: ${detail.ParentCode} (fallback)`);
                    }

                    try {
                      const savedDetail = await this.postgresDetailRepository.save(postgresDetail);
                      if (savedDetail && savedDetail.id) {
                        this.logger.log(`Inserted BOM detail record (fallback) for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode}, Parent Code: ${detail.ParentCode || 'NULL'}, Main ID: ${savedMain.id}, Detail ID: ${savedDetail.id}`);
                      } else {
                        this.logger.warn(`Possible issue with BOM fallback detail insertion: No ID returned for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode}`);
                      }
                    } catch (saveFallbackDetailError) {
                      // 详细记录BOM Fallback Detail保存错误
                      this.logger.error(`Failed to save BOM fallback detail record for DocumentNo: ${detail.DocumentNo}, Line: ${detail.Line}, Stock: ${detail.StockCode}, Error: ${saveFallbackDetailError.message}, Code: ${saveFallbackDetailError.code || 'N/A'}, Detail: ${saveFallbackDetailError.detail || 'N/A'}`);

                      // 检查是否是唯一约束冲突
                      if (saveFallbackDetailError.code === '23505') {
                        this.logger.warn(`Duplicate key error for BOM fallback detail record: ${detail.DocumentNo}, Line: ${detail.Line}, trying to fetch existing detail`);

                        // 尝试获取已存在的记录
                        const existingFallbackDetail = await this.postgresDetailRepository.findOne({
                          where: {
                            document_no: detail.DocumentNo,
                            line: detail.Line
                          }
                        });

                        if (existingFallbackDetail) {
                          this.logger.log(`Found existing BOM fallback detail record: ID ${existingFallbackDetail.id}, DocumentNo: ${existingFallbackDetail.document_no}, Line: ${existingFallbackDetail.line}`);
                        } else {
                          this.logger.error(`Could not find existing BOM fallback detail record after duplicate key error: ${detail.DocumentNo}, Line: ${detail.Line}`);
                        }
                      }

                      // 继续处理其他记录，不抛出异常
                    }
                  } catch (detailError) {
                    this.logger.error(`Error saving SC_Tran_Detail record for ${detail.DocumentNo}:`, detailError.stack);
                  }
                }
              }
            }
          } catch (detailsError) {
            this.logger.error(`Error fetching details for ${main.DocumentNo}:`, detailsError.stack);
          }

          // 检查是否成功插入了Detail记录
          const detailCount = await this.postgresDetailRepository.count({
            where: { main_id: savedMain.id }
          });

          if (detailCount === 0) {
            this.logger.warn(`Warning: No detail records were inserted for DocumentNo: ${main.DocumentNo}, Main ID: ${savedMain.id}. This may indicate a problem with the synchronization.`);
          } else {
            this.logger.log(`Successfully inserted ${detailCount} detail records for DocumentNo: ${main.DocumentNo}, Main ID: ${savedMain.id}`);
          }

          // 更新最后同步的DocumentNo
          if (main.DocumentNo > this.lastSyncedDocNo) {
            this.lastSyncedDocNo = main.DocumentNo;
            this.logger.log(`Updated last synced DocumentNo to: ${this.lastSyncedDocNo}`);
          }
        } catch (recordError) {
          this.logger.error(`Error processing record: ${main.DocumentNo}`, recordError.stack);
        }
      }

      this.logger.log(`Data synchronization completed. Last synced DocumentNo: ${this.lastSyncedDocNo}`);
    } catch (error) {
      this.logger.error('Error during data synchronization', error.stack);
    }
  }

  // 重新同步整个订单
  async refreshOrder(documentNo: string, supervisorId: number, reason: string): Promise<any> {
    this.logger.log(`Starting order refresh for documentNo: ${documentNo}, supervisorId: ${supervisorId}`);

    try {
      // 1. 验证主管权限
      const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
      if (!supervisor) {
        throw new NotFoundException('Supervisor not found');
      }

      if (supervisor.level !== 'admin') {
        throw new ForbiddenException('Only admin staff can refresh orders');
      }

      // 2. 检查订单是否存在
      const existingMain = await this.postgresMainRepository.findOne({
        where: { document_no: documentNo }
      });

      if (!existingMain) {
        throw new NotFoundException(`Order ${documentNo} not found`);
      }

      // 3. 获取当前已检查的明细记录（用于通知库存归位）
      const checkedDetails = await this.postgresDetailRepository.find({
        where: {
          document_no: Like(`${documentNo}%`),
          staff_checked: true
        }
      });

      this.logger.log(`Found ${checkedDetails.length} checked details for order ${documentNo}`);

      // 4. 从 MSSQL 获取最新的主表数据
      const sqlMain = await this.sqlServerMainRepository.findOne({
        where: { DocumentNo: documentNo }
      });

      if (!sqlMain) {
        // 如果 MSSQL 中没有找到订单，删除 PostgreSQL 中的记录
        this.logger.log(`Order ${documentNo} not found in MSSQL, removing from PostgreSQL`);

        // 删除明细记录
        const deletedDetailsCount = await this.postgresDetailRepository.count({
          where: { document_no: Like(`${documentNo}%`) }
        });

        await this.postgresDetailRepository.delete({
          document_no: Like(`${documentNo}%`)
        });

        // 删除主表记录
        await this.postgresMainRepository.delete({ document_no: documentNo });

        // 创建库存归还追踪记录和发送通知
        if (checkedDetails.length > 0) {
          // 创建追踪记录
          await this.stockReturnTrackingService.createStockReturnTracking(
            checkedDetails,
            supervisorId,
            'order_deleted'
          );

          // 发送通知
          await this.sendStockReturnNotifications(checkedDetails, supervisorId, reason);
        }

        return {
          success: true,
          message: `Order ${documentNo} has been removed (not found in source system)`,
          statistics: {
            deletedCount: deletedDetailsCount,
            insertedCount: 0,
            checkedItemsDeleted: checkedDetails.length,
            notificationsSent: await this.countNotificationsSent(checkedDetails)
          }
        };
      }

      // 5. 使用事务进行数据库操作
      const queryRunner = this.postgresMainRepository.manager.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // 6. 删除现有的明细记录
        const existingDetailsCount = await queryRunner.manager.count(Detail, {
          where: { document_no: Like(`${documentNo}%`) }
        });

        await queryRunner.manager.delete(Detail, {
          document_no: Like(`${documentNo}%`)
        });

        this.logger.log(`Deleted ${existingDetailsCount} existing detail records for ${documentNo}`);

        // 7. 更新主表信息
        await queryRunner.manager.update(Main,
          { document_no: documentNo },
          {
            customer_name: sqlMain.CustomerName || existingMain.customer_name,
            remarks: sqlMain.Remarks || existingMain.remarks,
            transporter_code: sqlMain.TransporterCode || existingMain.transporter_code,
            transporter_name: sqlMain.TransporterName || existingMain.transporter_name,
            issue_by: sqlMain.IssueBy || existingMain.issue_by,
            po_no: sqlMain.PONo || existingMain.po_no,
            // 重置出货状态
            is_shipped: false,
            shipped_at: null,
            shipped_by: null,
            driver_id: null
          }
        );

        // 8. 重新插入明细记录
        const newDetailsCount = await this.insertOrderDetails(queryRunner, documentNo, existingMain.id);

        // 提交事务
        await queryRunner.commitTransaction();

        this.logger.log(`Successfully refreshed order ${documentNo}: deleted ${existingDetailsCount}, inserted ${newDetailsCount} details`);

        // 9. 创建库存归还追踪记录和发送通知
        if (checkedDetails.length > 0) {
          // 创建追踪记录（只给普通staff，不给staff_bom）
          await this.stockReturnTrackingService.createStockReturnTracking(
            checkedDetails,
            supervisorId,
            'order_refresh'
          );

          // 发送通知给普通staff（stock return）和staff_bom（仅通知）
          await this.sendOrderRefreshNotifications(checkedDetails, supervisorId, reason);
        }

        return {
          success: true,
          message: `Order ${documentNo} refreshed successfully`,
          statistics: {
            deletedCount: existingDetailsCount,
            insertedCount: newDetailsCount,
            checkedItemsDeleted: checkedDetails.length,
            notificationsSent: await this.countOrderRefreshNotificationsSent(checkedDetails)
          }
        };

      } catch (error) {
        // 回滚事务
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // 释放查询运行器
        await queryRunner.release();
      }

    } catch (error) {
      this.logger.error(`Error refreshing order ${documentNo}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 辅助方法：重新插入订单明细
  private async insertOrderDetails(queryRunner: any, documentNo: string, mainId: number): Promise<number> {
    let insertedCount = 0;

    try {
      // 创建BOM父项映射，用于关联子项
      const bomParentMap = new Map<string, { id: number, productionNo?: string }>();

      // 从 MSSQL 获取普通明细记录
      const sqlDetails = await this.sqlServerDetailRepository.find({
        where: { DocumentNo: documentNo }
      });

      // 从 MSSQL 获取 BOM 明细记录
      const sqlBomDetails = await this.sqlServerScTranDetailRepository.find({
        where: { DocumentNo: Like(`${documentNo}%`) }
      });

      // 先插入普通明细记录和BOM父项
      for (const sqlDetail of sqlDetails) {
        // 获取库存信息
        const stockInfo = await this.sqlServerStockRepository.findOne({
          where: { StockCode: sqlDetail.Stock }
        });

        let binShelfNo = '';
        let uom = '';
        let haveStock = false;

        if (stockInfo) {
          binShelfNo = stockInfo.BinShelfNo || '';
          haveStock = (stockInfo.CurrentQuantity || 0) > 0;
          uom = stockInfo.UOM || sqlDetail.UOM || '';
        } else {
          binShelfNo = '';
          haveStock = false;
          uom = sqlDetail.UOM || '';
        }

        // 检查是否是BOM父项
        const isBomParent = sqlDetail.BOM === 'Y';

        const detail = new Detail();
        detail.document_no = sqlDetail.DocumentNo;
        detail.main_id = mainId;
        detail.line = sqlDetail.Line;
        detail.stock = sqlDetail.Stock;
        detail.description = sqlDetail.Description || '';
        detail.part_no = sqlDetail.PartNo || null;
        detail.quantity = Math.abs(sqlDetail.Quantity || 0);
        detail.uom = uom;
        detail.unit_price = sqlDetail.UnitPrice || null;
        detail.total_amount = sqlDetail.TotalAmount || null;
        detail.brand_code = sqlDetail.BrandCode || null;
        detail.parent_code = null; // 普通记录和BOM父项都没有parent_code
        detail.bin_shelf_no = binShelfNo;
        detail.have_stock = haveStock;
        detail.staff_checked = false;
        detail.supervisor_checked = false;
        detail.is_bom_parent = isBomParent; // 设置BOM父项标记
        detail.bom_parent_id = null; // BOM父项和普通记录的bom_parent_id都是null
        detail.bom_group = isBomParent ? sqlDetail.DocumentNo : null; // 只有BOM父项才有bom_group

        const savedDetail = await queryRunner.manager.save(Detail, detail);
        insertedCount++;

        // 如果是BOM父项，将其ID存储在Map中
        if (isBomParent && savedDetail && savedDetail.id) {
          const bomParentKey = `${sqlDetail.DocumentNo}|${sqlDetail.Stock}|${sqlDetail.Line}`;
          bomParentMap.set(bomParentKey, {
            id: savedDetail.id,
            productionNo: sqlDetail.ProductionNo
          });
          this.logger.log(`Added BOM parent to map: key=${bomParentKey}, id=${savedDetail.id}`);
        }
      }

      // 然后插入BOM子项记录
      for (const sqlBomDetail of sqlBomDetails) {
        // 获取库存信息
        const stockInfo = await this.sqlServerStockRepository.findOne({
          where: { StockCode: sqlBomDetail.StockCode }
        });

        let binShelfNo = '';
        let uom = '';
        let haveStock = false;

        if (stockInfo) {
          binShelfNo = stockInfo.BinShelfNo || '';
          haveStock = (stockInfo.CurrentQuantity || 0) > 0;
          uom = stockInfo.UOM || '';
        } else {
          binShelfNo = '';
          haveStock = false;
          uom = '';
        }

        const detail = new Detail();
        detail.document_no = sqlBomDetail.DocumentNo;
        detail.main_id = mainId;
        detail.line = sqlBomDetail.Line;
        detail.stock = sqlBomDetail.StockCode;
        detail.description = ''; // BOM子项没有描述字段
        detail.part_no = null; // SC_Tran_Detail表没有Part No字段
        detail.quantity = Math.abs(sqlBomDetail.Quantity || 0);
        detail.uom = uom;
        detail.unit_price = null;
        detail.total_amount = null;
        detail.brand_code = null;
        detail.parent_code = sqlBomDetail.ParentCode || null;
        detail.bin_shelf_no = binShelfNo;
        detail.have_stock = haveStock;
        detail.staff_checked = false;
        detail.supervisor_checked = false;
        detail.is_bom_parent = false; // BOM子项不是父项
        detail.bom_group = this.extractBaseDocNo(sqlBomDetail.DocumentNo);

        // 查找对应的BOM父项ID
        let foundParentId = null;
        const parentDocNo = this.extractBaseDocNo(sqlBomDetail.DocumentNo);

        // 从BOM子项的document_no中提取对应的父项line号
        // 例如：DO396391/1 -> 对应父项line 1，DO396391/2 -> 对应父项line 2
        let parentLine = null;
        const docParts = sqlBomDetail.DocumentNo.split('/');
        if (docParts.length > 1) {
          parentLine = parseInt(docParts[1]);
        }

        for (const [key, value] of bomParentMap.entries()) {
          const [docNo, stock, line] = key.split('|');
          const keyLine = parseInt(line);

          // 匹配条件：document_no、stock和line都要匹配
          if (docNo === parentDocNo && stock === sqlBomDetail.ParentCode && keyLine === parentLine) {
            foundParentId = value.id;
            this.logger.log(`Found exact BOM parent match: docNo=${docNo}, stock=${stock}, line=${line}, parentId=${foundParentId}`);
            break;
          }
        }

        // 如果没有找到精确匹配，尝试只匹配document_no和stock（向后兼容）
        if (!foundParentId) {
          for (const [key, value] of bomParentMap.entries()) {
            const [docNo, stock] = key.split('|');
            if (docNo === parentDocNo && stock === sqlBomDetail.ParentCode) {
              foundParentId = value.id;
              this.logger.log(`Found fallback BOM parent match: docNo=${docNo}, stock=${stock}, parentId=${foundParentId}`);
              break;
            }
          }
        }

        detail.bom_parent_id = foundParentId;

        if (foundParentId) {
          this.logger.log(`Set BOM parent ID ${foundParentId} for child ${sqlBomDetail.DocumentNo} with parent_code ${sqlBomDetail.ParentCode}`);
        } else {
          this.logger.warn(`BOM parent not found for ${sqlBomDetail.DocumentNo}, parent_doc_no: ${parentDocNo}, parent_code: ${sqlBomDetail.ParentCode}`);
        }

        await queryRunner.manager.save(Detail, detail);
        insertedCount++;
      }

      this.logger.log(`Inserted ${insertedCount} detail records for order ${documentNo}`);
      return insertedCount;

    } catch (error) {
      this.logger.error(`Error inserting order details for ${documentNo}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 辅助方法：发送库存归位通知
  private async sendStockReturnNotifications(checkedDetails: Detail[], supervisorId: number, reason: string): Promise<void> {
    try {
      // 按楼层分组需要归位的物品
      const itemsByFloor = new Map<string, Detail[]>();

      for (const detail of checkedDetails) {
        if (detail.bin_shelf_no && detail.bin_shelf_no.trim() !== '') {
          // 提取楼层号：2-1-G001-1 → "2"
          const floorNumber = detail.bin_shelf_no.split('-')[0];

          if (!itemsByFloor.has(floorNumber)) {
            itemsByFloor.set(floorNumber, []);
          }
          itemsByFloor.get(floorNumber)!.push(detail);
        }
      }

      // 为每个楼层发送通知
      for (const [floorNumber, items] of itemsByFloor) {
        // 查找负责该楼层的员工
        const floorStaff = await this.staffRepository.find({
          where: {
            floor: `${floorNumber}F`,
            level: 'regular'
          }
        });

        // 构建物品列表消息
        const itemList = items.map(item =>
          `• ${item.stock} (${item.description}) - Shelf: ${item.bin_shelf_no}`
        ).join('\n');

        // 创建多语言通知消息
        const messages = {
          en: `Order ${items[0].document_no.split('/')[0]} has been refreshed. Please return the following items to their original locations:\n${itemList}`,
          zh: `订单 ${items[0].document_no.split('/')[0]} 已重新同步。请将以下物品归还到原位置：\n${itemList}`,
          ms: `Pesanan ${items[0].document_no.split('/')[0]} telah disegarkan. Sila kembalikan item berikut ke lokasi asal:\n${itemList}`
        };

        // 为每个楼层员工创建通知
        for (const staff of floorStaff) {
          // 根据员工偏好语言选择消息（默认英文）
          const staffLanguage = staff.preferred_language || 'en';
          const message = messages[staffLanguage] || messages.en;

          await this.notificationService.createNotification({
            type: 'stock_return_order_refresh',
            message,
            recipient_id: staff.id,
            sender_id: supervisorId,
            document_no: items[0].document_no.split('/')[0],
            line: 0,
            detail_id: 0,
            reject_reason: reason || null
          });
        }

        this.logger.log(`Sent stock return notifications to ${floorStaff.length} staff members for floor ${floorNumber}`);
      }

    } catch (error) {
      this.logger.error(`Error sending stock return notifications: ${error.message}`, error.stack);
      // 不抛出错误，因为通知失败不应该影响主要的同步流程
    }
  }

  // 发送订单刷新通知：给普通staff发送stock return通知，给staff_bom发送普通通知
  private async sendOrderRefreshNotifications(checkedDetails: Detail[], supervisorId: number, reason: string): Promise<void> {
    try {
      this.logger.log(`Sending order refresh notifications for ${checkedDetails.length} checked details`);

      // 按楼层分组物品
      const itemsByFloor = new Map<string, Detail[]>();

      for (const detail of checkedDetails) {
        if (detail.bin_shelf_no && detail.bin_shelf_no.trim() !== '') {
          // 提取楼层号：2-1-G001-1 → "2"
          const floorNumber = detail.bin_shelf_no.split('-')[0];

          if (!itemsByFloor.has(floorNumber)) {
            itemsByFloor.set(floorNumber, []);
          }
          itemsByFloor.get(floorNumber)!.push(detail);
        }
      }

      // 为每个楼层发送通知
      for (const [floorNumber, items] of itemsByFloor) {
        // 查找负责该楼层的普通员工（用于stock return）
        const floorStaff = await this.staffRepository.find({
          where: {
            floor: `${floorNumber}F`,
            level: 'regular'
          }
        });

        // 构建物品列表消息
        const itemList = items.map(item =>
          `• ${item.stock} (${item.description}) - Shelf: ${item.bin_shelf_no}`
        ).join('\n');

        // 创建多语言通知消息（给普通员工的stock return消息）
        const stockReturnMessages = {
          en: `Order ${items[0].document_no.split('/')[0]} has been refreshed. Please return the following items to their original locations:\n${itemList}`,
          zh: `订单 ${items[0].document_no.split('/')[0]} 已重新同步。请将以下物品归还到原位置：\n${itemList}`,
          ms: `Pesanan ${items[0].document_no.split('/')[0]} telah disegarkan. Sila kembalikan item berikut ke lokasi asal:\n${itemList}`
        };

        // 为每个楼层的普通员工创建stock return通知
        for (const staff of floorStaff) {
          // 根据员工偏好语言选择消息（默认英文）
          const staffLanguage = staff.preferred_language || 'en';
          const message = stockReturnMessages[staffLanguage] || stockReturnMessages.en;

          await this.notificationService.createNotification({
            type: 'stock_return_order_refresh',
            message,
            recipient_id: staff.id,
            sender_id: supervisorId,
            document_no: items[0].document_no.split('/')[0],
            line: 0,
            detail_id: 0,
            reject_reason: reason || null
          });
        }

        this.logger.log(`Sent stock return notifications to ${floorStaff.length} regular staff members for floor ${floorNumber}`);
      }

      // 查找所有staff_bom员工并发送普通通知
      const bomStaff = await this.staffRepository.find({
        where: {
          level: 'staff_bom'
        }
      });

      if (bomStaff.length > 0) {
        // 创建给BOM专员的通知消息（不包含具体物品列表，只是通知订单已刷新）
        const bomNotificationMessages = {
          en: `Order ${checkedDetails[0].document_no.split('/')[0]} has been refreshed. Please check for any new BOM items that may require your attention.`,
          zh: `订单 ${checkedDetails[0].document_no.split('/')[0]} 已重新同步。请检查是否有新的BOM物品需要您的关注。`,
          ms: `Pesanan ${checkedDetails[0].document_no.split('/')[0]} telah disegarkan. Sila semak jika ada item BOM baru yang memerlukan perhatian anda.`
        };

        for (const staff of bomStaff) {
          const staffLanguage = staff.preferred_language || 'en';
          const message = bomNotificationMessages[staffLanguage] || bomNotificationMessages.en;

          await this.notificationService.createNotification({
            type: 'order_refresh_bom_notification',
            message,
            recipient_id: staff.id,
            sender_id: supervisorId,
            document_no: checkedDetails[0].document_no.split('/')[0],
            line: 0,
            detail_id: 0,
            reject_reason: reason || null
          });
        }

        this.logger.log(`Sent order refresh notifications to ${bomStaff.length} BOM staff members`);
      }

    } catch (error) {
      this.logger.error(`Error sending order refresh notifications: ${error.message}`, error.stack);
      // 不抛出错误，因为通知失败不应该影响主要的同步流程
    }
  }

  // 辅助方法：计算发送的通知数量
  private async countNotificationsSent(checkedDetails: Detail[]): Promise<number> {
    try {
      const itemsByFloor = new Map<string, Detail[]>();

      for (const detail of checkedDetails) {
        if (detail.bin_shelf_no && detail.bin_shelf_no.trim() !== '') {
          const floorNumber = detail.bin_shelf_no.split('-')[0];

          if (!itemsByFloor.has(floorNumber)) {
            itemsByFloor.set(floorNumber, []);
          }
          itemsByFloor.get(floorNumber)!.push(detail);
        }
      }

      let totalNotifications = 0;
      for (const [floorNumber] of itemsByFloor) {
        const floorStaffCount = await this.staffRepository.count({
          where: {
            floor: `${floorNumber}F`,
            level: 'regular'
          }
        });
        totalNotifications += floorStaffCount;
      }

      return totalNotifications;
    } catch (error) {
      this.logger.error(`Error counting notifications: ${error.message}`, error.stack);
      return 0;
    }
  }

  // 辅助方法：计算订单刷新发送的通知数量（包括普通staff和staff_bom）
  private async countOrderRefreshNotificationsSent(checkedDetails: Detail[]): Promise<number> {
    try {
      const itemsByFloor = new Map<string, Detail[]>();

      for (const detail of checkedDetails) {
        if (detail.bin_shelf_no && detail.bin_shelf_no.trim() !== '') {
          const floorNumber = detail.bin_shelf_no.split('-')[0];

          if (!itemsByFloor.has(floorNumber)) {
            itemsByFloor.set(floorNumber, []);
          }
          itemsByFloor.get(floorNumber)!.push(detail);
        }
      }

      // 计算普通员工通知数量
      let totalNotifications = 0;
      for (const [floorNumber] of itemsByFloor) {
        const floorStaffCount = await this.staffRepository.count({
          where: {
            floor: `${floorNumber}F`,
            level: 'regular'
          }
        });
        totalNotifications += floorStaffCount;
      }

      // 计算BOM专员通知数量
      const bomStaffCount = await this.staffRepository.count({
        where: {
          level: 'staff_bom'
        }
      });
      totalNotifications += bomStaffCount;

      return totalNotifications;
    } catch (error) {
      this.logger.error(`Error counting order refresh notifications: ${error.message}`, error.stack);
      return 0;
    }
  }
}
