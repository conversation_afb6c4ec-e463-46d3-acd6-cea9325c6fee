// import 'dart:io';
// import 'package:image/image.dart' as img;

// void main() async {
//   // 读取原始图标
//   final File originalFile = File('assets/images/Icon_TaskPickr-removebg.png');
//   final List<int> originalBytes = await originalFile.readAsBytes();
//   final img.Image? originalImage = img.decodeImage(originalBytes);
  
//   if (originalImage == null) {
//     print('无法解码图像');
//     return;
//   }
  
//   // 计算新尺寸 (原尺寸的110%)
//   final int newWidth = (originalImage.width * 1.1).round();
//   final int newHeight = (originalImage.height * 1.1).round();
  
//   // 调整图像大小
//   final img.Image resizedImage = img.copyResize(
//     originalImage,
//     width: newWidth,
//     height: newHeight,
//     interpolation: img.Interpolation.cubic,
//   );
  
//   // 创建一个新的图像，使图标居中，周围没有白边
//   final img.Image finalImage = img.Image(
//     width: originalImage.width,
//     height: originalImage.height,
//   );
  
//   // 计算偏移量，使调整大小后的图像居中
//   final int offsetX = (originalImage.width - newWidth) ~/ 2;
//   final int offsetY = (originalImage.height - newHeight) ~/ 2;
  
//   // 将调整大小后的图像复制到最终图像
//   img.compositeImage(
//     finalImage,
//     resizedImage,
//     dstX: offsetX,
//     dstY: offsetY,
//   );
  
//   // 保存处理后的图像
//   final File processedFile = File('assets/images/Icon_TaskPickr-processed.png');
//   await processedFile.writeAsBytes(img.encodePng(finalImage));
  
//   print('图像处理完成: ${processedFile.path}');
// }
