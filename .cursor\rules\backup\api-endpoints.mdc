---
description:
globs:
alwaysApply: false
---
# API 端点规范（按使用场景分类）

## 前端登录/认证相关 API

### 员工登录
- **路径**: `/staff/login`
- **方法**: `POST`
- **控制器**: StaffController.login
- **请求体**:
  - `username`: 用户名
  - `password`: 密码
- **说明**: 用于所有角色的用户登录系统

## 员工(Staff)操作相关 API

### 获取员工待检查列表
- **路径**: `/check/list`
- **方法**: `GET`
- **控制器**: CheckController.getCheckList
- **查询参数**:
  - `staffId`: 员工ID
- **说明**: 普通员工获取待检查的货物列表

### 员工检查货物
- **路径**: `/check/staff`
- **方法**: `POST`
- **控制器**: CheckController.staffCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `staffId`: 员工ID
- **说明**: 普通员工标记货物为已检查

### 获取楼层检查统计
- **路径**: `/check/floor-stats/:floor`
- **方法**: `GET`
- **控制器**: CheckController.getFloorStats
- **查询参数**:
  - `floor`: 楼层编号 (URL参数)
- **说明**: 获取特定楼层的检查统计信息，用于员工查看工作进度

### 获取用户未读通知
- **路径**: `/notification/unread/:userId`
- **方法**: `GET`
- **控制器**: NotificationController.getUnreadNotifications
- **查询参数**:
  - `userId`: 用户ID (URL参数)
- **说明**: 员工查看未读通知，如检查被拒绝等

### 获取用户所有通知
- **路径**: `/notification/all/:userId`
- **方法**: `GET`
- **控制器**: NotificationController.getAllNotifications
- **查询参数**:
  - `userId`: 用户ID (URL参数)
- **说明**: 员工查看所有通知历史

### 标记通知为已读
- **路径**: `/notification/read/:id`
- **方法**: `POST`
- **控制器**: NotificationController.markAsRead
- **查询参数**:
  - `id`: 通知ID (URL参数)
- **说明**: 员工标记单条通知为已读

### 标记所有通知为已读
- **路径**: `/notification/read-all`
- **方法**: `POST`
- **控制器**: NotificationController.markAllAsRead
- **查询参数**:
  - `userId`: 用户ID
- **说明**: 员工标记所有通知为已读

## 员工BOM(Staff_Bom)操作相关 API

### BOM专员检查货物
- **路径**: `/check/bom`
- **方法**: `POST`
- **控制器**: CheckController.bomSpecialistCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `bomSpecialistId`: BOM专员ID
- **说明**: BOM专员对BOM物品进行二次检查，只能检查已被普通员工初步检查过的BOM物品

### 获取员工待检查列表
- **路径**: `/check/list`
- **方法**: `GET`
- **控制器**: CheckController.getCheckList
- **查询参数**:
  - `staffId`: 员工ID
- **说明**: BOM专员获取待检查的BOM物品列表，系统会根据用户级别返回不同的列表内容

## 主管(Supervisor)操作相关 API

### 主管检查货物
- **路径**: `/check/supervisor`
- **方法**: `POST`
- **控制器**: CheckController.supervisorCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `supervisorId`: 主管ID
- **说明**: 主管确认货物检查，对于BOM物品需要先经过BOM专员检查

### 拒绝员工检查
- **路径**: `/check/reject`
- **方法**: `POST`
- **控制器**: CheckController.rejectCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `supervisorId`: 主管ID
  - `reason`: 拒绝原因 (可选)
- **说明**: 主管拒绝员工或BOM专员的检查结果

### 检查这张订单是否可以出货
- **路径**: `/check/document-ready`
- **方法**: `GET`
- **控制器**: CheckController.isDocumentReady
- **查询参数**:
  - `documentNo`: 单据编号
  - `staffId`: 员工ID
- **说明**: 主管检查订单是否所有项目都已确认可以出货

### 获取所有可出货订单
- **路径**: `/check/ready-documents`
- **方法**: `GET`
- **控制器**: CheckController.getReadyDocuments
- **查询参数**:
  - `staffId`: 员工ID（必须是主管或司机）
- **权限要求**:
  - 仅限主管(senior)或司机(driver)级别用户访问
- **返回数据**:
  ```json
  [
    {
      "id": 1,
      "document_no": "DO-2024-001",
      "document_date": "2025-04-15T00:00:00.000Z",
      "customer": "CUST001",
      "customer_name": "测试客户",
      "remarks": "紧急订单",
      "is_shipped": false,
      "created_at": "2024-04-15T08:30:00.000Z",
      "updated_at": "2024-04-15T09:15:00.000Z"
    }
  ]
  ```
- **说明**:
  - 返回所有未出货但至少有一个项目被主管确认过的订单
  - 用于主管出货页面和司机接单页面
  - 如果用户不是主管或司机，将返回403错误
  - 如果用户不存在，将返回404错误

### 标记订单为已出货
- **路径**: `/check/complete`
- **方法**: `POST`
- **控制器**: CheckController.completeDocument
- **查询参数**:
  - `documentNo`: 单据编号
  - `supervisorId`: 主管ID
  - `driverId`: 司机ID (可选)
- **说明**: 主管标记订单为已出货，可选择指派司机送货

### 获取主管指派的订单列表
- **路径**: `/shipping/supervisor-shipments`
- **方法**: `GET`
- **控制器**: ShippingController.getSupervisorShipments
- **查询参数**:
  - `supervisorId`: 主管ID
- **说明**: 主管查看自己指派的所有订单及其送达状态

### 获取所有司机列表
- **路径**: `/shipping/drivers`
- **方法**: `GET`
- **控制器**: ShippingController.getAllDrivers
- **查询参数**: 无
- **说明**: 主管获取所有司机列表，用于指派送货任务

## 司机(Driver)操作相关 API

### 获取司机待送达订单列表
- **路径**: `/shipping/delivery-list`
- **方法**: `GET`
- **控制器**: ShippingController.getDeliveryList
- **查询参数**:
  - `driverId`: 司机ID
- **说明**: 司机获取分配给自己的待送达订单列表

### 确认订单送达
- **路径**: `/shipping/confirm-delivery`
- **方法**: `POST`
- **控制器**: ShippingController.confirmDelivery
- **查询参数**:
  - `documentNo`: 单据编号
  - `driverId`: 司机ID
  - `notes`: 送达备注 (可选)
- **说明**: 司机确认订单已送达客户

### 司机拒绝分配的订单
- **路径**: `/check/reject-delivery`
- **方法**: `POST`
- **控制器**: CheckController.rejectDelivery
- **查询参数**:
  - `documentNo`: 单据编号
  - `driverId`: 司机ID
  - `reason`: 拒绝原因 (可选)
- **说明**: 司机拒绝接受分配的订单

### 获取所有可出货订单
- **路径**: `/check/ready-documents`
- **方法**: `GET`
- **控制器**: CheckController.getReadyDocuments
- **查询参数**:
  - `staffId`: 员工ID（必须是主管或司机）
- **说明**: 司机也可以查看可出货订单列表（与主管共用API）

## PDF 生成与邮件相关 API

### 生成订单 PDF
- **路径**: `/pdf/generate`
- **方法**: `POST`
- **控制器**: PdfController.generatePdf
- **查询参数**:
  - `documentNo`: 订单编号
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "PDF generated successfully",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 使用传统方式生成订单 PDF 文件
  - 生成的 PDF 文件保存在服务器的 PDF_Output/{当前日期} 目录下
  - 如果订单不存在，将返回 404 错误

### 使用 HTML 模板生成订单 PDF
- **路径**: `/pdf/generate-html`
- **方法**: `POST`
- **控制器**: PdfController.generateHtmlPdf
- **查询参数**:
  - `documentNo`: 订单编号
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "PDF generated successfully using HTML template",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 使用 HTML 模板和 Puppeteer 生成订单 PDF 文件
  - 生成的 PDF 文件保存在服务器的 PDF_Output/{当前日期} 目录下
  - 如果订单不存在，将返回 404 错误

### 查看订单 PDF
- **路径**: `/pdf/view`
- **方法**: `GET`
- **控制器**: PdfController.viewPdf
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **返回数据**: PDF 文件流
- **说明**:
  - 返回指定订单的 PDF 文件
  - 如果文件不存在，会尝试先生成 PDF 文件
  - 如果生成失败，将返回 404 错误

### 添加签名到 PDF
- **路径**: `/pdf/add-signature`
- **方法**: `POST`
- **控制器**: PdfController.addSignature
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **请求体**:
  - `signatureBase64`: 签名的 Base64 编码数据
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "Signature added successfully",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 将签名添加到指定订单的 PDF 文件中
  - 如果 PDF 文件不存在，将返回 404 错误

### 使用 HTML 模板添加签名到 PDF
- **路径**: `/pdf/add-signature-html`
- **方法**: `POST`
- **控制器**: PdfController.addSignatureHtml
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **请求体**:
  - `signatureBase64`: 签名的 Base64 编码数据
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "Signature added successfully using HTML template",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 使用 HTML 模板将签名添加到指定订单的 PDF 文件中
  - 如果 PDF 文件不存在，将返回 404 错误

### 发送 PDF 邮件
- **路径**: `/email/send-pdf`
- **方法**: `POST`
- **控制器**: EmailController.sendPdf
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **请求体**:
  - `to`: 收件人邮箱
  - `subject`: 邮件主题 (可选，默认为 "Delivery Order: {documentNo}")
  - `text`: 邮件正文 (可选，默认为 "Please find the attached delivery order PDF for document {documentNo}.")
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "Email <NAME_EMAIL>"
  }
  ```
- **说明**:
  - 将指定订单的 PDF 文件作为附件发送到指定邮箱
  - 如果 PDF 文件不存在，将返回错误
  - 邮件发送失败时，将返回错误信息

## 系统管理相关 API

### 获取所有员工
- **路径**: `/staff`
- **方法**: `GET`
- **控制器**: StaffController.findAll
- **查询参数**: 无
- **说明**: 系统管理员获取所有员工列表

### 根据 ID 获取员工
- **路径**: `/staff/:id`
- **方法**: `GET`
- **控制器**: StaffController.findOne
- **查询参数**:
  - `id`: 员工ID (URL参数)
- **说明**: 系统管理员获取特定员工信息

### 创建新员工
- **路径**: `/staff`
- **方法**: `POST`
- **控制器**: StaffController.create
- **请求体**:
  - `username`: 用户名
  - `password`: 密码
  - `level`: 权限级别
  - `floor`: 负责楼层
  - `full_name`: 全名
- **说明**: 系统管理员创建新员工账号

### 手动触发数据同步
- **路径**: `/sync`
- **方法**: `POST`
- **控制器**: SyncController.syncData
- **查询参数**: 无
- **说明**: 系统管理员手动触发数据同步，从外部系统导入订单数据

