import 'package:dio/dio.dart';
import 'package:queue_system/models/staff.dart';

// 错误代码常量
class AuthErrorCode {
  static const String serverEmpty = 'error_empty_response';
  static const String loginFailed = 'error_login_failed';
  static const String invalidCredentials = 'error_invalid_credentials';
  static const String networkError = 'error_network';
  static const String createStaffFailed = 'error_create_staff';
  static const String invalidData = 'error_invalid_data';
}

class AuthService {
  final Dio _dio;
  final String baseUrl;

  AuthService({required this.baseUrl})
      : _dio = Dio(BaseOptions(
          baseUrl: baseUrl,
          connectTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 3),
        ));

  // 登录方法
  Future<Staff> login(String username, String password) async {
    try {
      final response = await _dio.post(
        '/staff/login',
        data: {
          'username': username,
          'password': password,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data == null) {
          throw AuthErrorCode.serverEmpty;
        }
        return Staff.fromJson(response.data);
      } else {
        throw AuthErrorCode.loginFailed;
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AuthErrorCode.invalidCredentials;
      }
      throw AuthErrorCode.networkError;
    } catch (e) {
      throw AuthErrorCode.loginFailed;
    }
  }

  // 创建新员工
  Future<Staff> createStaff({
    required String username,
    required String password,
    required String level,
    required String floor,
    required String fullName,
  }) async {
    try {
      final response = await _dio.post(
        '/staff',
        data: {
          'username': username,
          'password': password,
          'level': level,
          'floor': floor,
          'full_name': fullName,
        },
      );

      if (response.statusCode == 201) {
        if (response.data == null) {
          throw AuthErrorCode.serverEmpty;
        }
        return Staff.fromJson(response.data);
      } else {
        throw AuthErrorCode.createStaffFailed;
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 400) {
        final errorMessage =
            e.response?.data['message'] ?? AuthErrorCode.invalidData;
        throw errorMessage;
      }
      throw AuthErrorCode.networkError;
    } catch (e) {
      throw AuthErrorCode.createStaffFailed;
    }
  }
}
