class AppLocalizationsMs {
  static Map<String, String> get values => {
        // Login page
        'login': '<PERSON>g <PERSON>',
        'username': '<PERSON><PERSON>',
        'password': '<PERSON><PERSON>',
        'please_enter_username': '<PERSON>la masukkan nama pengguna',
        'please_enter_password': 'Sila masukkan kata laluan',
        'register_new_staff': 'Daftar Kakitangan Baru',
        'switch_language': '<PERSON>kar <PERSON> (中文/English)',

        // Home page
        'warehouse_system': 'Sistem Pemeriksaan Gudang',
        'refresh': 'Muat Semula Data',
        'logout': 'Log <PERSON>',
        'supervisor': 'Penyelia',
        'admin': 'Pentadbir',
        'regular_staff': 'Kakitangan Biasa',
        'login_first': 'Sila log masuk dahulu',
        'loading': 'Memuatkan...',
        'connection_failed': 'Sambungan Gagal',
        'unable_connect_server':
            'Tidak dapat menyambung ke pelayan, sila periksa rang<PERSON>',
        'retry': 'Cuba Lagi',
        'no_items_to_check': 'Tiada item untuk diperiksa',
        'no_items_to_confirm': 'Tiada item untuk disahkan',
        'to_check_bom': 'Periksa BOM',
        'bom_status': 'Status BOM',
        'error_loading_bom_status': 'Ralat memuatkan status BOM',
        'no_bom_children_found': 'Tiada item anak BOM ditemui',
        'waiting_for_staff_bom': 'Menunggu pakar BOM',

        // Tabs
        'pending_check': 'Perlu Diperiksa',
        'pending_confirm': 'Perlu Disahkan',
        'to_check': 'Untuk Periksa',
        'to_confirm': 'Untuk Sahkan',
        'to_ship': 'Untuk Hantar',
        'to_deliver': 'Untuk Serahan',
        'to_delivery': 'Untuk Serahan',
        'delivery': 'Status Penghantaran',
        'delivery_status': 'Status Penghantaran',
        'bom_review': 'Semakan BOM',
        'queue': 'Barisan',
        'no_pending_items': 'Tiada item tertunda',

        // Check items
        'line': 'Baris',
        'quantity': 'Kuantiti',
        'shelf': 'Rak',
        'checked': 'Diperiksa',
        'unknown_time': 'Masa tidak diketahui',
        'note': 'Nota',
        'check': 'Periksa',
        'confirm': 'Sahkan',
        'waiting_for_supervisor': 'Menunggu penyelia',
        'waiting_for_staff': 'Menunggu pemeriksaan kakitangan',
        'already_checked': 'Telah diperiksa',
        'not_checked': 'Belum diperiksa',

        // Dialogs
        'confirm_check': 'Sahkan Pemeriksaan',
        'confirm_check_message':
            'Adakah anda pasti mahu menandakan item ini sebagai diperiksa?',
        'confirm_verify': 'Sahkan Pengesahan',
        'confirm_verify_message':
            'Adakah anda pasti mahu mengesahkan item ini?',
        'cancel': 'Batal',
        'confirm': 'Sahkan',
        'operation_success': 'Operasi berjaya',
        'operation_failed': 'Operasi gagal',
        'refresh_failed': 'Pemuat semula gagal',
        'loading_failed': 'Pemuatan data gagal',
        'failed': 'Gagal',
        'data_refreshed': 'Data berjaya dimuat semula',

        // Logout confirmation
        'logout_confirmation': 'Log Keluar',
        'logout_message': 'Adakah anda pasti mahu log keluar?',

        // Add staff
        'add_staff': 'Tambah Kakitangan',
        'fullname': 'Nama Penuh',
        'email': 'E-mel',
        'staff_level': 'Tahap Kakitangan',
        'floor': 'Lantai',
        'create_staff': 'Cipta Kakitangan',
        'please_fill_required': 'Sila isi semua ruangan yang diperlukan',
        'staff_created_success': 'Kakitangan berjaya dicipta',

        // Document details
        'document_no': 'No. Dokumen',
        'date': 'Tarikh',
        'line_no': 'Baris',
        'items': 'item',
        'pending_items': 'item tertunggak',
        'documents': 'dokumen',
        'quantity_short': 'Kuantiti',
        'shelf_location': 'Lokasi Rak',
        'parent_code': 'BOM',
        'checked_status': 'Status Periksa',
        'description': 'Penerangan',
        'no_shelf_assigned': 'Tidak ditetapkan',

        // Network diagnostic
        'network_diagnostic': 'Diagnostik Rangkaian',
        'start_diagnostic': 'Mula Diagnostik',
        'running_diagnostics': 'Menjalankan diagnostik rangkaian...',
        'error_running_diagnostics': 'Ralat menjalankan diagnostik',
        'network_diagnostic_explanation':
            'Alat ini memeriksa sambungan rangkaian dan akses pelayan anda.',
        'run_diagnostics_again': 'Jalankan Diagnostik Sekali Lagi',
        'back': 'Kembali',

        // Authentication errors
        'error_empty_response': 'Pelayan mengembalikan respons kosong',
        'error_login_failed': 'Log masuk gagal',
        'error_invalid_credentials': 'Nama pengguna atau kata laluan tidak sah',
        'error_network': 'Sambungan rangkaian gagal',
        'error_create_staff': 'Gagal mencipta kakitangan',
        'error_invalid_data': 'Data input tidak sah',

        // Service errors
        'error_get_checklist': 'Gagal mendapatkan senarai semak',
        'error_staff_check': 'Pemeriksaan kakitangan gagal',
        'error_supervisor_check': 'Pengesahan penyelia gagal',
        'error_connection': 'Sambungan gagal',
        'error_sync': 'Penyelarasan gagal',
        'success_sync': 'Penyelarasan berjaya',
        'connection_server_failed':
            'Gagal menyambung ke pelayan, sila periksa rangkaian',

        // Document screen
        'document_title': 'Dokumen',
        'detail_items': 'Item Terperinci',
        'no_detail_data': 'Tiada data terperinci',
        'document_number': 'No. Dokumen',
        'transaction_type': 'Jenis Transaksi',
        'document_date': 'Tarikh Dokumen',
        'customer_code': 'Kod Pelanggan',
        'customer_name': 'Nama Pelanggan',
        'salesman_code': 'Jurujual',
        'issuer': 'Pengeluar',
        'issue_date': 'Tarikh Terbitan',
        'issue_time': 'Masa Terbitan',
        'delivery_address': 'Alamat Penghantaran',
        'remarks': 'Catatan',
        'amount': 'Jumlah',
        'created_at': 'Dicipta Pada',
        'location': 'Lokasi',
        'quantity_full': 'Kuantiti',

        // App title
        'inventory_sync_system': 'Sistem Penyelarasan Inventori',
        'app_title': 'Sistem Pengurusan Gudang',

        // 员工类型
        'bom_specialist': 'Pakar BOM',
        'driver': 'Pemandu',
        'suggested_driver': 'Pemandu Dicadangkan',

        // 验证错误
        'username_no_spaces': 'Nama pengguna tidak boleh mengandungi ruang',
        'password_no_spaces': 'Kata laluan tidak boleh mengandungi ruang',
        'enter_username_no_spaces':
            'Masukkan nama pengguna (ruang tidak dibenarkan)',
        'enter_password_no_spaces':
            'Masukkan kata laluan (ruang tidak dibenarkan)',

        // 出货和送货相关
        'ready_to_ship': 'Sedia untuk Penghantaran',
        'not_ready': 'Belum Sedia',
        'check_failed': 'Pemeriksaan Gagal',
        'waiting_for_check': 'Menunggu Pemeriksaan',
        'no_ready_documents': 'Tiada dokumen sedia untuk dihantar',
        'no_delivery_documents': 'Tiada dokumen untuk diserahi',
        'no_shipments': 'Tiada penghantaran ditemui',
        'ship': 'Hantar',
        'ship_without_driver': 'Hantar tanpa Pemandu',
        'assign_driver': 'Tugaskan Pemandu',
        'driver_id': 'ID Pemandu',
        'confirm_ship_without_driver': 'Hantar tanpa Pemandu',
        'confirm_ship_with_driver': 'Tugaskan Pemandu dan Hantar',
        'confirm_shipment': 'Sahkan Penghantaran',
        'will_be_shipped_without_driver': 'akan dihantar tanpa pemandu',
        'will_be_shipped_and_assigned_to_driver':
            'akan dihantar dan ditugaskan kepada pemandu',
        'shipment_completed_successfully':
            'Penghantaran selesai dengan jayanya',
        'shipment_failed': 'Penghantaran gagal',
        'document': 'Dokumen',
        'customer': 'Pelanggan',
        'delivered': 'Dihantar',
        'rejected': 'Ditolak',
        'in_transit': 'Dalam Transit',
        'no_driver_assigned': 'Tiada pemandu ditugaskan',

        // 送达相关
        'confirm_delivery': 'Sahkan Serahan',
        'cannot_deliver': 'Tidak Dapat Diserahkan',
        'delivery_notes': 'Nota Penghantaran',
        'delivery_notes_hint': 'Masukkan nota penghantaran (pilihan)',
        'delivered_at': 'Dihantar Pada',
        'delivery_confirmed': 'Serahan disahkan',
        'delivery_confirmation_failed': 'Pengesahan serahan gagal',
        'reject_delivery': 'Tolak Serahan',
        'confirm_rejection': 'Sahkan Penolakan',
        'rejection_reason': 'Sebab Penolakan',
        'rejection_reason_hint': 'Sila masukkan sebab untuk penolakan serahan',
        'rejection_reason_required': 'Sebab penolakan diperlukan',
        'delivery_rejected': 'Serahan ditolak',
        'rejection_failed': 'Penolakan gagal',

        // API错误
        'error_get_ready_documents': 'Gagal mendapatkan dokumen yang sedia',
        'error_complete_document': 'Gagal melengkapkan dokumen',
        'error_get_delivery_list': 'Gagal mendapatkan senarai serahan',
        'error_confirm_delivery': 'Gagal mengesahkan serahan',
        'error_get_shipments': 'Gagal mendapatkan penghantaran',
        'error_reject_delivery': 'Gagal menolak serahan',
        'error_reject_shipment': 'Gagal menolak penghantaran',

        // Shipment rejection
        'reject_shipment': 'Tolak Penghantaran',
        'reject_shipment_warning':
            'Ini akan menetapkan semula semua pemeriksaan dan memerlukan kakitangan memeriksa semua item sekali lagi.',
        'shipment_rejected': 'Penghantaran berjaya ditolak',
        'reject_failed': 'Penolakan gagal',
        'processing': 'Memproses...',
        'no_reason_provided': 'Tiada sebab diberikan',
        'confirm_reject_shipment': 'Sahkan Tolak Penghantaran',
        'confirm_reject_shipment_message':
            'Adakah anda pasti mahu menolak penghantaran ini? Ini akan menetapkan semula semua pemeriksaan dan memerlukan kakitangan memeriksa semua item sekali lagi.',

        // Pemberitahuan berkaitan
        'notifications': 'Pemberitahuan',
        'notification': 'Pemberitahuan',
        'mark_as_read': 'Tandai sebagai Dibaca',
        'mark_all_as_read': 'Tandai Semua sebagai Dibaca',
        'marked_as_read': 'Ditandai sebagai dibaca',
        'all_marked_as_read': 'Semua pemberitahuan ditandai sebagai dibaca',
        'no_notifications': 'Tiada pemberitahuan yang belum dibaca',
        'check_rejected': 'Pemeriksaan Ditolak',
        'reason': 'Sebab',
        'you': 'Anda',
        'auto_assign_driver_message':
            'Anda akan secara automatik ditugaskan sebagai pemandu penghantaran',
        'reject_staff_check': 'Tolak Pemeriksaan Kakitangan',
        'enter_reject_reason': 'Masukkan sebab penolakan',
        'reject_reason': 'Sebab Penolakan',
        'confirm_reject': 'Sahkan Penolakan',
        'confirm_reject_message':
            'Sahkan penolakan pemeriksaan kakitangan ini? Kakitangan perlu memeriksa semula',
        'reject': 'Tolak',
        'confirm_bom_check': 'Sahkan Pemeriksaan BOM',
        'confirm_bom_check_message':
            'Adakah anda pasti item BOM ini diperiksa dengan betul?',
        'confirm_bom_child_check_message':
            'Sahkan pemeriksaan item anak BOM ini?',
        'bom_check': 'Pemeriksaan BOM',
        'unassigned_shelf': 'Rak Tidak Ditugaskan',
        'special_item': 'Item Khas',
        'stock': 'Stok',
        'modify_cancel': 'Ubah/Batal',
        'custom_reason': 'Sebab Kustom',
        'select_reject_type': 'Pilih jenis penolakan',
        'stock_return': 'Kembalikan Stok',
        'cn_return': 'Kembalikan CN',
        'return_tracking': 'Jejak Pemulangan',
        'stock_return_message':
            'Item ini telah diubah atau dikeluarkan. Klik untuk kembalikan stok.',
        'no_pending_stock_returns': 'Tiada stok yang perlu dikembalikan',
        'no_pending_cn_returns': 'Tiada CN yang perlu dikembalikan',
        'confirm_stock_return': 'Sahkan Pemulangan Stok',
        'confirm_return': 'Sahkan Pemulangan',
        'return_confirmed': 'Pemulangan disahkan',
        'confirm_stock_return_message':
            'Saya telah mengembalikan stok ini ke lokasi asalnya',
        'stock_return_confirmed': 'Pemulangan stok disahkan',
        'error_get_stock_returns':
            'Gagal mendapatkan senarai stok untuk dikembalikan',
        'error_confirm_stock_return': 'Gagal mengesahkan pemulangan stok',
        'completed_by': 'Diselesaikan oleh',
        'pdf_email_options': 'Pilihan E-mel PDF',
        'pdf_created_successfully': 'PDF berjaya dicipta',
        'skip_email': 'Langkau E-mel',
        'send_email': 'Hantar E-mel',
        'email_sent_failed': 'Gagal menghantar e-mel',
        'send_pdf': 'Hantar PDF',
        'pdf_options': 'Pilihan PDF',
        'view_pdf': 'Lihat PDF',
        'download_pdf': 'Muat Turun PDF',
        'view_pdf_history': 'Lihat Sejarah PDF',
        'pdf_history': 'Sejarah PDF',
        'no_pdf_documents': 'Tiada dokumen PDF tersedia',
        'error_open_pdf': 'Gagal membuka PDF',
        'downloading_pdf': 'Memuat turun PDF',
        'pdf_download_success': 'PDF berjaya dimuat turun',
        'pdf_download_failed': 'Gagal memuat turun PDF',

        // WhatsApp related
        'send_to_whatsapp': 'Hantar ke WhatsApp',
        'phone_number': 'Nombor Telefon',
        'please_enter_phone_number': 'Sila masukkan nombor telefon',
        'please_enter_valid_phone_format':
            'Sila masukkan format nombor telefon yang sah',
        'message_preview': 'Pratonton mesej yang akan dihantar:',
        'whatsapp_not_installed': 'WhatsApp Tidak Dipasang',
        'whatsapp_not_installed_message':
            'Aplikasi WhatsApp tidak dipasang pada peranti anda.\n\nSila pasang WhatsApp terlebih dahulu, kemudian cuba hantar mesej semula.\n\nSebagai alternatif, anda boleh memilih untuk berkongsi pautan PDF melalui kaedah lain.',
        'got_it': 'Faham',
        'download_whatsapp': 'Muat Turun WhatsApp',
        'whatsapp': 'WhatsApp',
        'getting_pdf_download_link': 'Mendapatkan pautan muat turun PDF...',
        'pdf_not_uploaded_to_cloud':
            'Fail PDF belum dimuat naik ke awan, tidak dapat dikongsi melalui WhatsApp',
        'whatsapp_opened_complete_sending':
            'WhatsApp dibuka, sila lengkapkan penghantaran mesej',
        'whatsapp_share_failed': 'Perkongsian WhatsApp gagal',
        'whatsapp_message_template':
            'Hello! Ini adalah pautan dokumen PDF untuk pesanan anda {documentNo}:\n\n{pdfUrl}\n\nSila klik pautan untuk melihat atau memuat turun butiran pesanan anda.\n\nTerima kasih!',

        // Order Refresh Related
        'refresh_order': 'Segarkan Pesanan',
        'refresh_order_warning': 'Amaran:',
        'refresh_order_warning_1':
            'Akan memadam semua rekod pemeriksaan sedia ada',
        'refresh_order_warning_2': 'Segerak semula data terkini dari sistem',
        'refresh_order_warning_3':
            'Kakitangan perlu memeriksa semula semua item',
        'refresh_order_reason_hint': 'Maklumat pesanan telah dikemas kini...',
        'confirm_refresh': 'Sahkan Penyegaran',
        'refreshing_order': 'Menyegarkan pesanan...',
        'order_refreshed_successfully': 'Pesanan berjaya disegarkan',
        'refresh_order_failed': 'Penyegaran pesanan gagal',
        'deleted': 'Dipadam',
        'inserted': 'Dimasukkan',
        'notifications_sent': 'Pemberitahuan dihantar',
        'optional': 'Pilihan',

        // Pagination related
        'load_more': 'Muat Lebih Banyak',
        'loading_more': 'Memuat lebih banyak...',
        'no_more_data': 'Tiada lagi data',

        // Search related
        'search_documents': 'Cari dokumen, pelanggan, stok...',
        'search_results': 'Hasil Carian',
        'no_search_results': 'Tiada hasil yang sepadan ditemui',
        'clear_search': 'Kosongkan Carian',
        'search_keyword': 'Kata Kunci Carian',

        // Urgent priority feature
        'set_urgent_status': 'Tetapkan Status Kecemasan',
        'current_priority': 'Keutamaan Semasa',
        'priority_normal': 'Biasa',
        'priority_urgent': 'Kecemasan',
        'select_new_priority': 'Pilih Keutamaan Baru',
        'set_to_normal': 'Tetapkan ke Biasa',
        'set_to_urgent': 'Tetapkan ke Kecemasan',
        'cancel_urgent': 'Batal Kecemasan',
        'urgent_status_updated': 'Keutamaan Dikemas Kini',
        'set_to_normal_priority': 'Ditetapkan ke keutamaan biasa',
        'set_to_urgent_priority': 'Ditetapkan ke keutamaan kecemasan',
        'failed_to_set_priority': 'Gagal menetapkan keutamaan',
        'urgent_label': 'Kecemasan',
      };
}
