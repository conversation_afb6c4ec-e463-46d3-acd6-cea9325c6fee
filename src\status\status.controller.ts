import { <PERSON>, Get, Render, Query, Res } from '@nestjs/common';
import { StatusService } from './status.service';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import * as os from 'os';

@ApiTags('Status')
@Controller('status')
export class StatusController {
  constructor(private readonly statusService: StatusService) { }

  @ApiOperation({ summary: '获取系统状态页面' })
  @Get()
  @Render('status')
  async getStatusPage(@Query('floor') floor: string) {
    const [systemStatus, orderQueue] = await Promise.all([
      this.statusService.getSystemStatus(),
      this.statusService.getOrderQueue()
    ]);

    let floorStatus = null;

    if (floor) {
      floorStatus = await this.statusService.getFloorStatus(floor);
    }

    return {
      title: 'System Status',
      systemStatus,
      floorStatus,
      orderQueue,
      currentTime: new Date().toLocaleString(),
      refreshInterval: 30 // 自动刷新间隔（秒）
    };
  }

  @ApiOperation({ summary: '获取系统状态数据（JSON）' })
  @ApiQuery({ name: 'floor', required: false, description: '楼层，例如 2F' })
  @Get('data')
  async getStatusData(
    @Query('floor') floor: string,
    @Res() res: Response
  ) {
    const systemStatus = await this.statusService.getSystemStatus();
    let floorStatus = null;

    if (floor) {
      floorStatus = await this.statusService.getFloorStatus(floor);
    }

    res.json({
      systemStatus,
      floorStatus,
      currentTime: new Date().toLocaleString()
    });
  }

  @ApiOperation({ summary: '获取服务器IP地址信息' })
  @Get('server-info')
  getServerInfo() {
    // 获取服务器IP地址
    const networkInterfaces = os.networkInterfaces();
    const addresses = [];

    for (const name of Object.keys(networkInterfaces)) {
      for (const net of networkInterfaces[name]) {
        // 跳过内部IP和非IPv4地址
        if (!net.internal && net.family === 'IPv4') {
          addresses.push(net.address);
        }
      }
    }

    return {
      hostname: os.hostname(),
      addresses,
      port: 3000
    };
  }
}
