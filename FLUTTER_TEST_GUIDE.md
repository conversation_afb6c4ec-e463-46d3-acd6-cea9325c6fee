# 📱 Flutter前端UI测试指南

## 🎯 **测试目标**
验证QueueSystem重新同步功能和归还追踪系统在Flutter前端的工作流程。

## 📋 **测试前准备**

### **1. 确保后端服务运行**
```bash
# 在 backend_nestjs 目录下
npm run start:dev
```

### **2. 启动Flutter应用**
```bash
# 在 mobile_flutter/queue_system 目录下
cd ../mobile_flutter/queue_system
flutter run
```

## 🔄 **核心测试流程**

### **阶段一：验证重新同步功能**

#### **步骤1：登录Senior用户**
1. **打开Flutter应用**
2. **登录界面**：
   - 用户名：输入具有`senior`权限的用户
   - 密码：输入对应密码
   - 点击"登录"按钮

#### **步骤2：查看Waiting页面**
1. **导航到Waiting页面**：
   - 点击底部导航栏的"Waiting"标签
2. **查找重新同步按钮**：
   - 在订单卡片中查找橙色的刷新图标
   - **验证权限**：只有senior用户能看到此按钮

#### **步骤3：执行重新同步**
1. **点击重新同步按钮**：
   - 应该弹出警告对话框
   - 显示重新同步的影响说明
2. **输入原因并确认**：
   - 输入重新同步原因：如"测试重新同步功能"
   - 点击"确认"按钮
3. **观察反馈**：
   - 应该显示加载对话框
   - 成功后显示统计信息（删除数量、插入数量、通知数量）

### **阶段二：验证通知系统**

#### **步骤4：检查通知**
1. **点击右上角通知图标**：
   - 应该显示红色数字徽章（未读通知数量）
   - 点击进入通知列表
2. **验证通知内容**：
   - 应该看到"库存归还"类型的通知
   - 通知应该包含订单编号和物品信息

### **阶段三：验证API端点**

#### **步骤5：使用浏览器测试API**
1. **打开浏览器访问**：
   ```
   http://localhost:3000/stock-return-tracking/pending?staffId=1
   ```
2. **验证响应**：
   - 应该返回JSON格式的追踪记录
   - 检查数据结构是否正确

3. **测试统计API**：
   ```
   http://localhost:3000/stock-return-tracking/statistics?staffId=1
   ```
4. **验证统计数据**：
   - 应该返回total、completed、pending、completion_rate字段

## 🔍 **详细验证要点**

### **后端API验证**
- ✅ `/sync/refresh-order` - 重新同步订单
- ✅ `/stock-return-tracking/pending` - 获取待归还任务
- ✅ `/stock-return-tracking/statistics` - 获取统计信息
- ✅ `/stock-return-tracking/confirm` - 确认归还
- ✅ `/notification/unread/:userId` - 获取未读通知

### **前端功能验证**
- ✅ **权限控制**：Senior用户能看到重新同步按钮
- ✅ **重新同步对话框**：显示警告和原因输入
- ✅ **通知系统**：显示未读通知数量和列表
- ✅ **API集成**：CheckService包含所有必要的API方法

## 🐛 **常见问题排查**

### **问题1：看不到重新同步按钮**
**原因**：用户权限不足
**解决**：确认登录的是senior权限用户

### **问题2：API调用失败**
**原因**：后端服务未启动或端口不正确
**解决**：
```bash
# 检查服务状态
npm run start:dev
# 确认端口3000可访问
curl http://localhost:3000
```

### **问题3：Flutter编译错误**
**原因**：Provider方法不匹配
**解决**：检查以下文件是否存在：
- `lib/providers/stock_return_tracking_provider.dart`
- `lib/models/stock_return_tracking_model.dart`

## 📊 **测试结果记录**

### **基础功能测试**
- [ ] 后端服务启动成功
- [ ] API端点响应正常
- [ ] Flutter应用编译成功

### **重新同步功能测试**
- [ ] Senior用户登录成功
- [ ] 重新同步按钮显示正确
- [ ] 重新同步操作成功
- [ ] 统计信息显示正确

### **通知系统测试**
- [ ] 通知图标显示未读数量
- [ ] 通知列表显示正确
- [ ] 通知内容包含完整信息

### **API集成测试**
- [ ] 追踪记录API正常
- [ ] 统计信息API正常
- [ ] 确认归还API正常
- [ ] 通知API正常

## 🎉 **测试完成标准**

当以下核心功能都正常工作时，测试通过：

1. ✅ **重新同步功能**：Senior用户能成功重新同步订单
2. ✅ **API响应**：所有追踪相关API正常响应
3. ✅ **数据库功能**：追踪记录正确创建和更新
4. ✅ **通知系统**：相关员工收到通知
5. ✅ **权限控制**：不同权限用户看到对应功能

## 📝 **快速验证命令**

### **后端API测试**
```bash
# 测试追踪记录API
curl "http://localhost:3000/stock-return-tracking/pending?staffId=1"

# 测试统计API
curl "http://localhost:3000/stock-return-tracking/statistics?staffId=1"

# 测试重新同步API
curl -X POST "http://localhost:3000/sync/refresh-order" \
  -H "Content-Type: application/json" \
  -d '{"documentNo":"DO0001","supervisorId":2,"reason":"测试"}'
```

### **数据库验证**
```bash
# 检查追踪记录
node -e "
const { Client } = require('pg');
require('dotenv').config();
const client = new Client({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT) || 5432,
  user: process.env.POSTGRES_USERNAME || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DATABASE || 'demo',
});
client.connect().then(() => {
  return client.query('SELECT COUNT(*) FROM stock_return_tracking');
}).then(result => {
  console.log('追踪记录数量:', result.rows[0].count);
  return client.end();
}).catch(console.error);
"
```

通过这个测试指南，你可以快速验证QueueSystem重新同步和归还追踪功能的核心部分是否正常工作。
