---
title: "状态管理说明"
description: "队列系统 Flutter 移动应用的状态管理方案"
version: "1.0.0"
last_updated: "2025-05-19"
---

# 状态管理说明

## 概述

队列系统移动应用使用 Provider 包进行状态管理，采用 MVVM (Model-View-ViewModel) 架构模式。这种方式将业务逻辑与 UI 分离，使代码更易于维护和测试。

## Provider 架构

### 1. 模型层 (Model)

模型层定义了应用的数据结构，位于 `lib/models/` 目录下：

- `staff_model.dart` - 员工数据模型
- `delivery_order_main_model.dart` - 出货单主表模型
- `delivery_order_detail_model.dart` - 出货单明细模型
- `notification_model.dart` - 通知模型

### 2. 视图模型层 (ViewModel)

视图模型层负责业务逻辑和状态管理，位于 `lib/providers/` 目录下：

- `auth_provider.dart` - 认证和用户会话管理
- `check_provider.dart` - 货物检查相关逻辑
- `shipping_provider.dart` - 出货和送货相关逻辑
- `notification_provider.dart` - 通知系统逻辑
- `pdf_provider.dart` - PDF 生成和签名逻辑

### 3. 视图层 (View)

视图层是用户界面，位于 `lib/screens/` 和 `lib/widgets/` 目录下，通过 `Consumer` 或 `context.watch()` 监听状态变化。

## 状态管理实现

### 1. 全局状态

使用 `MultiProvider` 在应用根部提供全局状态：

```dart
void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        // 其他全局 Provider
      ],
      child: MyApp(),
    ),
  );
}
```

### 2. 局部状态

对于仅在特定页面使用的状态，在页面级别提供 Provider：

```dart
class CheckScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => CheckProvider(),
      child: CheckScreenContent(),
    );
  }
}
```

### 3. 状态依赖

当一个 Provider 依赖另一个 Provider 时，使用 `ProxyProvider`：

```dart
ProxyProvider<AuthProvider, ShippingProvider>(
  update: (context, auth, previous) => 
    ShippingProvider(token: auth.token, previous: previous),
)
```

## 主要 Provider 说明

### 1. AuthProvider

负责用户认证和会话管理：

```dart
class AuthProvider with ChangeNotifier {
  Staff? _currentUser;
  String? _token;
  
  Staff? get currentUser => _currentUser;
  String? get token => _token;
  bool get isAuthenticated => _token != null;
  
  Future<bool> login(String username, String password) async {
    // 实现登录逻辑
  }
  
  void logout() {
    // 实现登出逻辑
  }
}
```

### 2. CheckProvider

负责货物检查相关逻辑：

```dart
class CheckProvider with ChangeNotifier {
  List<DeliveryOrderDetail> _checkList = [];
  bool _isLoading = false;
  
  List<DeliveryOrderDetail> get checkList => _checkList;
  bool get isLoading => _isLoading;
  
  Future<void> fetchCheckList(int staffId) async {
    // 获取待检查列表
  }
  
  Future<bool> checkItem(String documentNo, int line, int staffId) async {
    // 执行检查操作
  }
}
```

### 3. ShippingProvider

负责出货和送货相关逻辑：

```dart
class ShippingProvider with ChangeNotifier {
  List<DeliveryOrderMain> _readyDocuments = [];
  List<DeliveryOrderMain> _deliveryList = [];
  
  List<DeliveryOrderMain> get readyDocuments => _readyDocuments;
  List<DeliveryOrderMain> get deliveryList => _deliveryList;
  
  Future<void> fetchReadyDocuments(int staffId) async {
    // 获取可出货订单
  }
  
  Future<bool> completeDocument(String documentNo, int supervisorId, int? driverId) async {
    // 标记订单为已出货
  }
}
```

## 状态更新和通知

当状态发生变化时，使用 `notifyListeners()` 通知所有监听者：

```dart
Future<bool> checkItem(String documentNo, int line, int staffId) async {
  _isLoading = true;
  notifyListeners();
  
  try {
    // API 调用和状态更新
    _checkList = _checkList.map((item) {
      if (item.documentNo == documentNo && item.line == line) {
        return item.copyWith(staffChecked: true);
      }
      return item;
    }).toList();
    
    _isLoading = false;
    notifyListeners();
    return true;
  } catch (e) {
    _isLoading = false;
    notifyListeners();
    return false;
  }
}
```

## 最佳实践

1. **状态隔离**：将不同功能的状态分离到不同的 Provider 中
2. **懒加载**：使用 `ChangeNotifierProxyProvider.lazy` 延迟创建 Provider
3. **状态持久化**：使用 `shared_preferences` 或 `hive` 持久化关键状态
4. **错误处理**：在 Provider 中统一处理错误，并提供错误状态
5. **加载状态**：提供加载状态标志，用于显示加载指示器
6. **缓存管理**：实现缓存机制，减少不必要的网络请求
