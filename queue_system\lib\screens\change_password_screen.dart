import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/staff_provider.dart';
import 'package:queue_system/models/staff.dart';

class ChangePasswordScreen extends StatefulWidget {
  final Staff staff;

  const ChangePasswordScreen({super.key, required this.staff});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('change_password')),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.t('staff_info'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text('${context.t('username')}: ${widget.staff.username}'),
                      Text('${context.t('full_name')}: ${widget.staff.fullName}'),
                      Text('${context.t('level')}: ${_getLevelDisplayName(context, widget.staff.level)}'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              TextFormField(
                controller: _newPasswordController,
                decoration: InputDecoration(
                  labelText: context.t('new_password'),
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: Icon(_obscureNewPassword ? Icons.visibility : Icons.visibility_off),
                    onPressed: () {
                      setState(() {
                        _obscureNewPassword = !_obscureNewPassword;
                      });
                    },
                  ),
                ),
                obscureText: _obscureNewPassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.t('password_required');
                  }
                  if (value.length < 6) {
                    return context.t('password_min_length');
                  }
                  return null;
                },
                onChanged: (value) {
                  // 防止输入空格
                  if (value.contains(' ')) {
                    final newValue = value.replaceAll(' ', '');
                    _newPasswordController.value = _newPasswordController.value.copyWith(
                      text: newValue,
                      selection: TextSelection.collapsed(offset: newValue.length),
                    );
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _confirmPasswordController,
                decoration: InputDecoration(
                  labelText: context.t('confirm_password'),
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                ),
                obscureText: _obscureConfirmPassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.t('confirm_password_required');
                  }
                  if (value != _newPasswordController.text) {
                    return context.t('passwords_not_match');
                  }
                  return null;
                },
                onChanged: (value) {
                  // 防止输入空格
                  if (value.contains(' ')) {
                    final newValue = value.replaceAll(' ', '');
                    _confirmPasswordController.value = _confirmPasswordController.value.copyWith(
                      text: newValue,
                      selection: TextSelection.collapsed(offset: newValue.length),
                    );
                  }
                },
              ),
              const SizedBox(height: 24),
              Consumer<StaffProvider>(
                builder: (context, staffProvider, child) {
                  if (staffProvider.isLoading) {
                    return const Center(
                      child: Text(
                        '处理中...',
                        style: TextStyle(color: Colors.orange),
                      ),
                    );
                  }

                  return ElevatedButton(
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        final success = await staffProvider.changePassword(
                          widget.staff.id,
                          _newPasswordController.text,
                        );
                        
                        if (success && mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(context.t('password_changed_successfully')),
                              backgroundColor: Colors.green,
                            ),
                          );
                          Navigator.of(context).pop();
                        } else if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(staffProvider.error ?? context.t('password_change_failed')),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    child: Text(context.t('change_password')),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getLevelDisplayName(BuildContext context, String level) {
    switch (level) {
      case 'senior':
        return context.t('supervisor');
      case 'staff_bom':
        return context.t('bom_specialist');
      case 'driver':
        return context.t('driver');
      default:
        return context.t('regular_staff');
    }
  }
}
