import { Test, TestingModule } from '@nestjs/testing';
import { PostgresModule } from './postgres.module';
import { ConfigModule } from '@nestjs/config';
import databaseConfig from '../config/database.config';

jest.mock('@nestjs/typeorm', () => ({
  TypeOrmModule: {
    forRootAsync: jest.fn().mockReturnValue({
      module: class MockModule {},
      providers: [],
    }),
  },
}));

describe('PostgresModule', () => {
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [databaseConfig],
        }),
        PostgresModule,
      ],
    }).compile();
  }, 30000); // 增加超时时间到30秒

  it('should be defined', () => {
    expect(module).toBeDefined();
  });
}); 