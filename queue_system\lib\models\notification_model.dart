import 'package:flutter/foundation.dart';

class NotificationModel {
  final int id;
  final String type;
  final String message;
  final int recipientId;
  final int? senderId;
  final String? documentNo;
  final int? line;
  final int? detailId;
  final bool read;
  final DateTime? readAt;
  final String? rejectReason;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.type,
    required this.message,
    required this.recipientId,
    this.senderId,
    this.documentNo,
    this.line,
    this.detailId,
    required this.read,
    this.readAt,
    this.rejectReason,
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    try {
      return NotificationModel(
        id: json['id'],
        type: json['type'],
        message: json['message'],
        recipientId: json['recipient_id'],
        senderId: json['sender_id'],
        documentNo: json['document_no'],
        line: json['line'],
        detailId: json['detail_id'],
        read: json['read'] ?? false,
        readAt:
            json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
        rejectReason: json['reject_reason'],
        createdAt: DateTime.parse(json['created_at']),
      );
    } catch (e) {
      debugPrint('Error parsing NotificationModel: $e');
      debugPrint('JSON: $json');
      // 返回一个默认的通知模型，避免应用崩溃
      return NotificationModel(
        id: 0,
        type: 'error',
        message: '解析通知数据失败',
        recipientId: 0,
        read: false,
        createdAt: DateTime.now(),
      );
    }
  }
}
