// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shipping_document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShippingDocument _$ShippingDocumentFromJson(Map<String, dynamic> json) =>
    ShippingDocument(
      id: (json['id'] as num).toInt(),
      documentNo: json['document_no'] as String,
      documentDate: DateTime.parse(json['document_date'] as String),
      customerCode: json['customer'] as String,
      customerName: json['customer_name'] as String,
      customerEmail: json['customer_email'] as String?,
      customerDeliverAddress: json['customer_deliver_address'] as String?,
      customerTelephone: json['customer_telephone'] as String?,
      customerFax: json['customer_fax'] as String?,
      salesmanCode: json['salesman_code'] as String?,
      issueBy: json['issue_by'] as String?,
      transporterCode: json['transporter_code'] as String?,
      transporterName: json['transporter_name'] as String?,
      remarks: json['remarks'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      isShipped: json['is_shipped'] as bool,
      shippedAt: json['shipped_at'] == null
          ? null
          : DateTime.parse(json['shipped_at'] as String),
      shippedBy: (json['shipped_by'] as num?)?.toInt(),
      delivered: json['delivered'] as bool?,
      deliveredAt: json['delivered_at'] as String?,
      deliveredBy: (json['delivered_by'] as num?)?.toInt(),
      deliveryNotes: json['delivery_notes'] as String?,
      driverId: (json['driver_id'] as num?)?.toInt(),
      rejectionReason: json['rejection_reason'] as String?,
      rejectedBy: (json['rejected_by'] as num?)?.toInt(),
      rejectedAt: json['rejected_at'] as String?,
      driverUsername: json['driver_username'] as String?,
      priority: (json['priority'] as num?)?.toInt() ?? 0,
      readyToShip: json['ready_to_ship'] as bool?,
      unconfirmedItems: (json['unconfirmed_items'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$ShippingDocumentToJson(ShippingDocument instance) =>
    <String, dynamic>{
      'id': instance.id,
      'document_no': instance.documentNo,
      'document_date': instance.documentDate.toIso8601String(),
      'customer': instance.customerCode,
      'customer_name': instance.customerName,
      'customer_email': instance.customerEmail,
      'customer_deliver_address': instance.customerDeliverAddress,
      'customer_telephone': instance.customerTelephone,
      'customer_fax': instance.customerFax,
      'salesman_code': instance.salesmanCode,
      'issue_by': instance.issueBy,
      'transporter_code': instance.transporterCode,
      'transporter_name': instance.transporterName,
      'remarks': instance.remarks,
      'created_at': instance.createdAt.toIso8601String(),
      'is_shipped': instance.isShipped,
      'shipped_at': instance.shippedAt?.toIso8601String(),
      'shipped_by': instance.shippedBy,
      'delivered': instance.delivered,
      'delivered_at': instance.deliveredAt,
      'delivered_by': instance.deliveredBy,
      'delivery_notes': instance.deliveryNotes,
      'driver_id': instance.driverId,
      'rejection_reason': instance.rejectionReason,
      'rejected_by': instance.rejectedBy,
      'rejected_at': instance.rejectedAt,
      'driver_username': instance.driverUsername,
      'priority': instance.priority,
      'ready_to_ship': instance.readyToShip,
      'unconfirmed_items': instance.unconfirmedItems,
    };
