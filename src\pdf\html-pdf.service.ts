import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Main } from '../postgres/entities/main.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { FirebaseService, FirebaseUploadResult } from '../firebase/firebase.service';
import * as fs from 'fs';
import * as path from 'path';
import * as fsExtra from 'fs-extra';
import * as Handlebars from 'handlebars';
import * as puppeteer from 'puppeteer';
import { findChrome } from './chrome-finder';

export interface PdfGenerationResult {
  pdfPath: string;
  firebaseUpload: FirebaseUploadResult;
}

@Injectable()
export class HtmlPdfService {
  constructor(
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    private readonly firebaseService: FirebaseService,
  ) {
    // 注册Handlebars助手函数
    Handlebars.registerHelper('formatDate', function (date) {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    });

    Handlebars.registerHelper('formatNumber', function (num) {
      if (num === undefined || num === null) return '0.00';
      return parseFloat(num).toFixed(2);
    });

    Handlebars.registerHelper('formatQuantity', function (quantity) {
      if (quantity === undefined || quantity === null) return '0';
      return Math.floor(parseFloat(quantity) || 0);
    });

    // 注册文本截断助手函数
    Handlebars.registerHelper('truncateText', function (text, maxLength) {
      if (!text) return '';
      if (typeof text !== 'string') text = String(text);
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    });


  }

  /**
   * 生成订单PDF并保存到指定目录
   * @param documentNo 订单编号
   * @returns 生成的PDF文件路径
   */
  async generateOrderPdf(documentNo: string): Promise<string> {
    console.log(`[HTML-PDF] 开始生成订单PDF，文档编号: ${documentNo}`);

    // 获取订单主表数据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!main) {
      console.error(`[HTML-PDF] 找不到文档: ${documentNo}`);
      throw new Error(`Document ${documentNo} not found`);
    }

    console.log(`[HTML-PDF] 找到文档: ${documentNo}, 客户: ${main.customer_name}`);


    // 获取订单明细数据
    let details = await this.detailRepository.find({
      where: { main_id: main.id },
    });

    // 按BOM group和类型排序明细数据
    details = this.sortDetailsByBomGroup(details);

    // 不再使用remarks作为地址行
    const addressLines = [];

    // 计算总金额
    console.log(`[HTML-PDF] Starting total calculation with ${details.length} details`);
    details.forEach((detail, index) => {
      console.log(`[HTML-PDF] Detail ${index + 1}: stock=${detail.stock}, unit_price=${detail.unit_price}, total_amount=${detail.total_amount}, quantity=${detail.quantity}, is_bom_parent=${detail.is_bom_parent}`);
    });

    let totalAmount = 0;
    const processedDetails = details.map(detail => {
      // 使用detail中的unit_price和total_amount字段，确保转换为数字
      const unitPrice = parseFloat(String(detail.unit_price || 0));
      const totalAmountFromDb = parseFloat(String(detail.total_amount || 0));
      const quantity = parseFloat(String(detail.quantity || 0));
      const amount = totalAmountFromDb || (quantity * unitPrice);

      console.log(`[HTML-PDF] Processing ${detail.stock}: quantity=${detail.quantity} (type: ${typeof detail.quantity}), unitPrice=${unitPrice} (type: ${typeof unitPrice}), total_amount=${detail.total_amount} (type: ${typeof detail.total_amount}), calculated amount=${amount} (type: ${typeof amount})`);

      // 只有当金额大于0时才计入总金额，避免BOM子项的0金额影响计算
      if (amount > 0) {
        totalAmount += amount;
        console.log(`[HTML-PDF] Added to total: ${detail.stock} - ${amount}, running total: ${totalAmount}`);
      } else {
        console.log(`[HTML-PDF] Skipped zero amount: ${detail.stock} - ${amount}`);
      }



      return {
        ...detail,
        unitPrice,
        amount,
        brand: detail.brand_code || '', // 使用brand_code字段
        quantity: detail.quantity || 0, // 确保quantity不为null/undefined
        uom: detail.uom || 'pcs' // 添加单位
      };
    });

    // 创建输出目录 - 使用订单相关日期而不是当前日期
    let dateStr: string;
    if (main.shipped_at) {
      // 优先使用出货日期
      const shippedDate = new Date(main.shipped_at);
      dateStr = `${shippedDate.getFullYear()}-${String(shippedDate.getMonth() + 1).padStart(2, '0')}-${String(shippedDate.getDate()).padStart(2, '0')}`;
      console.log(`[HTML-PDF] 使用出货日期: ${dateStr}`);
    } else if (main.document_date) {
      // 其次使用文档日期
      const docDate = new Date(main.document_date);
      dateStr = `${docDate.getFullYear()}-${String(docDate.getMonth() + 1).padStart(2, '0')}-${String(docDate.getDate()).padStart(2, '0')}`;
      console.log(`[HTML-PDF] 使用文档日期: ${dateStr}`);
    } else {
      // 最后使用当前日期
      const today = new Date();
      dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      console.log(`[HTML-PDF] 使用当前日期: ${dateStr}`);
    }

    const baseDir = process.env.PDF_OUTPUT_DIR || path.join(__dirname, '..', '..', 'PDF_Output');
    const outputDir = path.join(baseDir, dateStr);

    console.log(`[HTML-PDF Service] 工作目录: ${process.cwd()}`);
    console.log(`[HTML-PDF Service] PDF基础目录: ${baseDir}`);
    console.log(`[HTML-PDF Service] 输出目录: ${outputDir}`);
    console.log(`[HTML-PDF] 输出目录: ${outputDir}`);

    try {
      // 确保目录存在
      await fsExtra.ensureDir(outputDir);
      console.log(`[HTML-PDF] 目录创建成功: ${outputDir}`);
    } catch (error) {
      console.error(`[HTML-PDF] 创建目录失败: ${error.message}`);
      // 尝试使用同步方法创建目录
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`[HTML-PDF] 使用同步方法创建目录成功: ${outputDir}`);
      }
    }

    // 创建PDF文件路径
    const pdfPath = path.join(outputDir, `${documentNo}.pdf`);
    console.log(`[HTML-PDF] PDF文件路径: ${pdfPath}`);

    // 读取HTML模板
    const templatePath = path.join(process.cwd(), 'src', 'pdf', 'templates', 'delivery-order.hbs');
    console.log(`[HTML-PDF] 模板路径: ${templatePath}`);

    let templateContent;
    try {
      templateContent = fs.readFileSync(templatePath, 'utf8');
      console.log(`[HTML-PDF] 模板读取成功，长度: ${templateContent.length}`);
    } catch (error) {
      console.error(`[HTML-PDF] 读取模板失败: ${error.message}`);
      throw error;
    }

    // 编译模板
    const template = Handlebars.compile(templateContent);

    // 获取主管和司机信息
    let supervisorName = '';
    let driverName = '';

    if (main.shipped_by) {
      const supervisor = await this.staffRepository.findOne({
        where: { id: main.shipped_by }
      });
      if (supervisor) {
        supervisorName = supervisor.username;
      }
    }

    // 优先使用 driver_id，如果为空则使用 delivered_by
    const driverId = main.driver_id || main.delivered_by;
    if (driverId) {
      const driver = await this.staffRepository.findOne({
        where: { id: driverId }
      });
      if (driver) {
        driverName = driver.username;
      }
    }

    // 获取每个明细项的检查员工信息
    const detailsWithStaffInfo = await Promise.all(
      processedDetails.map(async detail => {
        let staffName = '';
        if (detail.staff_id) {
          const staff = await this.staffRepository.findOne({
            where: { id: detail.staff_id }
          });
          if (staff) {
            staffName = staff.username;
          }
        }
        return {
          ...detail,
          staffName
        };
      })
    );

    console.log(`[HTML-PDF] Final total amount: ${totalAmount}`);
    console.log(`[HTML-PDF] Total details processed: ${processedDetails.length}`);

    // 准备模板数据
    const templateData = {
      main,
      details: detailsWithStaffInfo,
      addressLines,
      totalAmount,
      generatedDate: new Date().toLocaleString(),
      issuedBy: main.issue_by || '', // 使用main.issue_by字段
      supervisorName,
      driverName,
      signatureBase64: main.customer_signature || '' // 从数据库读取签名数据
    };

    // 渲染HTML
    const html = template(templateData);

    // 使用Puppeteer生成PDF
    let browser: puppeteer.Browser;
    try {
      console.log('启动Puppeteer浏览器...');
      // 查找Chrome可执行文件
      const chromePath = process.platform === 'win32' ? findChrome() : undefined;
      console.log(`使用浏览器路径: ${chromePath || '默认'}`);

      browser = await puppeteer.launch({
        headless: true, // 使用无头模式
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        executablePath: chromePath
      });

      console.log('创建新页面...');
      const page = await browser.newPage();

      console.log('设置HTML内容...');
      await page.setContent(html, { waitUntil: 'networkidle0' });

      console.log('生成PDF文件...');
      await page.pdf({
        path: pdfPath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '10mm',
          right: '10mm',
          bottom: '10mm',
          left: '10mm'
        },
        displayHeaderFooter: false,
        preferCSSPageSize: true
      });

      console.log('PDF生成成功:', pdfPath);
    } catch (error) {
      console.error('Puppeteer PDF生成错误:', error);
      throw error;
    } finally {
      if (browser) {
        console.log('关闭浏览器...');
        await browser.close();
      }
    }

    // PDF生成成功后，尝试上传到Firebase Storage
    await this.uploadPdfToFirebase(pdfPath, documentNo);

    return pdfPath;
  }

  /**
   * 生成订单PDF并返回详细结果（包含Firebase上传信息）
   * @param documentNo 订单编号
   * @returns PDF生成结果和Firebase上传结果
   */
  async generateOrderPdfWithDetails(documentNo: string): Promise<PdfGenerationResult> {
    // 生成PDF
    const pdfPath = await this.generateOrderPdf(documentNo);

    // 由于generateOrderPdf已经包含了Firebase上传，我们需要重新获取上传结果
    // 这里我们创建一个简化的上传状态检查
    const firebaseStatus = this.firebaseService.getStatus();
    let firebaseUpload: FirebaseUploadResult;

    if (!firebaseStatus.enabled) {
      firebaseUpload = {
        success: true,
        skipped: true,
        skipReason: 'Firebase上传功能已禁用',
      };
    } else if (!firebaseStatus.initialized) {
      firebaseUpload = {
        success: false,
        error: 'Firebase服务未初始化',
      };
    } else {
      firebaseUpload = {
        success: true,
        firebasePath: `pdfs/${new Date().toISOString().split('T')[0]}/${documentNo}.pdf`,
      };
    }

    return {
      pdfPath,
      firebaseUpload,
    };
  }

  /**
   * 将签名添加到现有的PDF文件中
   * @param documentNo 订单编号
   * @param signatureBase64 签名的Base64编码
   * @param date 可选的日期参数，默认为当天
   * @returns 更新后的PDF文件路径
   */
  async addSignatureToPdf(documentNo: string, signatureBase64: string, date?: string): Promise<string> {
    console.log(`[HTML-PDF] 开始添加签名到PDF，文档编号: ${documentNo}`);
    console.log(`[HTML-PDF] 签名数据长度: ${signatureBase64 ? signatureBase64.length : 0}`);

    // 获取订单主表数据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!main) {
      throw new Error(`Document ${documentNo} not found`);
    }

    // 确定使用的日期 - 优先使用出货日期，其次使用提供的日期，最后使用当前日期
    let dateStr = date;
    if (!dateStr) {
      if (main.shipped_at) {
        const shippedDate = new Date(main.shipped_at);
        dateStr = `${shippedDate.getFullYear()}-${String(shippedDate.getMonth() + 1).padStart(2, '0')}-${String(shippedDate.getDate()).padStart(2, '0')}`;
        console.log(`[HTML-PDF] 使用出货日期: ${dateStr}`);
      } else {
        const today = new Date();
        dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
        console.log(`[HTML-PDF] 使用当前日期: ${dateStr}`);
      }
    }

    // 保存签名数据到数据库
    console.log(`[HTML-PDF] 保存签名数据到数据库`);
    await this.mainRepository.update(
      { document_no: documentNo },
      {
        customer_signature: signatureBase64,
        signature_date: dateStr,
        signed_at: new Date(),
      }
    );
    console.log(`[HTML-PDF] 签名数据已保存到数据库`);

    // 智能确定PDF文件路径
    const pdfPath = await this.findPdfPath(documentNo, main, dateStr);
    console.log(`[HTML-PDF] 智能确定的PDF文件路径: ${pdfPath}`);

    // 检查文件是否存在，如果不存在则生成
    if (!fs.existsSync(pdfPath)) {
      console.log(`[HTML-PDF] PDF文件不存在，需要生成: ${pdfPath}`);

      try {
        await this.generateOrderPdf(documentNo);
        console.log(`[HTML-PDF] PDF文件生成成功`);
      } catch (genError) {
        console.error(`[HTML-PDF] 生成PDF文件失败: ${genError.message}`);
        throw new Error(`Failed to generate PDF file for document ${documentNo}: ${genError.message}`);
      }
    } else {
      console.log(`[HTML-PDF] 找到现有PDF文件，将重新生成包含签名的版本: ${pdfPath}`);
    }

    // 重新获取订单主表数据（包含最新的签名信息）
    const updatedMain = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!updatedMain) {
      throw new Error(`Document ${documentNo} not found`);
    }

    // 获取订单明细数据
    let details = await this.detailRepository.find({
      where: { main_id: updatedMain.id },
    });

    // 按BOM group和类型排序明细数据
    details = this.sortDetailsByBomGroup(details);

    // 不再使用remarks作为地址行
    const addressLines = [];

    // 计算总金额
    console.log(`[HTML-PDF] Starting total calculation (signature) with ${details.length} details`);
    details.forEach((detail, index) => {
      console.log(`[HTML-PDF] Detail ${index + 1} (signature): stock=${detail.stock}, unit_price=${detail.unit_price}, total_amount=${detail.total_amount}, quantity=${detail.quantity}, is_bom_parent=${detail.is_bom_parent}`);
    });

    let totalAmount = 0;
    const processedDetails = details.map(detail => {
      // 使用detail中的unit_price和total_amount字段，确保转换为数字
      const unitPrice = parseFloat(String(detail.unit_price || 0));
      const totalAmountFromDb = parseFloat(String(detail.total_amount || 0));
      const quantity = parseFloat(String(detail.quantity || 0));
      const amount = totalAmountFromDb || (quantity * unitPrice);

      // 只有当金额大于0时才计入总金额，避免BOM子项的0金额影响计算
      if (amount > 0) {
        totalAmount += amount;
        console.log(`[HTML-PDF] Added to total (signature): ${detail.stock} - ${amount}, running total: ${totalAmount}`);
      } else {
        console.log(`[HTML-PDF] Skipped zero amount (signature): ${detail.stock} - ${amount}`);
      }



      return {
        ...detail,
        unitPrice,
        amount,
        brand: detail.brand_code || '', // 使用brand_code字段
        quantity: detail.quantity || 0, // 确保quantity不为null/undefined
        uom: detail.uom || 'pcs' // 添加单位
      };
    });

    // 读取HTML模板
    const templatePath = path.join(process.cwd(), 'src', 'pdf', 'templates', 'delivery-order.hbs');
    const templateContent = fs.readFileSync(templatePath, 'utf8');

    // 编译模板
    const template = Handlebars.compile(templateContent);

    // 获取主管和司机信息
    let supervisorName = '';
    let driverName = '';

    if (updatedMain.shipped_by) {
      const supervisor = await this.staffRepository.findOne({
        where: { id: updatedMain.shipped_by }
      });
      if (supervisor) {
        supervisorName = supervisor.username;
      }
    }

    // 优先使用 driver_id，如果为空则使用 delivered_by
    const driverId = updatedMain.driver_id || updatedMain.delivered_by;
    if (driverId) {
      const driver = await this.staffRepository.findOne({
        where: { id: driverId }
      });
      if (driver) {
        driverName = driver.username;
      }
    }

    // 获取每个明细项的检查员工信息
    const detailsWithStaffInfo = await Promise.all(
      processedDetails.map(async detail => {
        let staffName = '';
        if (detail.staff_id) {
          const staff = await this.staffRepository.findOne({
            where: { id: detail.staff_id }
          });
          if (staff) {
            staffName = staff.username;
          }
        }
        return {
          ...detail,
          staffName
        };
      })
    );

    console.log(`[HTML-PDF] Final total amount (signature): ${totalAmount}`);

    // 准备模板数据
    const templateData = {
      main: updatedMain,
      details: detailsWithStaffInfo,
      addressLines,
      totalAmount,
      signatureBase64: updatedMain.customer_signature || signatureBase64, // 优先使用数据库中的签名
      generatedDate: new Date().toLocaleString(),
      signedDate: new Date().toLocaleString(),
      issuedBy: updatedMain.issue_by || '', // 使用main.issue_by字段
      supervisorName,
      driverName
    };

    // 渲染HTML
    const html = template(templateData);

    // 创建临时文件路径
    const pdfDir = path.dirname(pdfPath);
    const tempPdfPath = path.join(pdfDir, `${documentNo}_signed_temp.pdf`);

    // 使用Puppeteer生成PDF
    let browser: puppeteer.Browser;
    try {
      console.log('启动Puppeteer浏览器(签名版)...');
      // 查找Chrome可执行文件
      const chromePath = process.platform === 'win32' ? findChrome() : undefined;
      console.log(`使用浏览器路径(签名版): ${chromePath || '默认'}`);

      browser = await puppeteer.launch({
        headless: true, // 使用无头模式
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        executablePath: chromePath
      });

      console.log('创建新页面(签名版)...');
      const page = await browser.newPage();

      console.log('设置HTML内容(签名版)...');
      await page.setContent(html, { waitUntil: 'networkidle0' });

      console.log('生成PDF文件(签名版)...');
      await page.pdf({
        path: tempPdfPath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '10mm',
          right: '10mm',
          bottom: '10mm',
          left: '10mm'
        },
        displayHeaderFooter: false,
        preferCSSPageSize: true
      });

      console.log('签名PDF生成成功:', tempPdfPath);
    } catch (error) {
      console.error('Puppeteer签名PDF生成错误:', error);
      throw error;
    } finally {
      if (browser) {
        console.log('关闭浏览器(签名版)...');
        await browser.close();
      }
    }

    // 替换原始文件
    fs.unlinkSync(pdfPath);
    fs.renameSync(tempPdfPath, pdfPath);

    // 签名PDF生成成功后，尝试上传到Firebase Storage
    await this.uploadPdfToFirebase(pdfPath, documentNo);

    return pdfPath;
  }

  /**
   * 调试总金额计算 - 仅用于测试
   */
  async debugTotalAmount(documentNo: string): Promise<any> {
    console.log(`[HTML-PDF] Debug total amount for document: ${documentNo}`);

    // 获取订单主表数据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!main) {
      throw new Error(`Document ${documentNo} not found`);
    }

    // 获取订单明细数据
    let details = await this.detailRepository.find({
      where: { main_id: main.id },
    });

    // 按BOM group排序
    details = this.sortDetailsByBomGroup(details);

    console.log(`[HTML-PDF] Found ${details.length} details for document ${documentNo}`);

    let totalAmount = 0;
    const debugInfo = details.map((detail, index) => {
      const unitPrice = parseFloat(String(detail.unit_price || 0));
      const totalAmountFromDb = parseFloat(String(detail.total_amount || 0));
      const quantity = parseFloat(String(detail.quantity || 0));
      const amount = totalAmountFromDb || (quantity * unitPrice);

      const willAdd = amount > 0;
      if (willAdd) {
        totalAmount += amount;
      }

      return {
        index: index + 1,
        stock: detail.stock,
        description: detail.description,
        line: detail.line,
        is_bom_parent: detail.is_bom_parent,
        bom_group: detail.bom_group,
        bom_parent_id: detail.bom_parent_id,
        parent_code: detail.parent_code,
        quantity: detail.quantity,
        quantity_type: typeof detail.quantity,
        quantity_parsed: quantity,
        unit_price: detail.unit_price,
        total_amount: detail.total_amount,
        calculated_amount: amount,
        will_add_to_total: willAdd,
        running_total: willAdd ? totalAmount : 'not added'
      };
    });

    return {
      document_no: documentNo,
      total_details: details.length,
      final_total_amount: totalAmount,
      details: debugInfo
    };
  }

  /**
   * Sort details by BOM hierarchy
   * Sorting rules:
   * 1. Regular items (no bom_group) first, sorted by line number
   * 2. Each BOM parent immediately followed by its children
   * 3. Children sorted by line number within each parent group
   * 4. Multiple BOM groups sorted by parent's line number
   */
  private sortDetailsByBomGroup(details: any[]): any[] {
    console.log(`[HTML-PDF] Sorting ${details.length} details by BOM hierarchy`);

    // Display original order
    console.log('[HTML-PDF] Before sorting:');
    details.forEach((detail, index) => {
      console.log(`  ${index + 1}. ${detail.stock} - line:${detail.line}, bom_group:${detail.bom_group || 'null'}, is_bom_parent:${detail.is_bom_parent}, parent_code:${detail.parent_code || 'null'}`);
    });

    // Separate regular items and BOM items
    const regularItems = details.filter(d => !d.bom_group);
    const bomItems = details.filter(d => d.bom_group);

    // Sort regular items by line number
    regularItems.sort((a, b) => a.line - b.line);

    // Group BOM items by parent
    const bomParents = bomItems.filter(d => d.is_bom_parent);
    const bomChildren = bomItems.filter(d => !d.is_bom_parent);

    // Sort BOM parents by line number
    bomParents.sort((a, b) => a.line - b.line);

    // Build final sorted array
    const sorted = [...regularItems];

    // For each BOM parent, add it followed immediately by its children
    bomParents.forEach(parent => {
      // Add the parent
      sorted.push(parent);

      // Find children for this parent
      const children = bomChildren.filter(child => {
        // Match by parent_code first (more reliable)
        if (child.parent_code && child.parent_code === parent.stock) {
          return true;
        }
        // Fallback to bom_parent_id if parent_code is not available
        if (!child.parent_code && child.bom_parent_id === parent.id) {
          return true;
        }
        return false;
      });

      // Sort children by line number and add them with first-child marking
      children.sort((a, b) => a.line - b.line);
      children.forEach((child, index) => {
        // Mark the first child for special CSS treatment
        child.isFirstBomChild = index === 0;
        sorted.push(child);
      });

      console.log(`[HTML-PDF] Added BOM parent ${parent.stock} (line:${parent.line}) with ${children.length} children: ${children.map(c => `${c.stock}(${c.line})`).join(', ')}`);
    });

    // Add any orphaned children (children without matching parents)
    const processedChildrenIds = new Set();
    bomParents.forEach(parent => {
      const children = bomChildren.filter(child =>
        (child.parent_code && child.parent_code === parent.stock) ||
        (!child.parent_code && child.bom_parent_id === parent.id)
      );
      children.forEach(child => processedChildrenIds.add(child.id));
    });

    const orphanChildren = bomChildren.filter(child => !processedChildrenIds.has(child.id));
    if (orphanChildren.length > 0) {
      console.log(`[HTML-PDF] Found ${orphanChildren.length} orphan BOM children: ${orphanChildren.map(c => c.stock).join(', ')}`);
      orphanChildren.sort((a, b) => a.line - b.line);
      sorted.push(...orphanChildren);
    }

    // Display final sorted order
    console.log('[HTML-PDF] After sorting:');
    sorted.forEach((detail, index) => {
      const indent = detail.is_bom_parent ? '' : '  ';
      const type = detail.bom_group ? (detail.is_bom_parent ? 'BOM-Parent' : 'BOM-Child') : 'Regular';
      const firstChild = detail.isFirstBomChild ? ' (FIRST)' : '';
      console.log(`  ${index + 1}. ${indent}${detail.stock} - line:${detail.line}, type:${type}${firstChild}, parent_code:${detail.parent_code || 'null'}`);
    });

    return sorted;
  }

  /**
   * 智能确定PDF文件路径
   * 根据订单信息确定正确的PDF文件路径
   */
  private async findPdfPath(documentNo: string, main: any, fallbackDate: string): Promise<string> {
    const baseDir = process.env.PDF_OUTPUT_DIR || path.join(__dirname, '..', '..', 'PDF_Output');

    // 尝试多个可能的日期路径
    const possibleDates: Date[] = [];

    // 优先使用出货日期
    if (main.shipped_at) {
      possibleDates.push(new Date(main.shipped_at));
    }

    // 其次使用文档日期
    if (main.document_date) {
      possibleDates.push(new Date(main.document_date));
    }

    // 最后使用创建日期
    if (main.created_at) {
      possibleDates.push(new Date(main.created_at));
    }

    // 检查每个可能的日期路径
    for (const date of possibleDates) {
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const pdfPath = path.join(baseDir, dateStr, `${documentNo}.pdf`);

      console.log(`[HTML-PDF] 检查路径: ${pdfPath}`);
      if (fs.existsSync(pdfPath)) {
        console.log(`[HTML-PDF] 找到PDF文件: ${pdfPath}`);
        return pdfPath;
      }
    }

    // 如果都没找到，使用fallback日期
    const pdfPath = path.join(baseDir, fallbackDate, `${documentNo}.pdf`);
    console.log(`[HTML-PDF] 使用fallback日期路径: ${pdfPath}`);
    return pdfPath;
  }

  /**
   * 上传PDF文件到Firebase Storage
   * @param pdfPath 本地PDF文件路径
   * @param documentNo 订单编号
   * @returns Firebase上传结果
   */
  private async uploadPdfToFirebase(pdfPath: string, documentNo: string): Promise<FirebaseUploadResult> {
    try {
      console.log(`[HTML-PDF] 开始上传PDF到Firebase: ${documentNo}`);

      // 执行上传
      const uploadResult = await this.firebaseService.uploadPdf(pdfPath, documentNo);

      if (uploadResult.success && !uploadResult.skipped) {
        console.log(`[HTML-PDF] Firebase上传成功: ${documentNo}`);
        console.log(`[HTML-PDF] Firebase下载URL: ${uploadResult.downloadUrl}`);
        console.log(`[HTML-PDF] Firebase路径: ${uploadResult.firebasePath}`);
      } else if (uploadResult.skipped) {
        console.log(`[HTML-PDF] Firebase上传已跳过: ${documentNo} -> ${uploadResult.skipReason}`);
      } else {
        console.error(`[HTML-PDF] Firebase上传失败: ${documentNo} -> ${uploadResult.error}`);
      }

      return uploadResult;
    } catch (error) {
      // Firebase上传失败不应该影响PDF生成的主流程
      console.error(`[HTML-PDF] Firebase上传异常: ${documentNo} -> ${error.message}`);
      return {
        success: false,
        error: `上传异常: ${error.message}`,
      };
    }
  }
}
