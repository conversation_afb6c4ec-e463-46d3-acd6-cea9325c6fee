import { Entity, Column, PrimaryColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('AR_CN_Stock_Main')
export class SqlServerCnMain {
  @ApiProperty({ description: '文档编号', example: 'CN123456' })
  @PrimaryColumn({ name: 'Document No' })
  DocumentNo: string;

  @ApiProperty({ description: '文档日期', example: '2025-04-04' })
  @Column({ name: 'Document Date' })
  DocumentDate: Date;

  @ApiProperty({ description: '客户', example: 'CUST001' })
  @Column()
  Customer: string;

  @ApiProperty({ description: '销售员', example: 'S001' })
  @Column()
  Salesman: string;

  @ApiProperty({ description: '备注', example: '紧急订单' })
  @Column()
  Remarks: string;

  @ApiProperty({ description: '付款条件', example: 'CASH' })
  @Column()
  Terms: string;

  @ApiProperty({ description: '总金额', example: 1000 })
  @Column({ name: 'Total Amount' })
  TotalAmount: number;

  @ApiProperty({ description: '外币代码', example: 'USD' })
  @Column({ name: 'Forex Code' })
  ForexCode: string;

  @OneToMany('SqlServerCnDetail', 'main')
  details: any[];
}
