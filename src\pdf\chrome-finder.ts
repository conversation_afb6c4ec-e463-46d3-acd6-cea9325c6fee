import * as fs from 'fs';
import * as path from 'path';

/**
 * 在Windows系统上查找Chrome可执行文件
 */
export function findChrome(): string | undefined {
  // 常见的Chrome安装路径
  const commonPaths = [
    'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
    'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',
    'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
  ];

  // 检查常见路径
  for (const chromePath of commonPaths) {
    if (fs.existsSync(chromePath)) {
      console.log(`找到Chrome/Edge浏览器: ${chromePath}`);
      return chromePath;
    }
  }

  // 如果常见路径没有找到，尝试在Program Files目录下搜索
  try {
    const programFiles = ['C:\\Program Files', 'C:\\Program Files (x86)'];
    
    for (const programDir of programFiles) {
      if (!fs.existsSync(programDir)) continue;
      
      // 搜索Google目录
      const googleDir = path.join(programDir, 'Google');
      if (fs.existsSync(googleDir)) {
        const chromeDir = path.join(googleDir, 'Chrome');
        if (fs.existsSync(chromeDir)) {
          const appDir = path.join(chromeDir, 'Application');
          if (fs.existsSync(appDir)) {
            const chromePath = path.join(appDir, 'chrome.exe');
            if (fs.existsSync(chromePath)) {
              console.log(`找到Chrome浏览器: ${chromePath}`);
              return chromePath;
            }
          }
        }
      }
      
      // 搜索Microsoft Edge
      const microsoftDir = path.join(programDir, 'Microsoft');
      if (fs.existsSync(microsoftDir)) {
        const edgeDir = path.join(microsoftDir, 'Edge');
        if (fs.existsSync(edgeDir)) {
          const appDir = path.join(edgeDir, 'Application');
          if (fs.existsSync(appDir)) {
            const edgePath = path.join(appDir, 'msedge.exe');
            if (fs.existsSync(edgePath)) {
              console.log(`找到Edge浏览器: ${edgePath}`);
              return edgePath;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('搜索Chrome/Edge时出错:', error);
  }

  console.warn('未找到Chrome或Edge浏览器，将使用Puppeteer默认浏览器');
  return undefined;
}
