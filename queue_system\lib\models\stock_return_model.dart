import 'package:flutter/foundation.dart';

class StockReturnModel {
  final int id;
  final String documentNo;
  final int mainId;
  final int line;
  final String stock;
  final String description;
  final double quantity;
  final String uom;
  final double unitPrice;
  final double totalAmount;
  final bool checked;
  final DateTime? checkAt;
  final String? checkBy;
  final int? checkedId;
  final DateTime? checkedAt;
  final DateTime createdAt;
  final String binShelfNo; // 货架位置

  StockReturnModel({
    required this.id,
    required this.documentNo,
    required this.mainId,
    required this.line,
    required this.stock,
    required this.description,
    required this.quantity,
    required this.uom,
    required this.unitPrice,
    required this.totalAmount,
    required this.checked,
    this.checkAt,
    this.checkBy,
    this.checkedId,
    this.checkedAt,
    required this.createdAt,
    required this.binShelfNo,
  });

  factory StockReturnModel.fromJson(Map<String, dynamic> json) {
    try {
      return StockReturnModel(
        id: json['id'],
        documentNo: json['document_no'],
        mainId: json['main_id'] ?? 0,
        line: json['line'],
        stock: json['stock'] ?? '',
        description: json['description'] ?? '',
        quantity: json['quantity'] != null
            ? double.parse(json['quantity'].toString())
            : 0.0,
        uom: json['uom'] ?? '',
        unitPrice: json['unit_price'] != null
            ? double.parse(json['unit_price'].toString())
            : 0.0,
        totalAmount: json['total_amount'] != null
            ? double.parse(json['total_amount'].toString())
            : 0.0,
        checked: json['checked'] ?? false,
        checkAt: json['check_at'] != null ? DateTime.parse(json['check_at']) : null,
        checkBy: json['check_by'],
        checkedId: json['checked_id'],
        checkedAt: json['checked_at'] != null ? DateTime.parse(json['checked_at']) : null,
        createdAt: DateTime.parse(json['created_at']),
        binShelfNo: json['bin_shelf_no'] ?? '未分配货架',
      );
    } catch (e) {
      debugPrint('Error parsing StockReturnModel: $e');
      debugPrint('JSON: $json');
      // 返回一个默认的模型，避免应用崩溃
      return StockReturnModel(
        id: 0,
        documentNo: '',
        mainId: 0,
        line: 0,
        stock: '',
        description: '解析数据失败',
        quantity: 0,
        uom: '',
        unitPrice: 0,
        totalAmount: 0,
        checked: false,
        createdAt: DateTime.now(),
        binShelfNo: '未分配货架',
      );
    }
  }
}
