import { Controller, Get, Post, Query, BadRequestException, NotFoundException, ForbiddenException, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { StockReturnTrackingService } from './stock-return-tracking.service';

@ApiTags('Stock Return Tracking')
@Controller('stock-return-tracking')
export class StockReturnTrackingController {
  constructor(private readonly trackingService: StockReturnTrackingService) {}

  @ApiOperation({ summary: '获取员工的待归还库存列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '返回员工的待归还库存列表'
  })
  @Get('pending')
  async getPendingReturnsByStaff(@Query('staffId') staffId: number) {
    if (!staffId) {
      throw new BadRequestException('Staff ID is required');
    }

    try {
      return await this.trackingService.getPendingReturnsByStaff(parseInt(staffId.toString()));
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get pending returns: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '确认库存归还完成' })
  @ApiQuery({ name: 'trackingId', description: '追踪记录ID' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiQuery({ name: 'notes', description: '备注', required: false })
  @ApiResponse({
    status: 200,
    description: '确认库存归还成功'
  })
  @HttpCode(200)
  @Post('confirm')
  async confirmStockReturn(
    @Query('trackingId') trackingId: number,
    @Query('staffId') staffId: number,
    @Query('notes') notes?: string
  ) {
    if (!trackingId) {
      throw new BadRequestException('Tracking ID is required');
    }

    if (!staffId) {
      throw new BadRequestException('Staff ID is required');
    }

    try {
      return await this.trackingService.confirmStockReturn(
        parseInt(trackingId.toString()),
        parseInt(staffId.toString()),
        notes
      );
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Failed to confirm stock return: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '获取库存归还统计信息' })
  @ApiQuery({ name: 'staffId', description: '员工ID（可选，不提供则返回全局统计）', required: false })
  @ApiResponse({
    status: 200,
    description: '返回库存归还统计信息'
  })
  @Get('statistics')
  async getReturnStatistics(@Query('staffId') staffId?: number) {
    try {
      const parsedStaffId = staffId ? parseInt(staffId.toString()) : undefined;
      return await this.trackingService.getReturnStatistics(parsedStaffId);
    } catch (error) {
      throw new BadRequestException(`Failed to get return statistics: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '获取所有追踪记录（管理员用）' })
  @ApiQuery({ name: 'page', description: '页码', required: false })
  @ApiQuery({ name: 'limit', description: '每页数量', required: false })
  @ApiResponse({
    status: 200,
    description: '返回所有追踪记录'
  })
  @Get('all')
  async getAllTrackingRecords(
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    try {
      const parsedPage = page ? parseInt(page.toString()) : 1;
      const parsedLimit = limit ? parseInt(limit.toString()) : 50;
      return await this.trackingService.getAllTrackingRecords(parsedPage, parsedLimit);
    } catch (error) {
      throw new BadRequestException(`Failed to get tracking records: ${error.message}`);
    }
  }
}
