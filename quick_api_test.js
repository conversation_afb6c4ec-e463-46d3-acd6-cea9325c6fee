// 快速API测试脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

async function quickAPITest() {
  console.log('🚀 开始快速API测试\n');

  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };

  // 测试函数
  async function testAPI(name, testFn) {
    try {
      console.log(`🔍 测试: ${name}`);
      await testFn();
      console.log(`✅ ${name} - 通过\n`);
      results.passed++;
      results.tests.push({ name, status: 'PASS' });
    } catch (error) {
      console.log(`❌ ${name} - 失败: ${error.message}\n`);
      results.failed++;
      results.tests.push({ name, status: 'FAIL', error: error.message });
    }
  }

  // 1. 基本连接测试
  await testAPI('基本连接', async () => {
    const response = await api.get('/');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
  });

  // 2. 获取待归还任务
  await testAPI('获取待归还任务', async () => {
    const response = await api.get('/stock-return-tracking/pending?staffId=1');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 找到 ${response.data.length} 个待归还任务`);
  });

  // 3. 获取统计信息
  await testAPI('获取统计信息', async () => {
    const response = await api.get('/stock-return-tracking/statistics?staffId=1');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    const stats = response.data;
    console.log(`   - 总计: ${stats.total}, 已完成: ${stats.completed}, 待处理: ${stats.pending}`);
  });

  // 4. 获取所有记录
  await testAPI('获取所有记录', async () => {
    const response = await api.get('/stock-return-tracking/all?page=1&limit=5');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 找到 ${response.data.records.length} 条记录`);
  });

  // 5. 测试通知API
  await testAPI('获取通知', async () => {
    const response = await api.get('/notification/unread/1');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 未读通知数量: ${response.data.length}`);
  });

  // 6. 测试重新同步API（使用测试数据）
  await testAPI('重新同步订单', async () => {
    const response = await api.post('/sync/refresh-order', {
      documentNo: 'TEST001',
      supervisorId: 2,
      reason: 'API测试重新同步'
    });
    if (response.status !== 200 && response.status !== 201) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 响应: ${response.data.message}`);
  });

  // 7. 测试确认归还API（如果有待归还任务）
  await testAPI('确认归还功能', async () => {
    // 先获取一个待归还任务
    const pendingResponse = await api.get('/stock-return-tracking/pending?staffId=1');
    if (pendingResponse.data.length > 0) {
      const trackingId = pendingResponse.data[0].id;
      const response = await api.post(`/stock-return-tracking/confirm?trackingId=${trackingId}&staffId=1&notes=API测试确认归还`);
      if (response.status !== 200 && response.status !== 201) {
        throw new Error(`状态码: ${response.status}`);
      }
      console.log(`   - 确认归还成功，追踪ID: ${trackingId}`);
    } else {
      console.log(`   - 没有待归还任务，跳过测试`);
    }
  });

  // 8. 测试订单详情API
  await testAPI('获取订单详情', async () => {
    const response = await api.get('/postgres/detail?documentNo=DO0001');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 找到 ${response.data.length} 个订单明细`);
  });

  // 9. 测试员工列表API
  await testAPI('获取员工列表', async () => {
    const response = await api.get('/postgres/staff');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 找到 ${response.data.length} 个员工`);
  });

  // 10. 测试订单列表API
  await testAPI('获取订单列表', async () => {
    const response = await api.get('/postgres/main');
    if (response.status !== 200) {
      throw new Error(`状态码: ${response.status}`);
    }
    console.log(`   - 找到 ${response.data.length} 个订单`);
  });

  // 输出测试结果
  console.log('📊 测试结果汇总:');
  console.log(`✅ 通过: ${results.passed} 个测试`);
  console.log(`❌ 失败: ${results.failed} 个测试`);
  console.log(`📈 成功率: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%\n`);

  // 详细结果
  console.log('📋 详细结果:');
  results.tests.forEach(test => {
    const status = test.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${test.name}`);
    if (test.error) {
      console.log(`   错误: ${test.error}`);
    }
  });

  // 如果所有核心API都通过，显示Flutter测试建议
  const coreAPIs = ['基本连接', '获取待归还任务', '获取统计信息', '重新同步订单'];
  const coreResults = results.tests.filter(test => coreAPIs.includes(test.name));
  const corePassCount = coreResults.filter(test => test.status === 'PASS').length;

  if (corePassCount === coreAPIs.length) {
    console.log('\n🎉 核心API测试全部通过！');
    console.log('📱 现在可以进行Flutter前端测试：');
    console.log('1. 启动Flutter应用: cd ../mobile_flutter/queue_system && flutter run');
    console.log('2. 登录senior权限用户');
    console.log('3. 在Waiting页面查找重新同步按钮');
    console.log('4. 执行重新同步操作');
    console.log('5. 检查通知系统');
    console.log('\n详细测试指南请参考: FLUTTER_TEST_GUIDE.md');
  } else {
    console.log('\n⚠️ 部分核心API测试失败，请先解决后端问题再进行Flutter测试。');
  }
}

// 执行测试
if (require.main === module) {
  quickAPITest()
    .then(() => {
      console.log('\n✅ API测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ API测试失败:', error);
      process.exit(1);
    });
}

module.exports = { quickAPITest };
