import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider with ChangeNotifier {
  Locale _locale = const Locale('en', 'US'); // 默认使用英文

  Locale get locale => _locale;

  LocaleProvider() {
    _loadSavedLocale();
  }

  Future<void> _loadSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString('languageCode');
      final countryCode = prefs.getString('countryCode');

      if (languageCode != null) {
        _locale = Locale(languageCode, countryCode);
        notifyListeners();
      }
    } catch (e) {
      // 加载失败时使用默认语言
    }
  }

  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;

    _locale = locale;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('languageCode', locale.languageCode);
      await prefs.setString('countryCode', locale.countryCode ?? '');
    } catch (e) {
      // 保存失败不影响当前运行
    }
  }

  void toggleLocale() {
    Locale newLocale;

    switch (_locale.languageCode) {
      case 'en':
        newLocale = const Locale('zh', 'CN');
        break;
      case 'zh':
        newLocale = const Locale('ms', 'MY');
        break;
      case 'ms':
        newLocale = const Locale('en', 'US');
        break;
      default:
        newLocale = const Locale('en', 'US');
    }

    setLocale(newLocale);
  }
}
