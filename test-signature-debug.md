# 签名功能调试指南

## 🔍 **已添加的详细日志**

### 1. **Check Service (completeDocument)**
- 文档查找和验证
- 权限验证详细信息
- Supervisor/Driver查找和权限检查
- PDF生成过程

### 2. **Check Controller**
- API请求参数记录
- 成功/失败状态记录

### 3. **PDF Controller (add-signature-html)**
- 签名请求参数记录
- HTML PDF服务调用过程
- 文件路径处理

### 4. **HTML PDF Service**
- 已有详细的签名添加日志

## 🧪 **测试步骤**

### 步骤1：重启后端服务
```bash
cd d:\Project\QueueSystem\backend_nestjs
npm run start:dev
```

### 步骤2：测试admin/supervisor的完整流程

1. **在移动应用中执行以下操作：**
   - 使用admin或supervisor账户登录
   - 进入"To Ship"页面
   - 选择一个订单
   - 点击"Ship"按钮
   - 选择"不指定司机"
   - 点击"Confirm"按钮

2. **观察后端控制台日志，查找以下关键信息：**

#### A. Check Controller日志
```
[Check Controller] 完成出货请求 - documentNo: xxx, supervisorId: xxx, driverId: xxx
```

#### B. Check Service日志
```
[CompleteDocument] 开始处理 - documentNo: xxx, supervisorId: xxx, driverId: xxx
[CompleteDocument] 查找文档: xxx
[CompleteDocument] 文档找到: xxx, is_shipped: false
[CompleteDocument] 检查文档是否准备就绪: xxx
[CompleteDocument] 文档准备状态: ready=true, unconfirmedItems=0
[CompleteDocument] 查找supervisor: xxx
[CompleteDocument] Supervisor找到: ID=xxx, username=xxx, level=xxx
[CompleteDocument] 检查supervisor权限: level=xxx
[CompleteDocument] Supervisor权限验证通过: level=xxx
[CompleteDocument] 未指定司机，将生成PDF
```

#### C. PDF相关日志
```
[Check Service] 使用HTML模板生成PDF，文档编号: xxx
[Check Service] PDF generated successfully for document xxx
```

#### D. 如果到达签名阶段，PDF Controller日志
```
[PDF Controller] 添加签名请求 - documentNo: xxx, date: xxx, signatureLength: xxx
[PDF Controller] 调用HTML PDF服务添加签名: xxx
[PDF Controller] 签名添加成功: xxx
```

## 🔍 **关键检查点**

### 1. **权限验证失败**
如果看到以下错误：
```
[CompleteDocument] Supervisor权限不足: level=xxx, 需要: supervisor/admin/driver
```
**问题**：数据库中用户的level字段值不正确

### 2. **Supervisor不存在**
如果看到：
```
[CompleteDocument] Supervisor不存在: xxx
```
**问题**：传递的supervisorId在数据库中不存在

### 3. **文档未准备就绪**
如果看到：
```
[CompleteDocument] 文档未准备就绪: xxx, 未确认项目: xxx
```
**问题**：订单中还有未确认的项目

### 4. **网络超时**
如果没有看到任何日志，可能是网络超时问题

## 📋 **收集信息清单**

请在测试时收集以下信息：

1. **用户信息**
   - 登录的用户名
   - 用户ID
   - 用户权限级别

2. **订单信息**
   - 订单编号
   - 订单状态

3. **错误信息**
   - 前端显示的错误消息
   - 后端控制台的完整日志
   - 错误发生的具体步骤

4. **对比信息**
   - 司机用户的相同操作是否正常
   - 其他admin/supervisor用户是否有同样问题

## 🔧 **快速修复建议**

### 如果是权限问题：
1. 检查数据库中staff表的level字段
2. 确认用户权限级别是否正确

### 如果是网络问题：
1. 增加前端超时时间
2. 检查后端服务是否正常运行

### 如果是PDF生成问题：
1. 检查PDF输出目录权限
2. 确认Puppeteer依赖是否正确安装
