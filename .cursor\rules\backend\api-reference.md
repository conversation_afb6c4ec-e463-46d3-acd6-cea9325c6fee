---
title: "API 参考文档"
description: "队列系统后端 API 的完整参考文档"
version: "1.0.0"
last_updated: "2025-05-19"
---

# API 参考文档

## 简介

本文档提供了队列系统后端 API 的完整参考，包括所有端点的详细说明、请求参数、响应格式和使用示例。API 按照使用场景和用户角色进行分类，方便不同角色的用户查找所需的接口。

## 文档更新历史

| 版本  | 日期       | 描述                                                                                                                                                              |
| ----- | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.0.0 | 2024-07-01 | 初始版本                                                                                                                                                          |
| 1.1.0 | 2024-07-15 | 增加员工和主管权限描述                                                                                                                                            |
| 1.2.0 | 2025-04-15 | 优化主管检查逻辑，添加同步过滤规则，添加主管拒绝检查功能与通知系统，移除员工 email，添加 UOM 字段，限制楼层选项，主管 floor 支持 ALL 值，拒绝检查 reason 变为可选 |
| 1.3.0 | 2025-04-15 | 添加文档出货完成功能，允许标记整个订单为已出货                                                                                                                    |
| 1.4.0 | 2025-04-21 | 添加两类新员工类型：司机(driver)和 BOM 管理员(staff_bom)，拥有与主管相同的楼层权限                                                                                 |
| 1.5.0 | 2025-04-21 | 更新了数据结构和关系，支持 BOM 数据同步，delivery_order_detail 表 document_no 现支持格式如"D00001/1"，使用 main_id 关联主表                                       |
| 1.6.0 | 2025-04-22 | 添加独立的BOM专员字段：bom_specialist_checked、bom_specialist_checked_at 和 bom_specialist_id，明确区分普通员工检查和BOM专员检查                                  |

## 基础信息

- 基础 URL: `http://localhost:3000`
- 所有请求和响应均为 JSON 格式
- 标准响应格式:
  - 成功响应:
    ```json
    {
      "success": true,
      "message": "操作成功信息",
      "data": { ... } // 返回的数据
    }
    ```
  - 错误响应:
    ```json
    {
      "success": false,
      "message": "错误描述信息",
      "error": { ... } // 详细错误信息
    }
    ```

### 重要提示：处理包含斜杠的文档编号

由于文档编号现在支持格式如 `D00001/1`，在使用 URL 路径参数时会出现冲突问题。**请使用以下推荐方法**:

- **方法 1: 使用查询参数**

  ```
  POST /check/staff?documentNo=D00001/1&line=3
  ```

  这是处理含有斜杠文档编号的推荐方法

- **方法 2: 使用请求体参数**

  ```
  POST /check/item
  Content-Type: application/json

  {
    "documentNo": "D00001/1",
    "line": 3,
    "staffId": 1
  }
  ```

- **不推荐: URL 编码** (可能在某些情况下不工作)
  ```
  POST /check/staff/D00001%2F1/3
  ```

> **注意**: 文档中所有使用 `:documentNo` 作为路径参数的 API 端点都应使用上述推荐方法访问，特别是当文档编号包含斜杠时。

## 目录

1. [员工权限说明](#员工权限说明)
2. [前端登录/认证相关 API](#前端登录认证相关-api)
3. [员工(Staff)操作相关 API](#员工staff操作相关-api)
4. [员工BOM(Staff_Bom)操作相关 API](#员工bomstaff_bom操作相关-api)
5. [主管(Supervisor)操作相关 API](#主管supervisor操作相关-api)
6. [司机(Driver)操作相关 API](#司机driver操作相关-api)
7. [PDF 生成与邮件相关 API](#pdf-生成与邮件相关-api)
8. [系统管理相关 API](#系统管理相关-api)
9. [常见使用场景](#常见使用场景)
10. [数据结构说明](#数据结构说明)

## 员工权限说明

### 员工级别 (level)

- `supervisor`: 主管级别
  - 可以检查所有楼层的货物
  - 可以验证其他员工已检查的货物
  - 可以查看所有楼层的待检查货物
  - 可以看到所有未被主管检查过的货物，无论是否已被员工检查过
  - 可以在出货时选择是否指派司机负责送货
- `admin`: 管理员级别
  - 拥有所有主管权限
  - 可以进行员工管理
  - 可以设置紧急队列
  - 可以重新读取单据
- `driver`: 司机级别
  - 拥有与主管相同的楼层权限
  - 可以查看所有楼层的货物
  - 可以访问所有楼层
  - 可以像主管一样检查并确认货物
  - 可以像主管一样标记订单为已出货
  - 负责出货订单的送达确认
  - 可以标记由自己送达的订单为"已送达"，完成整个订单流程
- `staff_bom`: BOM 管理员级别
  - 拥有与主管相同的楼层权限
  - 可以查看所有楼层的货物
  - 可以访问所有楼层
  - 负责检查BOM物品（具有parent_code的物品）
  - 普通员工检查过的BOM物品需要经过BOM管理员二次检查
  - 只有BOM管理员确认后的BOM物品才能提交给主管检查
- `regular`: 普通员工级别
  - 只能检查自己负责楼层的货物
  - 不能验证其他员工已检查的货物
  - 只能查看自己负责楼层的待检查货物
  - 可以进行BOM物品的初步检查，但需要BOM管理员进行二次检查

### 楼层权限 (floor)

- 每个员工只能操作自己负责楼层的货物
- 楼层格式限定为以下选项：`1F`, `2F`, `3F`
- 主管、司机和 BOM 管理员可以使用`ALL`值表示有权操作所有楼层的货物
- 主管、司机和 BOM 管理员可以操作所有楼层的货物

### 货架位置编码 (bin_shelf_no)

- 格式为：`楼层-区域-货架-位置`，例如 `2-1-G001-1`
- 第一部分表示楼层编号（如 `2` 表示二楼）
- 系统通过第一个数字识别货物所在楼层
- 普通员工只能操作与自己负责楼层一致的货物
- 如果员工的 `floor` 是 "2F"，则只能操作 bin_shelf_no 以 "2-" 开头的货物

## 前端登录/认证相关 API

### 员工登录
- **路径**: `/staff/login`
- **方法**: `POST`
- **控制器**: StaffController.login
- **请求体**:
  - `username`: 用户名
  - `password`: 密码
- **说明**: 用于所有角色的用户登录系统
- **请求示例**:
  ```json
  {
    "username": "john.doe",
    "password": "secure_password"
  }
  ```
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "data": {
      "id": 1,
      "username": "john.doe",
      "level": "senior",
      "floor": "3F",
      "full_name": "John Doe",
      "status": "active",
      "last_login": "2025-05-19T11:00:00.000Z",
      "created_at": "2025-01-01T00:00:00.000Z"
    }
  }
  ```

## 员工(Staff)操作相关 API

### 获取员工待检查列表
- **路径**: `/check/list`
- **方法**: `GET`
- **控制器**: CheckController.getCheckList
- **查询参数**:
  - `staffId`: 员工ID
- **说明**: 普通员工获取待检查的货物列表
- **权限要求**:
  - 普通员工只能看到自己负责楼层的待检查货物
  - BOM管理员可以看到所有楼层中已被普通员工检查过但需要BOM专员检查的BOM物品
  - 主管和司机可以看到所有楼层的待检查货物
  - 主管和司机可以看到所有未被主管检查过的货物，无论是否已被员工检查过
- **响应示例**:
  ```json
  {
    "pendingStaffCheck": [
      {
        "id": 7,
        "document_no": "D00001/1",
        "line": 1,
        "stock": "stock123",
        "description": "测试货品1",
        "quantity": "10.00",
        "bin_shelf_no": "2-1-G001-1",
        "parent_code": null,
        "staff_checked": false,
        "staff_checked_at": null,
        "staff_id": null,
        "supervisor_checked": false,
        "supervisor_checked_at": null,
        "supervisor_id": null,
        "created_at": "2025-04-08T21:57:46.273Z",
        "main_id": 1,
        "document_date": "2025-04-06T16:00:00.000Z",
        "customer": "A0001",
        "customer_name": "Atas Com"
      }
    ],
    "pendingSupervisorCheck": []
  }
  ```

### 员工检查货物
- **路径**: `/check/staff`
- **方法**: `POST`
- **控制器**: CheckController.staffCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `staffId`: 员工ID
- **说明**: 普通员工标记货物为已检查
- **权限要求**:
  - 只有普通员工和BOM管理员可以执行此操作，主管不能执行员工检查
  - 普通员工只能检查自己负责楼层的货物
  - 对于非BOM物品（parent_code为NULL），普通员工检查后即可提交给主管验证
  - 对于BOM物品（有parent_code的物品），需要经过以下流程：
    1. 普通员工进行初步检查
    2. BOM管理员进行二次检查
    3. 提交给主管进行最终验证
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Item checked by staff john.doe",
    "detail": {
      "id": 7,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "2-1-G001-1",
      "parent_code": null,
      "staff_checked": true,
      "staff_checked_at": "2025-05-19T11:05:10.123Z",
      "staff_id": 1,
      "supervisor_checked": false,
      "supervisor_checked_at": null,
      "supervisor_id": null,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1,
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com"
    }
  }
  ```

### 获取楼层检查统计
- **路径**: `/check/floor-stats/:floor`
- **方法**: `GET`
- **控制器**: CheckController.getFloorStats
- **查询参数**:
  - `floor`: 楼层编号 (URL参数)
- **说明**: 获取特定楼层的检查统计信息，用于员工查看工作进度
- **响应示例**:
  ```json
  {
    "pendingStaffCheck": 5,
    "pendingSupervisorCheck": 3,
    "totalPending": 8
  }
  ```

### 获取用户未读通知
- **路径**: `/notification/unread/:userId`
- **方法**: `GET`
- **控制器**: NotificationController.getUnreadNotifications
- **查询参数**:
  - `userId`: 用户ID (URL参数)
- **说明**: 员工查看未读通知，如检查被拒绝等
- **响应示例**:
  ```json
  [
    {
      "id": 1,
      "type": "reject",
      "message": "您的检查被拒绝：数量不符，请重新检查",
      "recipient_id": 1,
      "sender_id": 2,
      "document_no": "D00001/1",
      "line": 1,
      "detail_id": 7,
      "read": false,
      "read_at": null,
      "reject_reason": "数量不符，请重新检查",
      "created_at": "2025-05-19T10:30:00.000Z"
    }
  ]
  ```

### 获取用户所有通知
- **路径**: `/notification/all/:userId`
- **方法**: `GET`
- **控制器**: NotificationController.getAllNotifications
- **查询参数**:
  - `userId`: 用户ID (URL参数)
- **说明**: 员工查看所有通知历史

### 标记通知为已读
- **路径**: `/notification/read/:id`
- **方法**: `POST`
- **控制器**: NotificationController.markAsRead
- **查询参数**:
  - `id`: 通知ID (URL参数)
- **说明**: 员工标记单条通知为已读

### 标记所有通知为已读
- **路径**: `/notification/read-all`
- **方法**: `POST`
- **控制器**: NotificationController.markAllAsRead
- **查询参数**:
  - `userId`: 用户ID
- **说明**: 员工标记所有通知为已读

## 员工BOM(Staff_Bom)操作相关 API

### BOM专员检查货物
- **路径**: `/check/bom`
- **方法**: `POST`
- **控制器**: CheckController.bomSpecialistCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `bomSpecialistId`: BOM专员ID
- **说明**: BOM专员对BOM物品进行二次检查，只能检查已被普通员工初步检查过的BOM物品
- **权限要求**:
  - 仅限BOM管理员(staff_bom)权限用户使用
  - 只能检查已被普通员工初步检查过的BOM物品(parent_code不为NULL)
  - BOM管理员可以检查所有楼层的BOM物品
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Item checked by BOM specialist bob.bom",
    "detail": {
      "id": 1,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "3-1-G001-1",
      "parent_code": "BOM001",
      "staff_checked": true,
      "staff_checked_at": "2025-04-08T21:05:10.123Z",
      "staff_id": 1,
      "bom_specialist_checked": true,
      "bom_specialist_checked_at": "2025-04-08T22:05:10.123Z",
      "bom_specialist_id": 3,
      "supervisor_checked": false,
      "supervisor_checked_at": null,
      "supervisor_id": null,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1
    }
  }
  ```

### 获取员工待检查列表
- **路径**: `/check/list`
- **方法**: `GET`
- **控制器**: CheckController.getCheckList
- **查询参数**:
  - `staffId`: 员工ID
- **说明**: BOM专员获取待检查的BOM物品列表，系统会根据用户级别返回不同的列表内容

## 主管(Supervisor)操作相关 API

### 主管检查货物
- **路径**: `/check/supervisor`
- **方法**: `POST`
- **控制器**: CheckController.supervisorCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `supervisorId`: 主管ID
- **说明**: 主管确认货物检查，对于BOM物品需要先经过BOM专员检查
- **权限要求**:
  - 仅限主管(senior)或司机(driver)级别用户使用
  - 可以确认所有楼层的货物
  - 对于非BOM物品，需要先经过普通员工检查
  - 对于BOM物品，需要先经过普通员工检查和BOM专员二次检查
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Item confirmed by supervisor jane.senior",
    "detail": {
      "id": 7,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "2-1-G001-1",
      "parent_code": null,
      "staff_checked": true,
      "staff_checked_at": "2025-05-19T11:05:10.123Z",
      "staff_id": 1,
      "supervisor_checked": true,
      "supervisor_checked_at": "2025-05-19T11:15:10.123Z",
      "supervisor_id": 2,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1
    }
  }
  ```

### 拒绝员工检查
- **路径**: `/check/reject`
- **方法**: `POST`
- **控制器**: CheckController.rejectCheck
- **查询参数**:
  - `documentNo`: 单据编号
  - `line`: 明细行号
  - `supervisorId`: 主管ID
  - `reason`: 拒绝原因 (可选)
- **说明**: 主管拒绝员工或BOM专员的检查结果
- **权限要求**:
  - 仅限主管(senior)或司机(driver)级别用户使用
  - 可以拒绝所有楼层的货物检查
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Check rejected by supervisor jane.senior",
    "detail": {
      "id": 7,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "2-1-G001-1",
      "parent_code": null,
      "staff_checked": false,
      "staff_checked_at": null,
      "staff_id": null,
      "supervisor_checked": false,
      "supervisor_checked_at": null,
      "supervisor_id": null,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1
    },
    "notification": {
      "id": 2,
      "type": "reject",
      "message": "您的检查被拒绝：数量不符，请重新检查",
      "recipient_id": 1,
      "sender_id": 2,
      "document_no": "D00001/1",
      "line": 1,
      "detail_id": 7,
      "read": false,
      "read_at": null,
      "reject_reason": "数量不符，请重新检查",
      "created_at": "2025-05-19T11:20:00.000Z"
    }
  }
  ```

### 检查这张订单是否可以出货
- **路径**: `/check/document-ready`
- **方法**: `GET`
- **控制器**: CheckController.isDocumentReady
- **查询参数**:
  - `documentNo`: 单据编号
  - `staffId`: 员工ID
- **说明**: 主管检查订单是否所有项目都已确认可以出货
- **权限要求**:
  - 仅限主管(senior)或司机(driver)级别用户使用
- **响应示例**:
  ```json
  {
    "ready": true,
    "totalItems": 5,
    "checkedItems": 5,
    "message": "所有项目已检查完成，可以出货"
  }
  ```

### 获取所有可出货订单
- **路径**: `/check/ready-documents`
- **方法**: `GET`
- **控制器**: CheckController.getReadyDocuments
- **查询参数**:
  - `staffId`: 员工ID（必须是主管或司机）
- **权限要求**:
  - 仅限主管(senior)或司机(driver)级别用户访问
- **返回数据**:
  ```json
  [
    {
      "id": 1,
      "document_no": "DO-2024-001",
      "document_date": "2025-04-15T00:00:00.000Z",
      "customer": "CUST001",
      "customer_name": "测试客户",
      "remarks": "紧急订单",
      "is_shipped": false,
      "created_at": "2024-04-15T08:30:00.000Z",
      "updated_at": "2024-04-15T09:15:00.000Z"
    }
  ]
  ```
- **说明**:
  - 返回所有未出货但至少有一个项目被主管确认过的订单
  - 用于主管出货页面和司机接单页面
  - 如果用户不是主管或司机，将返回403错误
  - 如果用户不存在，将返回404错误

### 标记订单为已出货
- **路径**: `/check/complete`
- **方法**: `POST`
- **控制器**: CheckController.completeDocument
- **查询参数**:
  - `documentNo`: 单据编号
  - `supervisorId`: 主管ID
  - `driverId`: 司机ID (可选)
- **说明**: 主管标记订单为已出货，可选择指派司机送货
- **权限要求**:
  - 仅限主管(senior)级别用户使用
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Document marked as shipped and assigned to driver dave.driver",
    "document": {
      "id": 1,
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "remarks": "ABC",
      "is_shipped": true,
      "shipped_at": "2025-05-19T11:30:00.000Z",
      "shipped_by": 2,
      "delivered": false,
      "delivered_at": null,
      "delivered_by": null,
      "delivery_notes": null,
      "created_at": "2025-04-08T21:57:46.248Z"
    }
  }
  ```

### 获取主管指派的订单列表
- **路径**: `/shipping/supervisor-shipments`
- **方法**: `GET`
- **控制器**: ShippingController.getSupervisorShipments
- **查询参数**:
  - `supervisorId`: 主管ID
- **说明**: 主管查看自己指派的所有订单及其送达状态

### 获取所有司机列表
- **路径**: `/shipping/drivers`
- **方法**: `GET`
- **控制器**: ShippingController.getAllDrivers
- **查询参数**: 无
- **说明**: 主管获取所有司机列表，用于指派送货任务

## 司机(Driver)操作相关 API

### 获取司机待送达订单列表
- **路径**: `/shipping/delivery-list`
- **方法**: `GET`
- **控制器**: ShippingController.getDeliveryList
- **查询参数**:
  - `driverId`: 司机ID
- **说明**: 司机获取分配给自己的待送达订单列表
- **权限要求**:
  - 仅限司机(driver)级别用户使用
- **响应示例**:
  ```json
  [
    {
      "id": 1,
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "remarks": "ABC",
      "is_shipped": true,
      "shipped_at": "2025-05-19T11:30:00.000Z",
      "shipped_by": 2,
      "delivered": false,
      "delivered_at": null,
      "delivered_by": null,
      "delivery_notes": null,
      "created_at": "2025-04-08T21:57:46.248Z"
    }
  ]
  ```

### 确认订单送达
- **路径**: `/shipping/confirm-delivery`
- **方法**: `POST`
- **控制器**: ShippingController.confirmDelivery
- **查询参数**:
  - `documentNo`: 单据编号
  - `driverId`: 司机ID
  - `notes`: 送达备注 (可选)
- **说明**: 司机确认订单已送达客户
- **权限要求**:
  - 仅限司机(driver)级别用户使用
  - 只能确认分配给自己的订单
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Delivery confirmed by driver dave.driver",
    "document": {
      "id": 1,
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "remarks": "ABC",
      "is_shipped": true,
      "shipped_at": "2025-05-19T11:30:00.000Z",
      "shipped_by": 2,
      "delivered": true,
      "delivered_at": "2025-05-19T14:30:00.000Z",
      "delivered_by": 3,
      "delivery_notes": "客户已签收",
      "created_at": "2025-04-08T21:57:46.248Z"
    }
  }
  ```

### 司机拒绝分配的订单
- **路径**: `/check/reject-delivery`
- **方法**: `POST`
- **控制器**: CheckController.rejectDelivery
- **查询参数**:
  - `documentNo`: 单据编号
  - `driverId`: 司机ID
  - `reason`: 拒绝原因 (可选)
- **说明**: 司机拒绝接受分配的订单
- **权限要求**:
  - 仅限司机(driver)级别用户使用
  - 只能拒绝分配给自己的订单
- **响应示例**:
  ```json
  {
    "success": true,
    "message": "Delivery rejected by driver dave.driver",
    "document": {
      "id": 1,
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "remarks": "ABC",
      "is_shipped": true,
      "shipped_at": "2025-05-19T11:30:00.000Z",
      "shipped_by": 2,
      "delivered": false,
      "delivered_at": null,
      "delivered_by": null,
      "delivery_notes": null,
      "created_at": "2025-04-08T21:57:46.248Z"
    }
  }
  ```

### 获取所有可出货订单
- **路径**: `/check/ready-documents`
- **方法**: `GET`
- **控制器**: CheckController.getReadyDocuments
- **查询参数**:
  - `staffId`: 员工ID（必须是主管或司机）
- **说明**: 司机也可以查看可出货订单列表（与主管共用API）

## PDF 生成与邮件相关 API

### 生成订单 PDF
- **路径**: `/pdf/generate`
- **方法**: `POST`
- **控制器**: PdfController.generatePdf
- **查询参数**:
  - `documentNo`: 订单编号
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "PDF generated successfully",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 使用传统方式生成订单 PDF 文件
  - 生成的 PDF 文件保存在服务器的 PDF_Output/{当前日期} 目录下
  - 如果订单不存在，将返回 404 错误

### 使用 HTML 模板生成订单 PDF
- **路径**: `/pdf/generate-html`
- **方法**: `POST`
- **控制器**: PdfController.generateHtmlPdf
- **查询参数**:
  - `documentNo`: 订单编号
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "PDF generated successfully using HTML template",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 使用 HTML 模板和 Puppeteer 生成订单 PDF 文件
  - 生成的 PDF 文件保存在服务器的 PDF_Output/{当前日期} 目录下
  - 如果订单不存在，将返回 404 错误

### 查看订单 PDF
- **路径**: `/pdf/view`
- **方法**: `GET`
- **控制器**: PdfController.viewPdf
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **返回数据**: PDF 文件流
- **说明**:
  - 返回指定订单的 PDF 文件
  - 如果文件不存在，会尝试先生成 PDF 文件
  - 如果生成失败，将返回 404 错误

### 添加签名到 PDF
- **路径**: `/pdf/add-signature`
- **方法**: `POST`
- **控制器**: PdfController.addSignature
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **请求体**:
  - `signatureBase64`: 签名的 Base64 编码数据
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "Signature added successfully",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 将签名添加到指定订单的 PDF 文件中
  - 如果 PDF 文件不存在，将返回 404 错误

### 使用 HTML 模板添加签名到 PDF
- **路径**: `/pdf/add-signature-html`
- **方法**: `POST`
- **控制器**: PdfController.addSignatureHtml
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **请求体**:
  - `signatureBase64`: 签名的 Base64 编码数据
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "Signature added successfully using HTML template",
    "filePath": "PDF_Output/2023-05-20/D00001.pdf"
  }
  ```
- **说明**:
  - 使用 HTML 模板将签名添加到指定订单的 PDF 文件中
  - 如果 PDF 文件不存在，将返回 404 错误

### 发送 PDF 邮件
- **路径**: `/email/send-pdf`
- **方法**: `POST`
- **控制器**: EmailController.sendPdf
- **查询参数**:
  - `documentNo`: 订单编号
  - `date`: 日期 (YYYY-MM-DD 格式，可选，默认为当前日期)
- **请求体**:
  - `to`: 收件人邮箱
  - `subject`: 邮件主题 (可选，默认为 "Delivery Order: {documentNo}")
  - `text`: 邮件正文 (可选，默认为 "Please find the attached delivery order PDF for document {documentNo}.")
- **返回数据**:
  ```json
  {
    "success": true,
    "message": "Email <NAME_EMAIL>"
  }
  ```
- **说明**:
  - 将指定订单的 PDF 文件作为附件发送到指定邮箱
  - 如果 PDF 文件不存在，将返回错误
  - 邮件发送失败时，将返回错误信息

## 系统管理相关 API

### 获取所有员工
- **路径**: `/staff`
- **方法**: `GET`
- **控制器**: StaffController.findAll
- **查询参数**: 无
- **说明**: 系统管理员获取所有员工列表

### 根据 ID 获取员工
- **路径**: `/staff/:id`
- **方法**: `GET`
- **控制器**: StaffController.findOne
- **查询参数**:
  - `id`: 员工ID (URL参数)
- **说明**: 系统管理员获取特定员工信息

### 创建新员工
- **路径**: `/staff`
- **方法**: `POST`
- **控制器**: StaffController.create
- **请求体**:
  - `username`: 用户名
  - `password`: 密码
  - `level`: 权限级别
  - `floor`: 负责楼层
  - `full_name`: 全名
- **说明**: 系统管理员创建新员工账号

### 手动触发数据同步
- **路径**: `/sync`
- **方法**: `POST`
- **控制器**: SyncController.syncData
- **查询参数**: 无
- **说明**: 系统管理员手动触发数据同步，从外部系统导入订单数据

## 常见使用场景

### 员工登录和待检查列表获取

1. 员工登录
   ```http
   POST /staff/login
   Content-Type: application/json

   {
     "username": "john.doe",
     "password": "secure_password"
   }
   ```

2. 登录成功后，使用返回的员工 ID 获取待检查列表
   ```http
   GET /check/list?staffId=1
   ```

3. 应用显示待检查列表，区分"待员工检查"和"待主管确认"两部分

4. 员工执行检查
   ```http
   POST /check/staff?documentNo=D00001/1&line=3&staffId=1
   ```

5. 主管执行确认
   ```http
   POST /check/supervisor?documentNo=D00001/1&line=3&supervisorId=2
   ```

### 订单出货流程

1. 检查订单是否可以出货（所有项目都已被主管或司机确认）
   ```http
   GET /check/document-ready?documentNo=D00001&staffId=2
   ```

2. 获取所有可以出货的订单列表
   ```http
   GET /check/ready-documents?staffId=2
   ```

3. 标记订单为已出货
   ```http
   POST /check/complete?documentNo=D00001&supervisorId=2&driverId=3
   ```

### 拒绝检查和通知流程

1. 主管或司机拒绝员工的检查
   ```http
   POST /check/reject?documentNo=D00001/1&line=3&supervisorId=2&reason=数量不符，请重新检查
   ```

2. 员工登录后检查未读通知列表
   ```http
   GET /notification/unread?userId=1
   ```

3. 员工阅读通知详情，了解被拒绝的原因

4. 员工标记通知为已读
   ```http
   POST /notification/read?id=123
   ```

5. 员工重新检查该物品并再次提交
   ```http
   POST /check/staff?documentNo=D00001/1&line=3&staffId=1
   ```

## 数据结构说明

系统已更新数据结构以支持 BOM 数据同步：

1. **明细表主键变更**
   - delivery_order_detail 表不再使用 document_no 和 line 的组合作为唯一约束
   - 现在仅使用 id 主键字段，允许存在重复的 document_no 和 line 组合

2. **Document No 格式变更**
   - 支持 document_no 格式如"D00001/1"、"D00001/2"等，表示 BOM 分组
   - 部分保留原始格式以便于追踪和调试

3. **表关系更新**
   - 添加 main_id 字段关联到 delivery_order_main 表的 id
   - 不再使用 document_no 作为外键关联
   - 查询使用 main_id 建立两表关系

4. **BOM 支持**
   - 添加 parent_code 字段，用于存储 BOM 的父级编码
   - 父级为 NULL 表示非 BOM 项目
   - 支持与 SC_Tran_Detail 数据的同步

5. **BOM 专员字段**
   - 添加专用字段以明确区分普通员工和BOM专员的检查步骤
   - 新增字段：
     - `bom_specialist_checked`: 布尔值，表示是否已经被BOM专员检查
     - `bom_specialist_checked_at`: 日期时间，表示BOM专员检查时间
     - `bom_specialist_id`: 整数，表示执行检查的BOM专员ID
