import 'dart:async';
import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';

/// 统一的可搜索列表Widget基类
/// 提供防抖搜索、分页、状态管理等统一功能
abstract class SearchableListWidget<T> extends StatefulWidget {
  final int staffId;
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  )? onConfirmAction;
  final Function? onDataChanged;

  const SearchableListWidget({
    super.key,
    required this.staffId,
    this.onConfirmAction,
    this.onDataChanged,
  });
}

abstract class SearchableListWidgetState<T, W extends SearchableListWidget<T>>
    extends State<W> {
  // 数据状态
  List<T> _items = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMore = true;
  static const int _pageSize = 10;

  // 搜索状态
  String _searchQuery = '';
  Timer? _searchDebounceTimer;

  // 抽象方法 - 子类必须实现
  Future<Map<String, dynamic>> loadData({
    required int page,
    required int limit,
    String? searchQuery,
  });

  Widget buildItemCard(T item);
  String getEmptyMessage();
  String getSearchHintText();

  // 可选重写的方法
  String getNoSearchResultsMessage() => context.t('no_search_results');
  Duration getDebounceDelay() => const Duration(seconds: 1);

  @override
  void initState() {
    super.initState();
    _loadItems(isRefresh: true);
  }

  @override
  void didUpdateWidget(W oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当搜索查询改变时，执行防抖搜索
    if (_searchQuery != oldWidget.toString()) {
      _handleSearchQueryChange();
    }
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 处理搜索查询变化
  void _handleSearchQueryChange() {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 设置新的防抖定时器
    _searchDebounceTimer = Timer(getDebounceDelay(), () {
      // 延迟后执行搜索
      _performSearch();
    });
  }

  // 执行实际的搜索操作
  void _performSearch() {
    setState(() {
      // 重置分页状态并重新加载数据
      _currentPage = 1;
      _hasMore = true;
      _items.clear();
    });

    // 执行服务器端搜索
    _loadItems(isRefresh: true);
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 立即更新搜索查询状态（用于UI显示）
    setState(() {
      _searchQuery = query;
    });

    // 触发防抖搜索
    _handleSearchQueryChange();
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    setState(() {
      _searchQuery = '';
      // 重置分页状态并重新加载所有数据
      _currentPage = 1;
      _hasMore = true;
      _items.clear();
    });

    // 立即重新加载所有数据
    _loadItems(isRefresh: true);
  }

  // 加载数据
  Future<void> _loadItems({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoading = true;
        _error = null;
        _currentPage = 1;
        _hasMore = true;
        _items.clear();
      });
    }

    try {
      final response = await loadData(
        page: _currentPage,
        limit: _pageSize,
        searchQuery: _searchQuery.trim().isNotEmpty ? _searchQuery.trim() : null,
      );

      final List<T> items = response['data'] as List<T>;
      final bool hasMore = response['hasMore'] as bool;

      setState(() {
        if (isRefresh) {
          _items = items;
        } else {
          _items.addAll(items);
        }
        _hasMore = hasMore;
        _isLoading = false;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
      rethrow;
    }
  }

  // 加载更多数据
  Future<void> _loadMoreItems() async {
    if (_isLoadingMore || !_hasMore) {
      return;
    }

    setState(() {
      _isLoadingMore = true;
    });

    // 先保存当前页码，以便出错时回退
    final originalPage = _currentPage;
    _currentPage++;

    try {
      await _loadItems();
    } catch (e) {
      // 如果加载失败，回退页码并重置状态
      setState(() {
        _currentPage = originalPage;
        _isLoadingMore = false;
        _error = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 搜索栏
        SearchBarWidget(
          hintText: getSearchHintText(),
          onSearchChanged: _onSearchChanged,
          onClear: _onSearchCleared,
          initialValue: _searchQuery,
        ),
        // 内容区域
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(context.t('loading_failed')),
            const SizedBox(height: 8),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadItems(isRefresh: true),
              child: Text(context.t('retry')),
            ),
          ],
        ),
      );
    }

    if (_items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isNotEmpty ? Icons.search_off : Icons.inbox_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? getNoSearchResultsMessage()
                  : getEmptyMessage(),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${context.t('search_keyword')}: "$_searchQuery"',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadItems(isRefresh: true),
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 显示搜索结果统计
        if (_searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '${context.t('search_results')}: ${_items.length} 条结果',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        // 列表内容
        Expanded(child: _buildItemsList()),
      ],
    );
  }

  Widget _buildItemsList() {
    return RefreshIndicator(
      onRefresh: () => _loadItems(isRefresh: true),
      child: ListView.builder(
        itemCount: _items.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          // 如果是最后一项且有更多数据，显示Load More按钮
          if (index == _items.length && _hasMore) {
            return Container(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: _isLoadingMore
                    ? const CircularProgressIndicator()
                    : ElevatedButton(
                        onPressed: _loadMoreItems,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                        ),
                        child: Text(context.t('load_more')),
                      ),
              ),
            );
          }

          final item = _items[index];
          return buildItemCard(item);
        },
      ),
    );
  }
}
