---
title: "系统安全指南"
description: "队列系统的安全实践和指南"
version: "1.0.0"
last_updated: "2025-05-19"
---

# 系统安全指南

## 概述

本文档提供了队列系统的安全实践和指南，包括认证、授权、数据保护和安全编码实践。这些指南适用于后端 NestJS 服务和前端 Flutter 应用。

## 认证机制

### JWT 认证

系统使用 JSON Web Token (JWT) 进行用户认证：

1. **令牌生成**：
   - 用户登录成功后，后端生成包含用户 ID、角色和过期时间的 JWT
   - 令牌使用 HS256 算法签名，密钥存储在环境变量中
   - 令牌有效期为 8 小时，过期后需要重新登录

2. **令牌验证**：
   - 前端将令牌存储在安全存储中，并在每个请求的 Authorization 头中发送
   - 后端使用 JWT 中间件验证令牌的有效性和完整性
   - 验证失败时返回 401 Unauthorized 响应

3. **令牌刷新**：
   - 系统不使用刷新令牌，用户需要在令牌过期后重新登录
   - 前端在检测到 401 响应时自动跳转到登录页面

### 密码安全

1. **密码存储**：
   - 使用 bcrypt 算法对密码进行单向哈希
   - 使用随机盐值增强安全性
   - 哈希强度因子设置为 10

2. **密码策略**：
   - 密码长度至少 8 个字符
   - 必须包含大小写字母、数字和特殊字符
   - 禁止使用常见密码和个人信息

3. **登录保护**：
   - 连续 5 次登录失败后，账户锁定 15 分钟
   - 登录尝试记录在数据库中，包括时间戳和 IP 地址

## 授权机制

### 基于角色的访问控制 (RBAC)

系统实现了基于角色的访问控制：

1. **角色定义**：
   - `regular`: 普通员工
   - `staff_bom`: BOM 专员
   - `supervisor`: 主管
   - `admin`: 管理员
   - `driver`: 司机

2. **权限检查**：
   - 使用 NestJS Guards 在控制器级别检查用户角色
   - 使用自定义装饰器 `@Roles()` 指定允许访问的角色
   - 在服务层进行额外的权限检查，如楼层访问权限

3. **楼层权限**：
   - 普通员工只能访问其负责楼层的数据
   - 主管、管理员、司机和 BOM 专员可以访问所有楼层的数据
   - 楼层权限在数据库查询中强制执行

### API 端点保护

1. **路由保护**：
   - 除登录端点外，所有 API 端点都受 JWT 认证保护
   - 敏感操作（如出货确认）受角色保护

2. **数据过滤**：
   - 查询结果根据用户角色和权限进行过滤
   - 普通员工只能看到自己负责楼层的数据
   - 主管和管理员可以看到所有数据

## 数据保护

### 数据传输安全

1. **HTTPS**：
   - 所有 API 通信使用 HTTPS 加密
   - 使用 TLS 1.2 或更高版本
   - 证书由受信任的 CA 签发

2. **数据验证**：
   - 使用 class-validator 验证所有输入数据
   - 实施严格的类型检查和边界验证
   - 防止 SQL 注入和 XSS 攻击

### 数据存储安全

1. **敏感数据加密**：
   - 客户联系信息使用 AES-256 加密存储
   - 加密密钥使用密钥管理系统保护

2. **数据备份**：
   - 数据库每日自动备份
   - 备份文件加密存储
   - 保留 30 天的备份历史

3. **数据访问日志**：
   - 记录所有敏感数据的访问操作
   - 日志包含用户 ID、时间戳、操作类型和访问的数据

## 移动应用安全

### 客户端数据保护

1. **安全存储**：
   - JWT 令牌存储在 Flutter Secure Storage 中
   - 敏感数据不缓存在本地存储中
   - 应用退出时清除内存中的敏感数据

2. **屏幕保护**：
   - 应用在后台运行 5 分钟后自动锁定
   - 返回应用时需要重新验证身份
   - 防止截屏功能用于敏感页面

3. **网络安全**：
   - 实施证书固定 (Certificate Pinning)
   - 验证服务器证书的有效性
   - 防止中间人攻击

### 应用完整性

1. **代码混淆**：
   - 使用 Flutter 混淆功能保护应用代码
   - 混淆类名、方法名和变量名
   - 增加逆向工程难度

2. **篡改检测**：
   - 检测应用是否在模拟器中运行
   - 检测应用是否被修改
   - 检测设备是否已越狱/获取 root 权限

## 安全编码实践

### 后端安全实践

1. **输入验证**：
   - 使用 DTO 类和 class-validator 验证所有输入
   - 实施白名单验证策略
   - 拒绝不符合预期格式的输入

2. **SQL 注入防护**：
   - 使用 TypeORM 的参数化查询
   - 避免使用原始 SQL 查询
   - 实施最小权限原则访问数据库

3. **错误处理**：
   - 使用全局异常过滤器捕获所有异常
   - 返回通用错误消息，不泄露敏感信息
   - 详细错误日志仅记录在服务器端

### 前端安全实践

1. **输入验证**：
   - 在客户端实施输入验证
   - 使用正则表达式验证输入格式
   - 限制输入长度和字符集

2. **状态管理安全**：
   - 敏感数据不存储在全局状态中
   - 使用加密存储敏感信息
   - 应用退出时清除状态

3. **依赖项安全**：
   - 定期更新依赖项
   - 使用 Flutter 依赖项安全扫描
   - 避免使用有已知漏洞的包

## 安全监控与响应

### 监控机制

1. **日志监控**：
   - 集中收集和分析应用日志
   - 设置异常行为告警
   - 监控认证失败和权限违规

2. **性能监控**：
   - 监控 API 响应时间
   - 检测异常流量模式
   - 识别潜在的 DoS 攻击

### 安全事件响应

1. **响应流程**：
   - 定义安全事件分类和严重程度
   - 建立响应团队和责任分工
   - 制定详细的响应步骤

2. **恢复计划**：
   - 制定数据恢复流程
   - 定期测试恢复能力
   - 记录和分析安全事件，持续改进

## 合规性

1. **数据保护法规**：
   - 遵循相关数据保护法规
   - 实施数据最小化原则
   - 提供数据访问和删除机制

2. **安全审计**：
   - 定期进行安全审计
   - 执行渗透测试
   - 更新安全措施以应对新威胁
