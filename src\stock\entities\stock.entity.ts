import { Entity, Column, PrimaryColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('Stock')
export class SqlServerStock {
  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @PrimaryColumn({ name: 'Stock Code' })
  StockCode: string;

  @ApiProperty({ description: '存放位置', example: 'A-101' })
  @Column({ name: 'Bin / Shelf No' })
  BinShelfNo: string;

  @ApiProperty({ description: '计量单位', example: 'PCS' })
  @Column({ name: 'UOM' })
  UOM: string;

  @ApiProperty({ description: '当前库存数量', example: 10 })
  @Column({ name: 'Current Quantity' })
  CurrentQuantity: number;
}