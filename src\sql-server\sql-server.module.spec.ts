import { Test, TestingModule } from '@nestjs/testing';
import { SqlServerModule } from './sql-server.module';
import { ConfigModule } from '@nestjs/config';
import databaseConfig from '../config/database.config';

jest.mock('@nestjs/typeorm', () => ({
  TypeOrmModule: {
    forRootAsync: jest.fn().mockReturnValue({
      module: class MockModule {},
      providers: [],
    }),
  },
}));

describe('SqlServerModule', () => {
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [databaseConfig],
        }),
        SqlServerModule,
      ],
    }).compile();
  }, 30000); // 增加超时时间到30秒

  it('should be defined', () => {
    expect(module).toBeDefined();
  });
}); 