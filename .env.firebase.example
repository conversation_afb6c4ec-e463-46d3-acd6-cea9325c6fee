# ========================================
# Firebase Configuration Template
# ========================================
# 复制此文件为 .env 并填入实际的Firebase配置信息

# Firebase项目基本信息
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_STORAGE_BUCKET=your-firebase-project-id.appspot.com

# ========================================
# 服务账户配置 - 选择其中一种方式
# ========================================

# 方式1: 使用服务账户JSON文件 (推荐用于开发环境)
# 将从Firebase Console下载的服务账户JSON文件放在项目根目录
FIREBASE_SERVICE_ACCOUNT_PATH=./firebase-service-account.json

# 方式2: 使用环境变量 (推荐用于生产环境)
# 从服务账户JSON文件中提取以下信息
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
# FIREBASE_CLIENT_EMAIL=<EMAIL>

# ========================================
# Firebase Storage上传配置
# ========================================

# PDF文件在Firebase Storage中的根目录
FIREBASE_PDF_ROOT_PATH=pdfs

# 是否启用Firebase上传功能 (true/false)
FIREBASE_UPLOAD_ENABLED=true

# 上传重试次数
FIREBASE_RETRY_ATTEMPTS=3

# 上传超时时间 (毫秒)
FIREBASE_UPLOAD_TIMEOUT=30000

# ========================================
# Firebase自动清理配置
# ========================================

# 是否启用自动清理功能 (true/false)
FIREBASE_AUTO_CLEANUP_ENABLED=true

# 文件保留天数（超过此天数的文件将被删除，默认90天即3个月）
FIREBASE_CLEANUP_DAYS=90

# 是否启用周度统计报告 (true/false)
FIREBASE_WEEKLY_STATS_ENABLED=true

# ========================================
# Firebase日志配置
# ========================================

# 是否启用Firebase日志 (true/false)
FIREBASE_LOGGING_ENABLED=true

# 日志级别 (debug/info/warn/error)
FIREBASE_LOG_LEVEL=info

# ========================================
# 配置说明
# ========================================
# 
# 1. 获取Firebase配置:
#    - 访问 https://console.firebase.google.com/
#    - 选择你的项目
#    - 进入 "项目设置" > "服务账户"
#    - 点击 "生成新的私钥" 下载JSON文件
#
# 2. 设置Storage权限:
#    - 在Firebase Console中进入 "Storage"
#    - 设置适当的安全规则
#
# 3. 开发环境建议:
#    - 使用方式1 (JSON文件)
#    - 设置 FIREBASE_UPLOAD_ENABLED=false 进行测试
#
# 4. 生产环境建议:
#    - 使用方式2 (环境变量)
#    - 确保私钥安全存储
#    - 设置适当的日志级别
#
# ========================================
