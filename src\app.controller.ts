import { Controller, Get, Post, Body, HttpCode } from '@nestjs/common';
import { AppService } from './app.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SqlServerMain } from './sql-server/entities/main.entity';
import { SqlServerDetail } from './sql-server/entities/detail.entity';
import { SqlServerScTranDetail } from './sql-server/entities/sc-tran-detail.entity';
import { Main } from './postgres/entities/main.entity';
import { Detail } from './postgres/entities/detail.entity';
import { SyncService } from './sync/sync.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';

@ApiTags('API')
@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly syncService: SyncService,
    @InjectRepository(SqlServerMain, 'sqlServerConnection')
    private readonly sqlServerMainRepository: Repository<SqlServerMain>,
    @InjectRepository(SqlServerScTranDetail, 'sqlServerConnection')
    private readonly sqlServerScTranDetailRepository: Repository<SqlServerScTranDetail>,
    @InjectRepository(Main, 'postgresConnection')
    private readonly postgresMainRepository: Repository<Main>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly postgresDetailRepository: Repository<Detail>,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @ApiOperation({ summary: '获取SQL Server数据' })
  @ApiResponse({ 
    status: 200, 
    description: '返回所有SQL Server main和SC_Tran_Detail记录。返回格式为 {main: [...], detail: [...]}。main包含字段: DocumentNo, TransactionType, DocumentDate, Customer, Salesman等信息；detail包含字段: DocumentNo, Line, StockCode, Quantity, ParentCode等' 
  })
  @Get('sqlserver')
  async getSqlServerData() {
    try {
      const main = await this.sqlServerMainRepository.find();
      const detail = await this.sqlServerScTranDetailRepository.find();
      return { main, detail };
    } catch (error) {
      return { error: error.message };
    }
  }

  @ApiOperation({ summary: '获取PostgreSQL数据' })
  @ApiResponse({ 
    status: 200, 
    description: '返回所有PostgreSQL main记录，不包含detail关联数据。返回字段包括: id, document_no, transaction_type, document_date, customer_code, salesman_code, issue_by, issue_date, issue_time, deliver_to, remarks, do_amount, customer_name, created_at' 
  })
  @Get('postgres')
  async getPostgresData() {
    try {
      const main = await this.postgresMainRepository.find();
      return main;
    } catch (error) {
      return { error: error.message };
    }
  }

  @ApiOperation({ summary: '获取PostgreSQL详细数据' })
  @ApiResponse({ 
    status: 200, 
    description: '返回所有PostgreSQL main记录及其关联的detail记录。每条main记录包含details数组字段，其中包含所有关联的detail记录。' 
  })
  @Get('postgres/details')
  async getPostgresDataWithDetails() {
    try {
      const mains = await this.postgresMainRepository.find();
      const result = [];
      
      // 对每个main记录，查找其关联的所有detail记录
      for (const main of mains) {
        // 使用main_id查询关联的detail记录
        const details = await this.postgresDetailRepository.find({
          where: { main_id: main.id }
        });
        
        // 附加details到main记录
        result.push({
          ...main,
          details
        });
      }
      
      return result;
    } catch (error) {
      return { error: error.message };
    }
  }

  @ApiOperation({ summary: '获取PostgreSQL明细记录' })
  @ApiResponse({ 
    status: 200, 
    description: '返回所有PostgreSQL detail记录，不包含main关联数据。返回字段包括: id, document_no, stock_code, location_code, do_quantity, created_at, updated_at' 
  })
  @Get('postgres/detail')
  async getPostgresDetailData() {
    try {
      const detail = await this.postgresDetailRepository.find();
      return detail;
    } catch (error) {
      return { error: error.message };
    }
  }

  @ApiOperation({ summary: '手动触发同步' })
  @ApiResponse({ 
    status: 200, 
    description: '手动触发从SQL Server到PostgreSQL的数据同步，返回同步结果。返回格式: {message: "同步完成"} 或错误信息 {error: "错误信息"}' 
  })
  @HttpCode(200)
  @Post('sync')
  async syncData() {
    try {
      await this.syncService.syncData();
      return { message: '同步完成' };
    } catch (error) {
      return { error: error.message };
    }
  }

  @ApiOperation({ summary: '创建SQL Server Main记录' })
  @ApiBody({ 
    description: '主记录信息，必须包含DocumentNo和DocumentDate字段，其他字段可选',
    type: SqlServerMain 
  })
  @ApiResponse({ 
    status: 201, 
    description: '成功创建记录，返回创建的记录信息',
    type: SqlServerMain
  })
  @Post('sqlserver/main')
  async createSqlServerMain(@Body() mainData: Partial<SqlServerMain>) {
    try {
      const main = this.sqlServerMainRepository.create(mainData);
      const result = await this.sqlServerMainRepository.save(main);
      return result;
    } catch (error) {
      return { error: error.message };
    }
  }

  @ApiOperation({ summary: '创建SQL Server Detail记录' })
  @ApiBody({ 
    description: '详情记录信息，必须包含DocumentNo字段，关联到Main表',
    type: SqlServerScTranDetail 
  })
  @ApiResponse({ 
    status: 201, 
    description: '成功创建记录，返回创建的记录信息',
    type: SqlServerScTranDetail
  })
  @Post('sqlserver/detail')
  async createSqlServerDetail(@Body() detailData: Partial<SqlServerScTranDetail>) {
    try {
      const detail = this.sqlServerScTranDetailRepository.create(detailData);
      const result = await this.sqlServerScTranDetailRepository.save(detail);
      return result;
    } catch (error) {
      return { error: error.message };
    }
  }
} 