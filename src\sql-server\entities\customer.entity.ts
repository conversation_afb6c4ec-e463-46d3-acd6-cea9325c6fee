import { Entity, Column, PrimaryColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('Customer_tbl')
export class SqlServerCustomer {
  @ApiProperty({ description: '客户代码', example: 'CUST001' })
  @PrimaryColumn({ name: 'Customer Code' })
  CustomerCode: string;

  @ApiProperty({ description: '客户名称', example: '测试客户' })
  @Column({ name: 'Customer Name' })
  CustomerName: string;

  @ApiProperty({ description: '客户邮箱', example: '<EMAIL>' })
  @Column({ name: 'Customer Email', nullable: true })
  CustomerEmail: string;

  @ApiProperty({ description: '客户送货地址', example: '123 Main St' })
  @Column({ name: 'Customer Deliver Address', nullable: true })
  CustomerDeliverAddress: string;

  @ApiProperty({ description: '客户电话', example: '************' })
  @Column({ name: 'Customer Telephone', nullable: true })
  CustomerTelephone: string;

  @ApiProperty({ description: '客户传真', example: '************' })
  @Column({ name: 'Customer Fax', nullable: true })
  CustomerFax: string;

  @ApiProperty({ description: '销售员代码', example: 'S001' })
  @Column({ name: 'Salesman Code', nullable: true })
  SalesmanCode: string;

  @ApiProperty({ description: '付款条件', example: 'CASH' })
  @Column({ name: 'Term', nullable: true })
  Term: string;
}
