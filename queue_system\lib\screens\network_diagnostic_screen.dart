import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/utils/network_checker.dart';

class NetworkDiagnosticScreen extends StatefulWidget {
  const NetworkDiagnosticScreen({Key? key}) : super(key: key);

  @override
  _NetworkDiagnosticScreenState createState() =>
      _NetworkDiagnosticScreenState();
}

class _NetworkDiagnosticScreenState extends State<NetworkDiagnosticScreen> {
  String _diagnosticResults = '';
  bool _isRunningDiagnostics = false;
  final NetworkChecker _networkChecker = NetworkChecker();

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunningDiagnostics = true;
      _diagnosticResults = context.t('running_diagnostics');
    });

    try {
      final results = await _networkChecker.runDiagnostics();
      setState(() {
        _diagnosticResults = results;
        _isRunningDiagnostics = false;
      });
    } catch (e) {
      setState(() {
        _diagnosticResults = '${context.t('error_running_diagnostics')}: $e';
        _isRunningDiagnostics = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('network_diagnostic')),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.t('network_diagnostic_explanation'),
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _diagnosticResults,
                    style: const TextStyle(
                      color: Colors.greenAccent,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunningDiagnostics
                    ? null
                    : () {
                        _runDiagnostics();
                      },
                icon: const Icon(Icons.refresh),
                label: Text(context.t('run_diagnostics_again')),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(context.t('back')),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
