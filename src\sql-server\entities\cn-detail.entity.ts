import { Entity, Column, PrimaryC<PERSON>umn, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('AR_CN_Stock_Detail')
export class SqlServerCnDetail {
  @ApiProperty({ description: '文档编号', example: 'CN123456' })
  @Column({ name: 'Document No', primary: true })
  DocumentNo: string;

  @ApiProperty({ description: '行号', example: 1 })
  @Column({ name: 'Line', primary: true })
  Line: number;

  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @Column({ name: 'Stock' })
  Stock: string;

  @ApiProperty({ description: '产品描述', example: '办公用品' })
  @Column({ name: 'Description' })
  Description: string;

  @ApiProperty({ description: '数量', example: 10 })
  @Column({ name: 'Quantity' })
  Quantity: number;

  @ApiProperty({ description: '计量单位', example: 'PCS' })
  @Column({ name: 'UOM' })
  UOM: string;

  @ApiProperty({ description: '单价', example: 10.5 })
  @Column({ name: 'Unit Price' })
  UnitPrice: number;

  @ApiProperty({ description: '总金额', example: 105 })
  @Column({ name: 'Total Amount' })
  TotalAmount: number;

  @ManyToOne('SqlServerCnMain', 'details')
  @JoinColumn({ name: 'Document No', referencedColumnName: 'DocumentNo' })
  main: any;
}
