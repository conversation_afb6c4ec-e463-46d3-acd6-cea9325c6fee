# PDF Issue Diagnosis Script

# Import common path functions
. "$PSScriptRoot\common-paths.ps1"

Write-Host "=== PDF API Issue Diagnosis Tool ===" -ForegroundColor Green

# Get project paths
$paths = Get-ProjectPaths

# Validate project structure
if (!(Test-ProjectStructure -Paths $paths)) {
    Write-Host "WARNING Project structure validation failed, continuing anyway..." -ForegroundColor Yellow
}

Write-Host "`n1. Check working directory and paths" -ForegroundColor Cyan
Write-Host "Current working directory: $(Get-Location)"
Write-Host "Project root directory: $($paths.ProjectRoot)"

Write-Host "`n2. Check PDF output directory" -ForegroundColor Cyan
$pdfDir = $paths.PDFOutput
if (Test-Path $pdfDir) {
    Write-Host "PDF_Output directory exists: $pdfDir" -ForegroundColor Green
    $subDirs = Get-ChildItem $pdfDir -Directory | Select-Object -First 5
    if ($subDirs) {
        Write-Host "Subdirectories:"
        $subDirs | ForEach-Object { Write-Host "  - $($_.Name)" }

        # Check latest PDF files
        $latestPdf = Get-ChildItem $pdfDir -Recurse -Filter "*.pdf" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
        if ($latestPdf) {
            Write-Host "Recent PDF files:"
            $latestPdf | ForEach-Object {
                Write-Host "  - $($_.FullName) ($(Get-Date $_.LastWriteTime -Format 'yyyy-MM-dd HH:mm:ss'))"
            }
        } else {
            Write-Host "No PDF files found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "PDF_Output directory is empty" -ForegroundColor Yellow
    }
} else {
    Write-Host "PDF_Output directory does not exist: $pdfDir" -ForegroundColor Red
}

Write-Host "`n3. Check environment variables" -ForegroundColor Cyan
$envFile = Join-Path $paths.ProjectRoot ".env"
if (Test-Path $envFile) {
    Write-Host ".env file exists" -ForegroundColor Green
    $envContent = Get-Content $envFile | Where-Object { $_ -like "*PDF*" -or $_ -like "*NODE_ENV*" }
    if ($envContent) {
        Write-Host "Related environment variables:"
        $envContent | ForEach-Object { Write-Host "  $_" }
    }
} else {
    Write-Host ".env file does not exist" -ForegroundColor Red
}

Write-Host "`n4. Check PM2 process status" -ForegroundColor Cyan
try {
    $pm2Status = pm2 jlist | ConvertFrom-Json
    $backendProcess = $pm2Status | Where-Object { $_.name -eq "backend-nestjs" }
    if ($backendProcess) {
        Write-Host "PM2 process status: $($backendProcess.pm2_env.status)" -ForegroundColor Green
        Write-Host "Process ID: $($backendProcess.pid)"
        Write-Host "Working directory: $($backendProcess.pm2_env.cwd)"
        Write-Host "Start time: $($backendProcess.pm2_env.created_at)"
        Write-Host "Restart count: $($backendProcess.pm2_env.restart_time)"
    } else {
        Write-Host "backend-nestjs process not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Cannot get PM2 status, PM2 may not be running" -ForegroundColor Yellow
}

Write-Host "`n5. Check Chrome/Edge browser" -ForegroundColor Cyan
$chromePaths = @(
    "C:\Program Files\Google\Chrome\Application\chrome.exe",
    "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
    "C:\Program Files\Microsoft\Edge\Application\msedge.exe",
    "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
)

$foundBrowser = $false
foreach ($path in $chromePaths) {
    if (Test-Path $path) {
        Write-Host "Found browser: $path" -ForegroundColor Green
        $foundBrowser = $true
        break
    }
}

if (-not $foundBrowser) {
    Write-Host "Chrome or Edge browser not found" -ForegroundColor Red
}

Write-Host "`n6. Test PDF API" -ForegroundColor Cyan
# 从配置文件中提取端口号
$port = "3000"  # 默认端口
$configFiles = @("ecosystem.production.config.js", "ecosystem.config.js")
foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        $configContent = Get-Content $configFile -Raw
        if ($configContent -match "PORT:\s*(\d+)") {
            $port = $matches[1]
            break
        }
    }
}

try {
    $response = Invoke-WebRequest -Uri "http://localhost:$port/pdf/view?documentNo=TEST001" -Method GET -TimeoutSec 10
    Write-Host "PDF API response status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "PDF API test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n7. Check log files" -ForegroundColor Cyan
$logDir = $paths.Logs
if (Test-Path $logDir) {
    $logFiles = Get-ChildItem $logDir -Filter "*.log" | Sort-Object LastWriteTime -Descending
    if ($logFiles) {
        Write-Host "Log files:"
        $logFiles | Select-Object -First 3 | ForEach-Object {
            Write-Host "  - $($_.Name) ($(Get-Date $_.LastWriteTime -Format 'yyyy-MM-dd HH:mm:ss'))"
        }

        # Show last few lines of latest error log
        $errorLog = $logFiles | Where-Object { $_.Name -like "*error*" } | Select-Object -First 1
        if ($errorLog) {
            Write-Host "`nLatest error log ($($errorLog.Name)) last 10 lines:"
            Get-Content $errorLog.FullName -Tail 10 | ForEach-Object { Write-Host "  $_" }
        }
    } else {
        Write-Host "No log files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "logs directory does not exist" -ForegroundColor Yellow
}

Write-Host "`n=== Diagnosis Complete ===" -ForegroundColor Green
Write-Host "If issues are found, please fix them based on the above information."
