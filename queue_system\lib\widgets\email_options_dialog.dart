import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/screens/native_pdf_viewer_screen.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/utils/app_config.dart';

class EmailOptionsDialog extends StatefulWidget {
  final String documentNo;
  final String? defaultEmail;
  final Function(String) onSendEmail;
  final Function() onSkipEmail;
  final Function(String) onViewPdf;

  const EmailOptionsDialog({
    super.key,
    required this.documentNo,
    this.defaultEmail,
    required this.onSendEmail,
    required this.onSkipEmail,
    required this.onViewPdf,
  });

  @override
  State<EmailOptionsDialog> createState() => _EmailOptionsDialogState();
}

class _EmailOptionsDialogState extends State<EmailOptionsDialog> {
  late TextEditingController _emailController;
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController(text: widget.defaultEmail ?? '');
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(context.t('pdf_email_options')),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${context.t('document_no')}: ${widget.documentNo}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              context.t('pdf_created_successfully'),
              style: TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: InputDecoration(
                labelText: context.t('email'),
                hintText: '<EMAIL>, <EMAIL>',
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  // 支持多个邮箱地址，用逗号分隔
                  List<String> emails =
                      value.split(',').map((e) => e.trim()).toList();

                  // 更灵活的邮箱格式验证，支持多级域名
                  final emailRegex = RegExp(
                      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

                  for (String email in emails) {
                    if (email.isEmpty) continue; // 跳过空字符串
                    if (!emailRegex.hasMatch(email)) {
                      return context.t('invalid_email');
                    }
                  }
                }
                return null;
              },
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
      actions: [
        // 查看PDF按钮
        TextButton.icon(
          icon: const Icon(Icons.picture_as_pdf),
          label: Text(context.t('view_pdf')),
          onPressed: _isLoading
              ? null
              : () {
                  final pdfUrl = CheckService(baseUrl: AppConfig.baseUrl)
                      .getPdfViewUrl(widget.documentNo);
                  widget.onViewPdf(pdfUrl);
                },
        ),
        // 不发送Email按钮
        TextButton(
          onPressed: _isLoading ? null : widget.onSkipEmail,
          child: Text(context.t('skip_email')),
        ),
        // 发送Email按钮
        ElevatedButton(
          onPressed: _isLoading
              ? null
              : () {
                  if (_formKey.currentState!.validate()) {
                    if (_emailController.text.isEmpty) {
                      // 如果邮箱为空，提示用户
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(context.t('email_required')),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    widget.onSendEmail(_emailController.text);
                  }
                },
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(context.t('send_email')),
        ),
      ],
    );
  }
}
