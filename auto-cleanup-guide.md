# 🗑️ Firebase Storage自动删除超期文件功能指南

## 功能概述

自动删除Firebase Storage中超过3个月（90天）的PDF文件，帮助控制存储成本和管理文件生命周期。

## 🔧 配置选项

在 `.env` 文件中添加以下配置：

```env
# 是否启用自动清理功能
FIREBASE_AUTO_CLEANUP_ENABLED=true

# 文件保留天数（默认90天，即3个月）
FIREBASE_CLEANUP_DAYS=90

# 是否启用周度统计报告
FIREBASE_WEEKLY_STATS_ENABLED=true
```

## 📅 定时任务计划

### 自动清理任务
- **执行时间**：每月1日凌晨2点（中国时区）
- **功能**：删除超过指定天数的PDF文件
- **安全限制**：最少保留30天的文件

### 统计报告任务
- **执行时间**：每周日凌晨3点（中国时区）
- **功能**：生成Firebase Storage使用统计报告
- **内容**：文件数量、存储大小、按月分布等

## 🔍 API端点

### 1. 获取存储统计

**GET** `/firebase-test/storage-stats`

获取Firebase Storage的详细使用统计。

**响应示例：**
```json
{
  "success": true,
  "totalFiles": 150,
  "totalSize": 52428800,
  "totalSizeMB": "50.00 MB",
  "oldestFile": {
    "path": "pdfs/2025-03-19/DOC001.pdf",
    "created": "2025-03-19T10:30:00.000Z"
  },
  "newestFile": {
    "path": "pdfs/2025-06-19/DOC150.pdf",
    "created": "2025-06-19T15:45:00.000Z"
  },
  "filesByMonth": {
    "2025-06": { "count": 45, "size": 15728640 },
    "2025-05": { "count": 38, "size": 13107200 },
    "2025-04": { "count": 42, "size": 14680064 },
    "2025-03": { "count": 25, "size": 8912896 }
  }
}
```

### 2. 清理超期文件（按天数）

**POST** `/firebase-test/cleanup-old-files`

清理超过指定天数的PDF文件。

**参数：**
- `daysOld` (可选) - 文件超过多少天被认为是过期的（默认90天）
- `dryRun` (可选) - 是否为试运行（默认true，只检查不删除）

**响应示例：**
```json
{
  "success": true,
  "totalFiles": 150,
  "expiredFiles": 25,
  "deletedFiles": 25,
  "failedDeletes": 0,
  "savedSpace": 8912896,
  "savedSpaceMB": "8.50 MB",
  "message": "清理完成：删除了25个文件，节省了8.50MB空间",
  "details": [
    {
      "path": "pdfs/2025-03-19/DOC001.pdf",
      "size": 356352,
      "created": "2025-03-19T10:30:00.000Z",
      "deleted": true
    }
  ]
}
```

### 3. 清理指定日期前的文件

**POST** `/firebase-test/cleanup-by-date`

清理指定日期之前的PDF文件。

**参数：**
- `beforeDate` (必需) - 删除此日期之前的文件（格式：YYYY-MM-DD）
- `dryRun` (可选) - 是否为试运行（默认true）

**示例：**
```
POST /firebase-test/cleanup-by-date?beforeDate=2025-03-01&dryRun=false
```

### 4. 获取定时任务状态

**GET** `/firebase-test/scheduler-status`

获取定时任务的配置和状态。

**响应示例：**
```json
{
  "success": true,
  "autoCleanupEnabled": true,
  "weeklyStatsEnabled": true,
  "cleanupDays": 90,
  "cleanupSchedule": "每月1日凌晨2点",
  "statsSchedule": "每周日凌晨3点",
  "timezone": "Asia/Shanghai"
}
```

### 5. 手动触发清理任务

**POST** `/firebase-test/trigger-manual-cleanup`

手动触发清理任务（用于测试或紧急清理）。

**参数：**
- `daysOld` (可选) - 文件超过多少天被认为是过期的（默认90天）
- `dryRun` (可选) - 是否为试运行（默认true）

## 🛡️ 安全机制

### 1. 最小保留期限
- 系统强制要求至少保留30天的文件
- 任何小于30天的清理请求都会被拒绝

### 2. 试运行模式
- 默认所有清理操作都是试运行模式
- 必须明确设置 `dryRun=false` 才会实际删除文件

### 3. 详细日志记录
- 所有清理操作都会记录详细日志
- 包含删除的文件列表、大小、时间等信息

### 4. 错误处理
- 单个文件删除失败不会影响整个清理过程
- 提供详细的失败原因和统计

## 📊 使用场景

### 场景1：定期维护（推荐）
让系统自动执行月度清理：
```env
FIREBASE_AUTO_CLEANUP_ENABLED=true
FIREBASE_CLEANUP_DAYS=90
```

### 场景2：手动清理测试
在执行实际清理前先试运行：
```bash
# 试运行，查看将要删除的文件
curl -X POST "http://localhost:3000/firebase-test/cleanup-old-files?daysOld=120&dryRun=true"

# 确认无误后执行实际清理
curl -X POST "http://localhost:3000/firebase-test/cleanup-old-files?daysOld=120&dryRun=false"
```

### 场景3：紧急空间清理
清理指定日期前的所有文件：
```bash
# 删除2025年3月1日之前的所有文件
curl -X POST "http://localhost:3000/firebase-test/cleanup-by-date?beforeDate=2025-03-01&dryRun=false"
```

### 场景4：存储监控
定期检查存储使用情况：
```bash
curl "http://localhost:3000/firebase-test/storage-stats"
```

## ⚠️ 注意事项

1. **不可恢复**：删除的文件无法恢复，请谨慎操作
2. **网络依赖**：清理过程需要稳定的网络连接
3. **权限要求**：确保Firebase服务账户有删除文件的权限
4. **时区设置**：定时任务使用中国时区（Asia/Shanghai）
5. **性能影响**：大量文件清理可能需要较长时间

## 🔧 故障排除

### 问题1：定时任务未执行
- 检查 `FIREBASE_AUTO_CLEANUP_ENABLED` 是否为 `true`
- 确认Firebase服务已正确初始化
- 查看应用日志中的定时任务相关信息

### 问题2：文件删除失败
- 检查Firebase服务账户权限
- 确认网络连接稳定
- 查看详细错误信息

### 问题3：清理效果不明显
- 检查文件的实际创建时间
- 确认清理天数设置是否合理
- 使用试运行模式验证清理范围

## 📈 监控建议

1. **定期检查统计**：每周查看存储统计报告
2. **监控清理日志**：关注月度清理任务的执行结果
3. **空间趋势分析**：跟踪存储使用量的变化趋势
4. **成本优化**：根据实际使用情况调整保留天数
