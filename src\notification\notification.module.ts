import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { Notification } from '../postgres/entities/notification.entity';
import { Staff } from '../postgres/entities/staff.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Notification, Staff], 'postgresConnection')
  ],
  providers: [NotificationService],
  controllers: [NotificationController],
  exports: [NotificationService]
})
export class NotificationModule {} 