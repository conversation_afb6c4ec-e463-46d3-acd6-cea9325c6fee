# 权限系统重构说明

## 重构概述

本次重构将原有的 `senior` 权限拆分为两个独立的权限级别：
- **`supervisor`（主管）**：保留核心业务功能，移除管理员专属功能
- **`admin`（管理员）**：继承所有 supervisor 功能，并拥有管理员专属功能

## 权限级别定义

### 新的权限体系
1. **`regular`**: 普通员工
2. **`staff_bom`**: BOM 专员
3. **`supervisor`**: 主管
4. **`admin`**: 管理员
5. **`driver`**: 司机

### 权限功能分配

#### Admin 专属功能
- ✅ 注册新员工 (Register New Staff)
- ✅ 员工管理功能 (Staff Management)
- ✅ 紧急队列功能 (Emergency Queue)
- ✅ 重新读取单据功能 (Refresh Order)

#### Supervisor 保留功能
- ✅ BOM 项目管理
- ✅ 拒绝订单并重置状态
- ✅ 查看 PDF 历史
- ✅ 处理特殊 BOM 项目（空货架位置或无库存）
- ✅ 所有楼层数据访问权限
- ✅ 主管检查和确认功能

## 修改的文件列表

### 后端文件
1. **`src/postgres/entities/staff.entity.ts`**
   - 更新权限级别注释

2. **`src/staff/staff.controller.ts`**
   - 更新权限验证逻辑
   - 添加 'supervisor' 和 'admin' 到有效权限列表
   - 更新楼层权限设置逻辑

3. **`src/check/check.service.ts`**
   - 更新 BOM 子项详细信息权限检查
   - 更新员工检查权限验证
   - 更新文档就绪检查权限
   - **紧急队列功能限制为仅 admin 用户**

4. **`src/check/check.controller.ts`**
   - 更新文档就绪检查权限验证

5. **`src/sync/sync.service.ts`**
   - **重新读取单据功能限制为仅 admin 用户**

### 前端文件
1. **`queue_system/lib/models/staff.dart`**
   - 更新权限级别注释

2. **`queue_system/lib/providers/auth_provider.dart`**
   - 添加 `isSupervisor` 和 `isAdmin` getter
   - 保持 `isSenior` 向后兼容性

3. **`queue_system/lib/screens/home_screen.dart`**
   - **员工管理按钮限制为仅 admin 用户显示**
   - 更新 PDF 历史按钮权限（supervisor + admin + driver）
   - 更新标签页权限逻辑

4. **`queue_system/lib/providers/check_list_provider.dart`**
   - 更新数据过滤权限逻辑

5. **`queue_system/lib/screens/pdf_list_screen.dart`**
   - 更新 PDF 访问权限检查

6. **`queue_system/lib/widgets/check_item_card.dart`**
   - 更新主管确认按钮权限检查

7. **`queue_system/lib/widgets/waiting_tab.dart`**
   - **紧急队列按钮限制为仅 admin 用户显示**
   - **重新同步按钮限制为仅 admin 用户显示**
   - 更新权限检查逻辑

8. **`queue_system/lib/screens/staff_management_screen.dart`**
   - 更新权限级别颜色显示
   - 更新权限级别名称显示

9. **`queue_system/lib/screens/add_staff_screen.dart`**
   - 更新权限级别选项列表
   - 更新权限级别显示名称

### 文档文件
1. **`.cursor/rules/backend/data-models.md`**
2. **`.cursor/rules/data-models.mdc`**
3. **`.cursor/rules/security-guidelines.md`**
4. **`.cursor/rules/backend/business-flow.md`**
5. **`.cursor/rules/backend/api-reference.md`**
6. **`queue_system/.cursor/rules/providers.mdc`**
7. **`queue_system/.cursor/rules/screens-widgets.mdc`**
8. **`PERMISSION_DATA_ISSUE_SOLUTION.md`**

## 向后兼容性

- 保留了 `isSenior` getter 以确保现有代码的兼容性
- `isSenior` 现在等同于 `isSupervisor`（supervisor + admin）
- 所有现有的 supervisor 功能对新的 supervisor 和 admin 用户都可用

## 数据库迁移注意事项

⚠️ **重要**：需要手动更新现有数据库中的用户权限级别：

### SQL 迁移脚本示例

```sql
-- 1. 将需要管理功能的 senior 用户更新为 admin
UPDATE staff
SET level = 'admin'
WHERE level = 'senior'
AND username IN ('admin_user1', 'admin_user2'); -- 替换为实际的管理员用户名

-- 2. 将其他 senior 用户更新为 supervisor
UPDATE staff
SET level = 'supervisor'
WHERE level = 'senior';

-- 3. 验证更新结果
SELECT username, level, floor FROM staff WHERE level IN ('supervisor', 'admin');
```

### 迁移建议
- 将需要管理功能的用户设置为 `'admin'`
- 将只需要业务功能的用户设置为 `'supervisor'`
- 在生产环境执行前，请先在测试环境验证

## 测试建议

1. **权限验证测试**
   - 测试 admin 用户可以访问所有功能
   - 测试 supervisor 用户无法访问管理员专属功能
   - 测试其他权限用户的功能不受影响

2. **UI 显示测试**
   - 验证管理员专属按钮只对 admin 用户显示
   - 验证 supervisor 用户可以正常使用业务功能
   - 验证权限级别在员工管理页面正确显示

3. **API 权限测试**
   - 测试紧急队列 API 只允许 admin 用户访问
   - 测试重新读取单据 API 只允许 admin 用户访问
   - 测试其他 API 的权限检查正常工作

## 部署步骤

1. 部署后端代码更新
2. 更新数据库中的用户权限级别
3. 部署前端代码更新
4. 进行权限功能测试
5. 通知用户权限变更

---

## 重构完成状态

✅ **重构已完成** - 所有代码修改已实施

### 完成的修改
- ✅ 后端权限验证逻辑更新（5个文件）
- ✅ 前端权限检查逻辑更新（9个文件）
- ✅ 文档和配置文件更新（8个文件）
- ✅ 向后兼容性保证（isSenior getter）
- ✅ 数据库迁移脚本提供

### 待执行步骤
- ⏳ 数据库权限级别迁移
- ⏳ 生产环境部署
- ⏳ 权限功能测试

---

**重构完成日期**: 2025-06-15
**影响范围**: 权限系统、用户管理、紧急队列、单据管理
**向后兼容**: 是（通过 isSenior getter）
