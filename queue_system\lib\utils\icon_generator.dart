import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

/// 图标生成器类，用于生成不同尺寸的应用图标
class IconGenerator {
  /// 生成Android图标
  static Future<void> generateAndroidIcons(String iconPath) async {
    final ByteData data = await rootBundle.load(iconPath);
    final List<int> bytes = data.buffer.asUint8List();
    final img.Image? originalIcon = img.decodeImage(bytes);
    
    if (originalIcon == null) {
      throw Exception('无法解码图标图像');
    }
    
    // Android图标尺寸
    final Map<String, int> androidIconSizes = {
      'mipmap-mdpi': 48,
      'mipmap-hdpi': 72,
      'mipmap-xhdpi': 96,
      'mipmap-xxhdpi': 144,
      'mipmap-xxxhdpi': 192,
    };
    
    // 生成并保存不同尺寸的图标
    for (final entry in androidIconSizes.entries) {
      final String directory = entry.key;
      final int size = entry.value;
      
      // 调整图标大小
      final img.Image resizedIcon = img.copyResize(
        originalIcon,
        width: size,
        height: size,
        interpolation: img.Interpolation.cubic,
      );
      
      // 创建圆角图标
      final img.Image roundedIcon = _createRoundedIcon(resizedIcon);
      
      // 保存图标
      final String iconDirectory = 'android/app/src/main/res/$directory';
      await Directory(iconDirectory).create(recursive: true);
      final File iconFile = File('$iconDirectory/ic_launcher.png');
      await iconFile.writeAsBytes(img.encodePng(roundedIcon));
    }
  }
  
  /// 生成iOS图标
  static Future<void> generateIosIcons(String iconPath) async {
    final ByteData data = await rootBundle.load(iconPath);
    final List<int> bytes = data.buffer.asUint8List();
    final img.Image? originalIcon = img.decodeImage(bytes);
    
    if (originalIcon == null) {
      throw Exception('无法解码图标图像');
    }
    
    // iOS图标尺寸
    final Map<String, int> iosIconSizes = {
      '<EMAIL>': 20,
      '<EMAIL>': 40,
      '<EMAIL>': 60,
      '<EMAIL>': 29,
      '<EMAIL>': 58,
      '<EMAIL>': 87,
      '<EMAIL>': 40,
      '<EMAIL>': 80,
      '<EMAIL>': 120,
      '<EMAIL>': 120,
      '<EMAIL>': 180,
      '<EMAIL>': 76,
      '<EMAIL>': 152,
      '<EMAIL>': 167,
      '<EMAIL>': 1024,
    };
    
    // 生成并保存不同尺寸的图标
    final String iconDirectory = 'ios/Runner/Assets.xcassets/AppIcon.appiconset';
    await Directory(iconDirectory).create(recursive: true);
    
    for (final entry in iosIconSizes.entries) {
      final String fileName = entry.key;
      final int size = entry.value;
      
      // 调整图标大小
      final img.Image resizedIcon = img.copyResize(
        originalIcon,
        width: size,
        height: size,
        interpolation: img.Interpolation.cubic,
      );
      
      // 保存图标
      final File iconFile = File('$iconDirectory/$fileName');
      await iconFile.writeAsBytes(img.encodePng(resizedIcon));
    }
  }
  
  /// 创建圆角图标
  static img.Image _createRoundedIcon(img.Image icon) {
    final int width = icon.width;
    final int height = icon.height;
    final int radius = width ~/ 8; // 圆角半径
    
    // 创建一个透明背景的图像
    final img.Image result = img.Image(width: width, height: height);
    
    // 绘制圆角矩形
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        // 检查是否在圆角区域内
        bool inCorner = false;
        
        // 左上角
        if (x < radius && y < radius) {
          inCorner = (x - radius) * (x - radius) + (y - radius) * (y - radius) > radius * radius;
        }
        // 右上角
        else if (x >= width - radius && y < radius) {
          inCorner = (x - (width - radius)) * (x - (width - radius)) + (y - radius) * (y - radius) > radius * radius;
        }
        // 左下角
        else if (x < radius && y >= height - radius) {
          inCorner = (x - radius) * (x - radius) + (y - (height - radius)) * (y - (height - radius)) > radius * radius;
        }
        // 右下角
        else if (x >= width - radius && y >= height - radius) {
          inCorner = (x - (width - radius)) * (x - (width - radius)) + (y - (height - radius)) * (y - (height - radius)) > radius * radius;
        }
        
        if (!inCorner) {
          result.setPixel(x, y, icon.getPixel(x, y));
        }
      }
    }
    
    return result;
  }
}
