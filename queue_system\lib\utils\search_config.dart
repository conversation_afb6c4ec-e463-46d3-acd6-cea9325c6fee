/// 统一的搜索配置
class SearchConfig {
  // 防抖延迟时间
  static const Duration debounceDelay = Duration(seconds: 1);
  
  // 分页配置
  static const int defaultPageSize = 10;
  static const int initialPage = 1;
  
  // 搜索字段配置
  static const List<String> documentSearchFields = [
    'document_no',
    'customer',
    'customer_name',
    'remarks',
    'description',
    'part_no',
  ];
  
  // 搜索提示文本
  static const Map<String, String> searchHints = {
    'documents': 'search_documents',
    'shipments': 'search_shipments',
    'items': 'search_items',
    'pdf_history': 'search_pdf_history',
  };
  
  // 空状态消息
  static const Map<String, String> emptyMessages = {
    'no_documents': 'no_documents',
    'no_shipments': 'no_shipments',
    'no_items': 'no_items_to_check',
    'no_pdf_files': 'no_pdf_files',
    'no_search_results': 'no_search_results',
  };
  
  // 搜索结果统计格式
  static String getSearchResultsText(int count) {
    return 'search_results: $count 条结果';
  }
  
  // 搜索关键词显示格式
  static String getSearchKeywordText(String keyword) {
    return 'search_keyword: "$keyword"';
  }
}
