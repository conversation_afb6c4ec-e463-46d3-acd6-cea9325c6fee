# Common Path Configuration Script
# This script provides centralized path management for all deployment scripts

function Get-ProjectPaths {
    param(
        [Parameter(Mandatory=$false)]
        [string]$CustomBasePath = ""
    )
    
    # Determine base project path
    if ([string]::IsNullOrEmpty($CustomBasePath)) {
        # Use current directory if no custom path specified
        $basePath = Get-Location
        Write-Host "Using current directory as base path: $basePath" -ForegroundColor Yellow
    } else {
        $basePath = $CustomBasePath
        Write-Host "Using custom base path: $basePath" -ForegroundColor Yellow
    }
    
    # Define all project paths
    $paths = @{
        ProjectRoot = $basePath
        PDFOutput = Join-Path $basePath "PDF_Output"
        Logs = Join-Path $basePath "logs"
        Dist = Join-Path $basePath "dist"
        Scripts = Join-Path $basePath "scripts"
        NodeModules = Join-Path $basePath "node_modules"
        Src = Join-Path $basePath "src"
    }
    
    # Display paths for verification
    Write-Host "`nProject Paths Configuration:" -ForegroundColor Cyan
    foreach ($key in $paths.Keys) {
        Write-Host "  $key : $($paths[$key])" -ForegroundColor Gray
    }
    
    return $paths
}

function Test-ProjectStructure {
    param(
        [hashtable]$Paths
    )
    
    Write-Host "`nValidating project structure..." -ForegroundColor Cyan
    
    $requiredFiles = @(
        "package.json",
        "tsconfig.json",
        "nest-cli.json"
    )
    
    $requiredDirs = @(
        "src"
    )
    
    $allValid = $true
    
    # Check required files
    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $Paths.ProjectRoot $file
        if (Test-Path $filePath) {
            Write-Host "  OK Found: $file" -ForegroundColor Green
        } else {
            Write-Host "  ERROR Missing: $file" -ForegroundColor Red
            $allValid = $false
        }
    }
    
    # Check required directories
    foreach ($dir in $requiredDirs) {
        $dirPath = Join-Path $Paths.ProjectRoot $dir
        if (Test-Path $dirPath) {
            Write-Host "  OK Found: $dir/" -ForegroundColor Green
        } else {
            Write-Host "  ERROR Missing: $dir/" -ForegroundColor Red
            $allValid = $false
        }
    }
    
    if ($allValid) {
        Write-Host "  Project structure validation passed" -ForegroundColor Green
        return $true
    } else {
        Write-Host "  Project structure validation failed" -ForegroundColor Red
        Write-Host "  Make sure you're running this script from the NestJS project root directory" -ForegroundColor Yellow
        return $false
    }
}

function Initialize-ProjectDirectories {
    param(
        [hashtable]$Paths
    )
    
    Write-Host "`nInitializing project directories..." -ForegroundColor Cyan
    
    $dirsToCreate = @(
        $Paths.PDFOutput,
        $Paths.Logs
    )
    
    foreach ($dir in $dirsToCreate) {
        if (!(Test-Path $dir)) {
            try {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
                Write-Host "  OK Created: $dir" -ForegroundColor Green
            } catch {
                Write-Host "  ERROR Failed to create: $dir - $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "  OK Exists: $dir" -ForegroundColor Yellow
        }
    }
}

# Functions are automatically available when script is dot-sourced
# No need for Export-ModuleMember in script files
