# WhatsApp PDF分享功能

## 功能概述

本功能允许用户通过WhatsApp分享PDF订单文档。系统会自动获取Firebase Storage中的PDF下载链接，并通过WhatsApp发送给客户。

## 实现的功能

### 1. 核心组件

- **WhatsAppDialog**: 用于输入和确认WhatsApp分享信息的对话框
- **WhatsAppService**: 处理WhatsApp URL构建和消息发送的服务类
- **PdfShareOptionsDialog**: 集成了邮件和WhatsApp分享选项的统一对话框

### 2. 业务流程

1. **PDF生成和上传**: 
   - 系统生成PDF文档
   - 自动上传到Firebase Storage
   - 保存下载链接到数据库

2. **WhatsApp分享流程**:
   - 用户点击"发送到WhatsApp"按钮
   - 系统获取Firebase下载链接
   - 显示WhatsApp确认对话框
   - 用户确认手机号码和消息内容
   - 系统打开WhatsApp应用并预填充消息

### 3. 技术特性

- **手机号码格式化**: 自动添加马来西亚国家代码(+60)
- **URL编码**: 正确编码消息内容以确保WhatsApp兼容性
- **错误处理**: 处理WhatsApp未安装、网络错误等情况
- **用户体验**: 提供清晰的加载状态和错误提示
- **国际化支持**: 完整支持中文、英文、马来文三种语言

## 使用的页面

### 1. To Ship Tab (待出货页面)
- 在PDF选项对话框中添加了WhatsApp选项
- 使用新的`PdfShareOptionsDialog`统一处理邮件和WhatsApp分享

### 2. To Delivery Tab (待送达页面)  
- 在签名完成后的分享选项中包含WhatsApp功能
- 支持司机直接通过WhatsApp发送PDF给客户

### 3. PDF History Screen (PDF历史页面)
- 在PDF操作选项中添加WhatsApp分享功能
- 允许用户重新分享历史PDF文档

## 消息模板

系统使用国际化的消息模板，支持中文、英文、马来文三种语言：

**中文模板：**
```
您好！这是您的订单 {documentNo} 的PDF文档链接：

{pdfUrl}

请点击链接查看或下载您的订单详情。

谢谢！
```

**英文模板：**
```
Hello! Here is the PDF document link for your order {documentNo}:

{pdfUrl}

Please click the link to view or download your order details.

Thank you!
```

**马来文模板：**
```
Hello! Ini adalah pautan dokumen PDF untuk pesanan anda {documentNo}:

{pdfUrl}

Sila klik pautan untuk melihat atau memuat turun butiran pesanan anda.

Terima kasih!
```

## 技术实现细节

### 1. WhatsApp URL格式
```
https://wa.me/{phone_number}?text={encoded_message}
```

### 2. 手机号码处理
- 移除前导0
- 添加+60国家代码（马来西亚）
- 验证格式：`+\d{10,15}`

### 3. Firebase集成
- 使用后端API获取下载链接：`/pdf/firebase-download-url`
- 确保PDF已上传到Firebase Storage
- 提供友好的错误提示

### 4. 错误处理
- WhatsApp未安装：显示下载提示对话框
- PDF未上传：提示用户PDF尚未准备就绪
- 网络错误：显示具体错误信息

### 5. 国际化键值
系统添加了以下国际化键值，支持中文、英文、马来文：

**WhatsApp功能相关：**
- `send_to_whatsapp`: 发送到WhatsApp
- `phone_number`: 手机号码
- `please_enter_phone_number`: 请输入手机号码
- `please_enter_valid_phone_format`: 请输入有效的手机号码格式
- `message_preview`: 将要发送的消息预览
- `whatsapp_not_installed`: WhatsApp未安装
- `whatsapp_not_installed_message`: WhatsApp未安装提示消息
- `got_it`: 知道了
- `download_whatsapp`: 下载WhatsApp
- `whatsapp`: WhatsApp
- `getting_pdf_download_link`: 获取PDF下载链接
- `pdf_not_uploaded_to_cloud`: PDF文件尚未上传到云端
- `whatsapp_opened_complete_sending`: 已打开WhatsApp，请完成消息发送
- `whatsapp_share_failed`: WhatsApp分享失败
- `whatsapp_message_template`: WhatsApp消息模板

## 依赖项

- `url_launcher: ^6.2.5`: 用于打开WhatsApp应用
- Firebase Storage: 用于存储和获取PDF文件
- 现有的PDF生成和邮件发送功能

## 配置要求

1. **Android权限** (android/app/src/main/AndroidManifest.xml):
```xml
<uses-permission android:name="android.permission.INTERNET" />
<queries>
  <intent>
    <action android:name="android.intent.action.VIEW" />
    <data android:scheme="https" />
  </intent>
</queries>
```

2. **iOS配置** (ios/Runner/Info.plist):
```xml
<key>LSApplicationQueriesSchemes</key>
<array>
  <string>https</string>
  <string>whatsapp</string>
</array>
```

## 使用说明

### 对于用户：
1. 在相关页面点击PDF选项
2. 选择"发送到WhatsApp"
3. 确认或修改客户手机号码
4. 预览消息内容
5. 点击"发送到WhatsApp"
6. 在WhatsApp中完成消息发送

### 对于开发者：
1. 确保Firebase Storage配置正确
2. 验证PDF上传功能正常工作
3. 测试不同的手机号码格式
4. 处理各种错误情况

## 测试建议

1. **功能测试**:
   - 测试有效和无效的手机号码格式
   - 测试WhatsApp已安装和未安装的情况
   - 测试网络连接问题

2. **用户体验测试**:
   - 验证加载状态显示
   - 确认错误消息清晰易懂
   - 测试取消操作的处理

3. **集成测试**:
   - 验证与现有PDF生成流程的集成
   - 测试与邮件发送功能的协同工作
   - 确认数据库操作正确执行
