{"name": "backend_nestjs", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "pm2:start": "powershell -ExecutionPolicy Bypass -File scripts/start-pm2.ps1", "pm2:dev": "powershell -ExecutionPolicy Bypass -File scripts/setup-environment.ps1 -Environment development", "pm2:staging": "powershell -ExecutionPolicy Bypass -File scripts/setup-environment.ps1 -Environment staging", "pm2:prod": "powershell -ExecutionPolicy Bypass -File scripts/setup-environment.ps1 -Environment production", "deploy:windows": "powershell -ExecutionPolicy Bypass -File scripts/deploy-windows-production.ps1", "pm2:stop": "pm2 stop backend-nestjs", "pm2:restart": "pm2 restart backend-nestjs", "pm2:delete": "pm2 delete backend-nestjs", "pm2:logs": "pm2 logs backend-nestjs", "pm2:status": "pm2 status", "diagnose": "powershell -ExecutionPolicy Bypass -File scripts/diagnose-pdf.ps1", "test:pdf": "powershell -ExecutionPolicy Bypass -File scripts/test-pdf-fix.ps1", "check:config": "powershell -ExecutionPolicy Bypass -File scripts/check-config.ps1", "check:npm": "powershell -ExecutionPolicy Bypass -File scripts/check-config.ps1 -Mode npm", "check:pm2": "powershell -ExecutionPolicy Bypass -File scripts/check-config.ps1 -Mode pm2-dev", "test:config": "powershell -ExecutionPolicy Bypass -File scripts/test-config.ps1", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^8.1.1", "@nestjs/typeorm": "^11.0.0", "@types/fs-extra": "^11.0.4", "axios": "^1.9.0", "bcrypt": "^5.1.1", "firebase-admin": "^13.4.0", "fs-extra": "^11.3.0", "handlebars": "^4.7.8", "mssql": "^11.0.1", "pdfkit": "^0.17.0", "pg": "^8.14.1", "puppeteer": "^24.8.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}