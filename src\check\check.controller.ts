import { <PERSON>, Post, Get, BadRequestException, NotFoundException, ForbiddenException, HttpCode, Query, Body } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { CheckService } from './check.service';

@ApiTags('Check')
@Controller('check')
export class CheckController {
  constructor(
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    private readonly checkService: CheckService,
  ) { }

  @ApiOperation({ summary: '员工检查货物' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（支持D00001/1格式）' })
  @ApiQuery({ name: 'line', description: '明细行号' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '成功标记为员工已检查'
  })
  @HttpCode(200)
  @Post('staff')
  async staffCheck(
    @Query('documentNo') documentNo: string,
    @Query('line') line: number,
    @Query('staffId') staffId: number
  ) {
    // 使用 CheckService 处理员工检查
    return this.checkService.checkByStaff(
      staffId,
      documentNo,
      line
    );
  }

  @ApiOperation({ summary: '主管检查货物' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（支持D00001/1格式）' })
  @ApiQuery({ name: 'line', description: '明细行号' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiResponse({
    status: 200,
    description: '成功标记为主管已检查'
  })
  @HttpCode(200)
  @Post('supervisor')
  async supervisorCheck(
    @Query('documentNo') documentNo: string,
    @Query('line') line: number,
    @Query('supervisorId') supervisorId: number
  ) {
    // 使用 CheckService 处理主管检查
    return this.checkService.checkBySupervisor(
      supervisorId,
      documentNo,
      line
    );
  }

  @ApiOperation({ summary: '批量主管检查货物' })
  @ApiResponse({
    status: 200,
    description: '成功批量标记为主管已检查'
  })
  @HttpCode(200)
  @Post('supervisor/batch')
  async batchSupervisorCheck(
    @Body() body: {
      supervisorId: number;
      items: Array<{ documentNo: string; line: number }>;
    }
  ) {
    return this.checkService.batchCheckBySupervisor(
      body.supervisorId,
      body.items
    );
  }

  @ApiOperation({ summary: 'BOM专员检查货物' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（支持D00001/1格式）' })
  @ApiQuery({ name: 'line', description: '明细行号' })
  @ApiQuery({ name: 'bomSpecialistId', description: 'BOM专员ID' })
  @ApiResponse({
    status: 200,
    description: '成功标记为BOM专员已检查'
  })
  @HttpCode(200)
  @Post('bom')
  async bomSpecialistCheck(
    @Query('documentNo') documentNo: string,
    @Query('line') line: number,
    @Query('bomSpecialistId') bomSpecialistId: number
  ) {
    // 使用 CheckService 处理BOM专员检查
    return this.checkService.checkByBomSpecialist(
      bomSpecialistId,
      documentNo,
      line
    );
  }

  @ApiOperation({ summary: '获取待检查货物' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '返回所有未经全部检查的货物'
  })
  @HttpCode(200)
  @Post('pending')
  async getPendingItems(@Query('staffId') staffId: number) {
    // 使用 CheckService 获取待检查列表
    return this.checkService.getPendingChecks(staffId);
  }

  @ApiOperation({ summary: '获取员工待检查列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '返回该员工可以检查的货物列表（基于楼层权限）',
    schema: {
      type: 'object',
      properties: {
        pendingStaffCheck: {
          type: 'array',
          description: '待员工检查的货物列表',
          items: {
            type: 'object',
            properties: {
              document_no: { type: 'string', example: 'DO-2024-001' },
              line: { type: 'number', example: 1 },
              stock: { type: 'string', example: 'ITEM001' },
              description: { type: 'string', example: '测试商品' },
              quantity: { type: 'number', example: 10 },
              bin_shelf_no: { type: 'string', example: '2-1-G001-1' },
              parent_code: { type: 'string', example: 'BOM001', nullable: true },
              staff_checked: { type: 'boolean', example: false },
              staff_checked_at: { type: 'string', format: 'date-time', example: null },
              staff_id: { type: 'number', example: null },
              bom_specialist_checked: { type: 'boolean', example: false },
              bom_specialist_checked_at: { type: 'string', format: 'date-time', example: null },
              bom_specialist_id: { type: 'number', example: null },
              supervisor_checked: { type: 'boolean', example: false },
              supervisor_checked_at: { type: 'string', format: 'date-time', example: null },
              supervisor_id: { type: 'number', example: null }
            }
          }
        },
        pendingSupervisorCheck: {
          type: 'array',
          description: '待主管确认的货物列表',
          items: {
            type: 'object',
            properties: {
              document_no: { type: 'string', example: 'DO-2024-001' },
              line: { type: 'number', example: 1 },
              stock: { type: 'string', example: 'ITEM001' },
              description: { type: 'string', example: '测试商品' },
              quantity: { type: 'number', example: 10 },
              bin_shelf_no: { type: 'string', example: '2-1-G001-1' },
              parent_code: { type: 'string', example: 'BOM001', nullable: true },
              staff_checked: { type: 'boolean', example: true },
              staff_checked_at: { type: 'string', format: 'date-time', example: '2024-02-20T10:30:00Z' },
              staff_id: { type: 'number', example: 1 },
              bom_specialist_checked: { type: 'boolean', example: true },
              bom_specialist_checked_at: { type: 'string', format: 'date-time', example: '2024-02-20T11:30:00Z' },
              bom_specialist_id: { type: 'number', example: 3 },
              supervisor_checked: { type: 'boolean', example: false },
              supervisor_checked_at: { type: 'string', format: 'date-time', example: null },
              supervisor_id: { type: 'number', example: null }
            }
          }
        },
        waitingList: {
          type: 'array',
          description: '等待普通员工检查的BOM物品列表（仅BOM专员可见）',
          items: {
            type: 'object',
            properties: {
              document_no: { type: 'string', example: 'DO-2024-001' },
              line: { type: 'number', example: 1 },
              stock: { type: 'string', example: 'ITEM001' },
              description: { type: 'string', example: '测试商品' },
              quantity: { type: 'number', example: 10 },
              bin_shelf_no: { type: 'string', example: '2-1-G001-1' },
              parent_code: { type: 'string', example: 'BOM001', nullable: true },
              staff_checked: { type: 'boolean', example: false },
              staff_checked_at: { type: 'string', format: 'date-time', example: null },
              staff_id: { type: 'number', example: null },
              bom_specialist_checked: { type: 'boolean', example: false },
              bom_specialist_checked_at: { type: 'string', format: 'date-time', example: null },
              bom_specialist_id: { type: 'number', example: null },
              supervisor_checked: { type: 'boolean', example: false },
              supervisor_checked_at: { type: 'string', format: 'date-time', example: null },
              supervisor_id: { type: 'number', example: null }
            }
          }
        },
        total: {
          type: 'number',
          description: '待检查货物总数',
          example: 5
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '员工不存在'
  })
  @Get('list')
  async getStaffPendingList(
    @Query('staffId') staffId: number
  ) {
    return this.checkService.getPendingChecks(staffId);
  }

  @ApiOperation({ summary: '获取员工待检查列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiQuery({ name: 'search', description: '搜索关键词', required: false })
  @ApiResponse({
    status: 200,
    description: '返回员工待检查货物列表',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          properties: {
            pendingRegularCheck: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  document_no: { type: 'string', example: 'D00001' },
                  line: { type: 'number', example: 1 },
                  stock: { type: 'string', example: 'ITEM001' },
                  description: { type: 'string', example: '测试货物' },
                  quantity: { type: 'number', example: 10 },
                  bin_shelf_no: { type: 'string', example: '2-1-G001-1' },
                  staff_checked: { type: 'boolean', example: false },
                  bom_specialist_checked: { type: 'boolean', example: false },
                  supervisor_checked: { type: 'boolean', example: false },
                  document_date: { type: 'string', format: 'date-time' },
                  customer: { type: 'string', example: 'CUST001' },
                  customer_name: { type: 'string', example: '测试客户' },
                  priority: { type: 'number', example: 0 }
                }
              }
            },
            pendingBomCheck: {
              type: 'array',
              items: {
                type: 'object',
                description: 'BOM子项待检查列表'
              }
            },
            pendingSupervisorCheck: {
              type: 'array',
              items: {
                type: 'object',
                description: '待主管确认列表'
              }
            },
            waitingList: {
              type: 'array',
              items: {
                type: 'object',
                description: '等待列表（BOM专员和主管可见）'
              }
            },
            total: { type: 'number', description: '当前页面项目总数' }
          }
        },
        total: { type: 'number', description: '项目总数' },
        hasMore: { type: 'boolean', description: '固定为false' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '员工不存在'
  })
  @Get('list-paginated')
  async getStaffPendingListPaginated(
    @Query('staffId') staffId: number,
    @Query('search') searchQuery?: string
  ) {
    return this.checkService.getPendingChecksPaginated(staffId, 1, 10, searchQuery);
  }

  @ApiOperation({ summary: '获取普通员工待检查列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiQuery({ name: 'search', description: '搜索关键词', required: false })
  @ApiResponse({
    status: 200,
    description: '返回普通员工待检查货物列表',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          properties: {
            pendingRegularCheck: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  document_no: { type: 'string', example: 'D00001' },
                  line: { type: 'number', example: 1 },
                  stock: { type: 'string', example: 'ITEM001' },
                  description: { type: 'string', example: '测试货物' },
                  quantity: { type: 'number', example: 10 },
                  bin_shelf_no: { type: 'string', example: '2-1-G001-1' },
                  staff_checked: { type: 'boolean', example: false },
                  bom_specialist_checked: { type: 'boolean', example: false },
                  supervisor_checked: { type: 'boolean', example: false },
                  document_date: { type: 'string', format: 'date-time' },
                  customer: { type: 'string', example: 'CUST001' },
                  customer_name: { type: 'string', example: '测试客户' },
                  priority: { type: 'number', example: 0 }
                }
              }
            },
            total: { type: 'number', description: '当前页面项目总数' }
          }
        },
        total: { type: 'number', description: '项目总数' },
        hasMore: { type: 'boolean', description: '固定为false' }
      }
    }
  })
  @Get('regular-paginated')
  async getRegularChecksPaginated(
    @Query('staffId') staffId: number,
    @Query('search') searchQuery?: string
  ) {
    return this.checkService.getPendingRegularChecksPaginated(staffId, 1, 10, searchQuery);
  }

  @ApiOperation({ summary: '获取BOM待检查列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiQuery({ name: 'search', description: '搜索关键词', required: false })
  @ApiResponse({
    status: 200,
    description: '返回BOM待检查货物列表',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          properties: {
            pendingBomCheck: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  document_no: { type: 'string', example: 'D00001' },
                  line: { type: 'number', example: 1 },
                  stock: { type: 'string', example: 'ITEM001' },
                  description: { type: 'string', example: '测试货物' },
                  quantity: { type: 'number', example: 10 },
                  bin_shelf_no: { type: 'string', example: '2-1-G001-1' },
                  staff_checked: { type: 'boolean', example: false },
                  bom_specialist_checked: { type: 'boolean', example: false },
                  supervisor_checked: { type: 'boolean', example: false },
                  document_date: { type: 'string', format: 'date-time' },
                  customer: { type: 'string', example: 'CUST001' },
                  customer_name: { type: 'string', example: '测试客户' },
                  priority: { type: 'number', example: 0 }
                }
              }
            },
            total: { type: 'number', description: '当前页面项目总数' }
          }
        },
        total: { type: 'number', description: '项目总数' },
        hasMore: { type: 'boolean', description: '固定为false' }
      }
    }
  })
  @Get('bom-paginated')
  async getBomChecksPaginated(
    @Query('staffId') staffId: number,
    @Query('search') searchQuery?: string
  ) {
    return this.checkService.getPendingBomChecksPaginated(staffId, 1, 10, searchQuery);
  }

  @ApiOperation({ summary: '获取指定楼层检查统计' })
  @ApiQuery({ name: 'floor', description: '楼层数字，如 2 代表2楼' })
  @ApiResponse({
    status: 200,
    description: '返回指定楼层的检查统计信息',
    schema: {
      type: 'object',
      properties: {
        pendingStaffCheck: { type: 'number', example: 5 },
        pendingSupervisorCheck: { type: 'number', example: 3 },
        totalPending: { type: 'number', example: 8 }
      }
    }
  })
  @Get('floor-stats')
  async getFloorStatistics(@Query('floor') floor: string) {
    return this.checkService.getFloorStats(floor);
  }

  @ApiOperation({ summary: '主管拒绝员工检查' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（支持D00001/1格式）' })
  @ApiQuery({ name: 'line', description: '明细行号' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiQuery({ name: 'reason', description: '拒绝原因', required: false })
  @ApiResponse({
    status: 200,
    description: '拒绝成功',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Staff check for item D00001-1 has been rejected by supervisor jane.smith' },
        detail: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            line: { type: 'number', example: 1 },
            staff_checked: { type: 'boolean', example: false },
            supervisor_checked: { type: 'boolean', example: false }
          }
        }
      }
    }
  })
  @Post('reject')
  async rejectStaffCheck(
    @Query('documentNo') documentNo: string,
    @Query('line') line: number,
    @Query('supervisorId') supervisorId: number,
    @Query('reason') reason?: string
  ) {
    return this.checkService.rejectStaffCheck(supervisorId, documentNo, line, reason || '');
  }

  @ApiOperation({ summary: '检查文档是否可以完成出货' })
  @ApiQuery({ name: 'documentNo', description: '文档编号（支持D00001格式）' })
  @ApiQuery({ name: 'staffId', description: '员工ID（主管或司机）' })
  @ApiResponse({
    status: 200,
    description: '文档可出货状态',
    schema: {
      properties: {
        ready: { type: 'boolean', example: true },
        unconfirmedItems: {
          type: 'array',
          items: { type: 'number' },
          example: [1, 3, 5],
          description: '未经主管或司机确认的项目行号'
        }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '无权访问此操作'
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在'
  })
  @Get('document-ready')
  async checkDocumentReady(
    @Query('documentNo') documentNo: string,
    @Query('staffId') staffId: number
  ) {
    // 检查用户是否存在并有权限
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    // 检查用户权限
    if (staff.level !== 'supervisor' && staff.level !== 'admin' && staff.level !== 'driver') {
      throw new ForbiddenException('Only supervisors or drivers can check if a document is ready for shipping');
    }

    return this.checkService.isDocumentReady(documentNo);
  }

  @ApiOperation({ summary: '获取所有未出货的文档列表（包括未准备好的）' })
  @ApiQuery({ name: 'staffId', description: '员工ID（主管或司机）' })
  @ApiQuery({ name: 'search', description: '搜索关键词', required: false })
  @ApiResponse({
    status: 200,
    description: '返回所有未出货文档列表，包含准备状态',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              document_no: { type: 'string', example: 'DO-2024-001' },
              document_date: { type: 'string', format: 'date-time', example: '2025-04-15T00:00:00.000Z' },
              customer: { type: 'string', example: 'CUST001' },
              customer_name: { type: 'string', example: '测试客户' },
              remarks: { type: 'string', example: '紧急订单' },
              is_shipped: { type: 'boolean', example: false },
              priority: { type: 'number', example: 0 },
              created_at: { type: 'string', format: 'date-time', example: '2025-04-15T08:00:00.000Z' },
              ready_to_ship: { type: 'boolean', example: true, description: '是否准备好出货' },
              unconfirmed_items: { type: 'array', items: { type: 'number' }, example: [1, 3], description: '未确认的项目行号' }
            }
          }
        },
        total: { type: 'number', example: 100 },
        hasMore: { type: 'boolean', example: false }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '无权访问此列表'
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在'
  })
  @Get('all-unshipped-documents')
  async getAllUnshippedDocuments(
    @Query('staffId') staffId: number,
    @Query('search') searchQuery?: string
  ) {
    return this.checkService.getAllUnshippedDocuments(staffId, 1, 10, searchQuery);
  }

  @ApiOperation({ summary: '获取 to_ship 页面专用文档列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID（主管或司机）' })
  @ApiResponse({
    status: 200,
    description: '返回可出货文档列表',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              document_no: { type: 'string', example: 'DO-2024-001' },
              document_date: { type: 'string', format: 'date-time', example: '2025-04-15T00:00:00.000Z' },
              customer: { type: 'string', example: 'CUST001' },
              customer_name: { type: 'string', example: '测试客户' },
              remarks: { type: 'string', example: '紧急订单' },
              is_shipped: { type: 'boolean', example: false },
              priority: { type: 'number', example: 0 },
              created_at: { type: 'string', format: 'date-time', example: '2025-04-15T08:00:00.000Z' }
            }
          }
        },
        total: { type: 'number', example: 100 },
        hasMore: { type: 'boolean', example: false }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '无权访问此列表'
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在'
  })
  @Get('to-ship-documents')
  async getToShipDocuments(
    @Query('staffId') staffId: number
  ) {
    return this.checkService.getToShipDocuments(staffId, 1, 10);
  }

  @ApiOperation({ summary: '获取可以完成出货的文档列表（旧版本，保留兼容性）' })
  @ApiQuery({ name: 'staffId', description: '员工ID（主管或司机）' })
  @ApiQuery({ name: 'page', description: '页码', required: false })
  @ApiQuery({ name: 'limit', description: '每页数量', required: false })
  @ApiResponse({
    status: 200,
    description: '分页返回可出货文档列表',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              document_no: { type: 'string', example: 'DO-2024-001' },
              document_date: { type: 'string', format: 'date-time', example: '2025-04-15T00:00:00.000Z' },
              customer: { type: 'string', example: 'CUST001' },
              customer_name: { type: 'string', example: '测试客户' },
              remarks: { type: 'string', example: '紧急订单' },
              is_shipped: { type: 'boolean', example: false },
            }
          }
        },
        total: { type: 'number', example: 100 },
        hasMore: { type: 'boolean', example: false }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '无权访问此列表'
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在'
  })
  @Get('ready-documents')
  async getReadyDocuments(
    @Query('staffId') staffId: number
  ) {
    return this.checkService.getReadyDocuments(staffId, 1, 10);
  }

  @ApiOperation({ summary: '标记文档为已出货' })
  @ApiQuery({ name: 'documentNo', description: '文档编号（支持D00001格式）' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiQuery({ name: 'driverId', description: '司机ID', required: false })
  @ApiResponse({
    status: 200,
    description: '文档已标记为已出货',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Document D00001 has been marked as shipped' },
        document: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            is_shipped: { type: 'boolean', example: true }
          }
        },
        pdfPath: { type: 'string', example: '/PDF_Output/2023-05-20/D00001.pdf', description: '如果没有指派司机，会生成PDF文件并返回路径' }
      }
    }
  })
  @Post('complete')
  async completeDocument(
    @Query('documentNo') documentNo: string,
    @Query('supervisorId') supervisorId: number,
    @Query('driverId') driverId?: number
  ) {
    return this.checkService.completeDocument(documentNo, supervisorId, driverId);
  }

  @ApiOperation({ summary: '获取司机待送达订单列表' })
  @ApiQuery({ name: 'driverId', description: '司机ID' })
  @ApiResponse({
    status: 200,
    description: '返回该司机待送达的订单列表',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          document_no: { type: 'string', example: 'DO-2024-001' },
          document_date: { type: 'string', format: 'date-time', example: '2025-04-15T00:00:00.000Z' },
          customer: { type: 'string', example: 'CUST001' },
          customer_name: { type: 'string', example: '测试客户' },
          remarks: { type: 'string', example: '紧急订单' },
          is_shipped: { type: 'boolean', example: true },
          shipped_at: { type: 'string', format: 'date-time', example: '2025-04-16T10:30:00.000Z' },
          shipped_by: { type: 'number', example: 3 },
          delivered: { type: 'boolean', example: false }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '司机不存在'
  })
  @Get('delivery-list')
  async getDriverDeliveryList(
    @Query('driverId') driverId: number
  ) {
    return this.checkService.getDriverDeliveryList(driverId);
  }

  @ApiOperation({ summary: '司机确认订单送达' })
  @ApiQuery({ name: 'documentNo', description: '文档编号（支持D00001格式）' })
  @ApiQuery({ name: 'driverId', description: '司机ID' })
  @ApiQuery({ name: 'notes', description: '送达备注', required: false })
  @ApiResponse({
    status: 200,
    description: '订单已确认送达',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Document D00001 has been confirmed as delivered' },
        document: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            delivered: { type: 'boolean', example: true },
            delivered_at: { type: 'string', format: 'date-time', example: '2025-04-16T15:30:00.000Z' },
            delivered_by: { type: 'number', example: 3 }
          }
        }
      }
    }
  })
  @Post('confirm-delivery')
  async confirmDelivery(
    @Query('documentNo') documentNo: string,
    @Query('driverId') driverId: number,
    @Query('notes') notes?: string
  ) {
    return this.checkService.confirmDelivery(documentNo, driverId, notes || '');
  }

  @ApiOperation({ summary: '获取BOM子项状态' })
  @ApiQuery({ name: 'bomGroup', description: 'BOM组标识（通常是父项的document_no）' })
  @ApiQuery({ name: 'parentStock', description: 'BOM父项的库存代码，用于精确过滤子项', required: false })
  @ApiResponse({
    status: 200,
    description: '返回指定BOM组的所有子项及其状态',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          document_no: { type: 'string', example: 'D00001/1' },
          line: { type: 'number', example: 1 },
          stock: { type: 'string', example: 'PART001' },
          description: { type: 'string', example: '零件1' },
          quantity: { type: 'number', example: 2 },
          parent_code: { type: 'string', example: 'BOM001' },
          staff_checked: { type: 'boolean', example: true },
          bom_specialist_checked: { type: 'boolean', example: false },
          supervisor_checked: { type: 'boolean', example: false },
          bom_group: { type: 'string', example: 'D00001' },
          is_bom_parent: { type: 'boolean', example: false }
        }
      }
    }
  })
  @Get('bom-children')
  async getBomChildrenStatus(
    @Query('bomGroup') bomGroup: string,
    @Query('parentStock') parentStock?: string,
    @Query('bomParentId') bomParentId?: string
  ) {
    // 将bomParentId从字符串转换为数字
    const bomParentIdNum = bomParentId ? parseInt(bomParentId, 10) : undefined;
    return this.checkService.getBomChildrenStatus(bomGroup, parentStock, bomParentIdNum);
  }

  @ApiOperation({ summary: '获取BOM子项详细信息用于逐个检查' })
  @ApiQuery({ name: 'bomGroup', description: 'BOM组标识' })
  @ApiQuery({ name: 'parentStock', description: 'BOM父项库存代码' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiResponse({
    status: 200,
    description: '返回BOM子项详细列表，支持逐个检查',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          document_no: { type: 'string', example: 'D00001/1' },
          line: { type: 'number', example: 1 },
          stock: { type: 'string', example: 'PART001' },
          description: { type: 'string', example: '零件1' },
          quantity: { type: 'number', example: 2 },
          bin_shelf_no: { type: 'string', example: '3-1-G001-1' },
          parent_code: { type: 'string', example: 'BOM001' },
          staff_checked: { type: 'boolean', example: true },
          bom_specialist_checked: { type: 'boolean', example: false },
          supervisor_checked: { type: 'boolean', example: false },
          have_stock: { type: 'boolean', example: true },
          skip_checks: { type: 'boolean', example: false, description: '是否跳过staff/staff_bom检查' }
        }
      }
    }
  })
  @Get('bom-children-details')
  async getBomChildrenDetails(
    @Query('bomGroup') bomGroup: string,
    @Query('parentStock') parentStock: string,
    @Query('supervisorId') supervisorId: number
  ) {
    return this.checkService.getBomChildrenDetails(bomGroup, parentStock, supervisorId);
  }

  @ApiOperation({ summary: '设置订单紧急状态（仅限主管）' })
  @ApiQuery({ name: 'documentNo', description: '文档编号' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiQuery({ name: 'priority', description: '优先级（0=普通，1=紧急）', required: false })
  @ApiResponse({
    status: 200,
    description: '成功设置订单优先级',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Order priority updated successfully' },
        document: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            priority: { type: 'number', example: 1 }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，仅限主管操作'
  })
  @ApiResponse({
    status: 404,
    description: '文档或用户不存在'
  })
  @Post('set-urgent')
  async setUrgentStatus(
    @Query('documentNo') documentNo: string,
    @Query('supervisorId') supervisorId: number,
    @Query('priority') priority: number = 1
  ) {
    return this.checkService.setUrgentStatus(documentNo, supervisorId, priority);
  }

  @ApiOperation({ summary: '修复错误标记的BOM父项数据' })
  @ApiResponse({
    status: 200,
    description: '修复完成',
    schema: {
      type: 'object',
      properties: {
        fixed: { type: 'number', example: 5, description: '修复的记录数' },
        total: { type: 'number', example: 10, description: '检查的记录总数' }
      }
    }
  })
  @Post('fix-bom-parent-data')
  async fixBomParentData() {
    return this.checkService.fixBomParentData();
  }

}