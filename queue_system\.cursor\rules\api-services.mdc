---
description:
globs:
alwaysApply: false
---
# API 服务

## 基础 API 服务 (ApiService)

应用使用 `ApiService` 作为与后端 API 通信的基础服务类，它封装了 Dio HTTP 客户端。

```dart
class ApiService {
  final Dio dio;
  final String baseUrl;
  
  ApiService({required this.baseUrl}) : dio = Dio(BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    responseType: ResponseType.json,
  ));
  
  Future<Map<String, dynamic>> get(String path, {Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await dio.get(path, queryParameters: queryParameters);
      return response.data;
    } catch (e) {
      // 异常处理逻辑
      throw _handleError(e);
    }
  }
  
  Future<Map<String, dynamic>> post(String path, {Map<String, dynamic>? data, Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await dio.post(path, data: data, queryParameters: queryParameters);
      return response.data;
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // 其他方法...
  
  Exception _handleError(dynamic error) {
    // 处理网络错误、服务器错误等
    return Exception('API请求失败: ${error.toString()}');
  }
}
```

## 认证服务 (AuthService)

```dart
class AuthService {
  final ApiService apiService;
  
  AuthService({required this.apiService});
  
  Future<AuthCredentialsModel> login(String username, String password) async {
    final response = await apiService.post('/staff/login', data: {
      'username': username,
      'password': password,
    });
    
    if (response['success'] == true) {
      return AuthCredentialsModel.fromJson(response['data']);
    } else {
      throw Exception(response['message'] ?? '登录失败');
    }
  }
  
  // 其他认证相关方法...
}
```

## 检查服务 (CheckService)

```dart
class CheckService {
  final ApiService apiService;
  
  CheckService({required this.apiService});
  
  Future<List<CheckItemModel>> getCheckList(int staffId) async {
    final response = await apiService.get('/check/list', queryParameters: {
      'staffId': staffId,
    });
    
    if (response['success'] == true) {
      final List<dynamic> items = response['data'];
      return items.map((item) => CheckItemModel.fromJson(item)).toList();
    } else {
      throw Exception(response['message'] ?? '获取检查列表失败');
    }
  }
  
  Future<CheckItemModel> staffCheck(String documentNo, int line, int staffId) async {
    final response = await apiService.post('/check/staff', queryParameters: {
      'documentNo': documentNo,
      'line': line,
      'staffId': staffId,
    });
    
    if (response['success'] == true) {
      return CheckItemModel.fromJson(response['detail']);
    } else {
      throw Exception(response['message'] ?? '员工检查失败');
    }
  }
  
  Future<CheckItemModel> supervisorCheck(String documentNo, int line, int supervisorId) async {
    final response = await apiService.post('/check/supervisor', queryParameters: {
      'documentNo': documentNo,
      'line': line,
      'supervisorId': supervisorId,
    });
    
    if (response['success'] == true) {
      return CheckItemModel.fromJson(response['detail']);
    } else {
      throw Exception(response['message'] ?? '主管检查失败');
    }
  }
  
  Future<bool> isDocumentReady(String documentNo, int staffId) async {
    final response = await apiService.get('/check/document-ready', queryParameters: {
      'documentNo': documentNo,
      'staffId': staffId,
    });
    
    if (response['success'] == true) {
      return response['ready'] == true;
    } else {
      throw Exception(response['message'] ?? '检查订单状态失败');
    }
  }
  
  // 其他检查相关方法...
}
```

## 订单出货服务 (ShippingService)

```dart
class ShippingService {
  final ApiService apiService;
  
  ShippingService({required this.apiService});
  
  Future<List<DeliveryOrderMainModel>> getShippingList(int staffId) async {
    final response = await apiService.get('/shipping/list', queryParameters: {
      'staffId': staffId,
    });
    
    if (response['success'] == true) {
      final List<dynamic> items = response['data'];
      return items.map((item) => DeliveryOrderMainModel.fromJson(item)).toList();
    } else {
      throw Exception(response['message'] ?? '获取待出货列表失败');
    }
  }
  
  Future<DeliveryOrderMainModel> shipDocument(String documentNo, int supervisorId, [int? driverId]) async {
    final Map<String, dynamic> queryParams = {
      'documentNo': documentNo,
      'supervisorId': supervisorId,
    };
    
    if (driverId != null) {
      queryParams['driverId'] = driverId;
    }
    
    final response = await apiService.post('/shipping/ship', queryParameters: queryParams);
    
    if (response['success'] == true) {
      return DeliveryOrderMainModel.fromJson(response['detail']);
    } else {
      throw Exception(response['message'] ?? '订单出货失败');
    }
  }
  
  Future<DeliveryOrderMainModel> confirmDelivery(String documentNo, int driverId, String? notes) async {
    final response = await apiService.post('/shipping/confirm-delivery', queryParameters: {
      'documentNo': documentNo,
      'driverId': driverId,
      if (notes != null) 'notes': notes,
    });
    
    if (response['success'] == true) {
      return DeliveryOrderMainModel.fromJson(response['detail']);
    } else {
      throw Exception(response['message'] ?? '确认送达失败');
    }
  }
  
  // 其他出货相关方法...
}
```

## 通知服务 (NotificationService)

```dart
class NotificationService {
  final ApiService apiService;
  
  NotificationService({required this.apiService});
  
  Future<List<NotificationModel>> getUnreadNotifications(int userId) async {
    final response = await apiService.get('/notification/unread/$userId');
    
    if (response['success'] == true) {
      final List<dynamic> items = response['data'];
      return items.map((item) => NotificationModel.fromJson(item)).toList();
    } else {
      throw Exception(response['message'] ?? '获取未读通知失败');
    }
  }
  
  Future<NotificationModel> markAsRead(int notificationId) async {
    final response = await apiService.post('/notification/read/$notificationId');
    
    if (response['success'] == true) {
      return NotificationModel.fromJson(response['detail']);
    } else {
      throw Exception(response['message'] ?? '标记通知为已读失败');
    }
  }
  
  // 其他通知相关方法...
}
```
