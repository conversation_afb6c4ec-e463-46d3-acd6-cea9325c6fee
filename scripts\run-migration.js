const { Client } = require('pg');
const fs = require('fs');
require('dotenv').config();

async function runMigration() {
  const client = new Client({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    user: process.env.POSTGRES_USERNAME || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DATABASE || 'postgres',
  });

  try {
    await client.connect();
    console.log('✅ 连接到PostgreSQL成功');

    // 检查表是否存在
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'delivery_order_detail'
      );
    `);

    if (!tableCheck.rows[0].exists) {
      console.log('❌ delivery_order_detail表不存在，请先运行NestJS应用以创建表结构');
      return;
    }

    // 检查字段是否已存在
    const fieldCheck = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'delivery_order_detail' 
      AND column_name = 'part_no';
    `);

    if (fieldCheck.rows.length > 0) {
      console.log('✅ part_no 字段已存在，跳过创建');
    } else {
      // 添加 part_no 字段
      await client.query(`
        ALTER TABLE delivery_order_detail 
        ADD COLUMN part_no VARCHAR(100);
      `);
      console.log('✅ part_no 字段添加成功');

      // 添加索引
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_delivery_order_detail_part_no 
        ON delivery_order_detail(part_no);
      `);
      console.log('✅ part_no 索引创建成功');

      // 添加注释
      await client.query(`
        COMMENT ON COLUMN delivery_order_detail.part_no IS '零件号，从MS SQL Server AR_DO_Detail_tbl表的Part No字段同步';
      `);
      console.log('✅ part_no 字段注释添加成功');
    }

    // 查看表结构确认
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'delivery_order_detail' 
      AND column_name IN ('description', 'part_no')
      ORDER BY ordinal_position;
    `);

    console.log('📋 相关字段信息:');
    columns.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
  } finally {
    await client.end();
  }
}

runMigration();
