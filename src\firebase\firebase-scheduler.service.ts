import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { FirebaseService } from './firebase.service';

@Injectable()
export class FirebaseSchedulerService {
  private readonly logger = new Logger(FirebaseSchedulerService.name);

  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 每月第一天凌晨2点执行自动清理
   * 删除超过3个月（90天）的PDF文件
   */
  @Cron('0 2 1 * *', {
    name: 'firebase-cleanup',
    timeZone: 'Asia/Shanghai', // 使用中国时区
  })
  async handleMonthlyCleanup() {
    const firebaseConfig = this.configService.get('firebase');
    
    // 检查是否启用自动清理
    const autoCleanupEnabled = firebaseConfig?.uploadConfig?.autoCleanupEnabled !== false;
    if (!autoCleanupEnabled) {
      this.logger.log('[定时清理] 自动清理功能已禁用，跳过执行');
      return;
    }

    // 检查Firebase服务状态
    const firebaseStatus = this.firebaseService.getStatus();
    if (!firebaseStatus.enabled || !firebaseStatus.initialized) {
      this.logger.warn('[定时清理] Firebase服务未就绪，跳过清理任务');
      return;
    }

    try {
      this.logger.log('[定时清理] 开始执行月度Firebase Storage清理任务');
      
      // 获取配置的清理天数，默认90天（3个月）
      const cleanupDays = firebaseConfig?.uploadConfig?.cleanupDays || 90;
      
      // 执行清理（非试运行）
      const result = await this.firebaseService.cleanupOldFiles(cleanupDays, false);
      
      if (result.success) {
        const savedSpaceMB = (result.savedSpace / 1024 / 1024).toFixed(2);
        this.logger.log(`[定时清理] 清理完成 - 删除${result.deletedFiles}个文件，节省${savedSpaceMB}MB空间`);
        
        // 如果有删除失败的文件，记录警告
        if (result.failedDeletes > 0) {
          this.logger.warn(`[定时清理] 有${result.failedDeletes}个文件删除失败`);
        }
      } else {
        this.logger.error(`[定时清理] 清理失败: ${result.error}`);
      }
    } catch (error) {
      this.logger.error(`[定时清理] 清理任务异常: ${error.message}`);
    }
  }

  /**
   * 每周日凌晨3点执行存储统计报告
   */
  @Cron('0 3 * * 0', {
    name: 'firebase-stats',
    timeZone: 'Asia/Shanghai',
  })
  async handleWeeklyStats() {
    const firebaseConfig = this.configService.get('firebase');
    
    // 检查是否启用统计报告
    const statsEnabled = firebaseConfig?.uploadConfig?.weeklyStatsEnabled !== false;
    if (!statsEnabled) {
      this.logger.log('[统计报告] 周度统计报告已禁用，跳过执行');
      return;
    }

    // 检查Firebase服务状态
    const firebaseStatus = this.firebaseService.getStatus();
    if (!firebaseStatus.enabled || !firebaseStatus.initialized) {
      this.logger.warn('[统计报告] Firebase服务未就绪，跳过统计任务');
      return;
    }

    try {
      this.logger.log('[统计报告] 开始生成Firebase Storage周度统计报告');
      
      const stats = await this.firebaseService.getStorageStats();
      
      if (stats.success) {
        const totalSizeMB = (stats.totalSize / 1024 / 1024).toFixed(2);
        
        this.logger.log(`[统计报告] Firebase Storage统计:`);
        this.logger.log(`  - 总文件数: ${stats.totalFiles}`);
        this.logger.log(`  - 总大小: ${totalSizeMB} MB`);
        
        if (stats.oldestFile) {
          this.logger.log(`  - 最老文件: ${stats.oldestFile.path} (${stats.oldestFile.created.toISOString()})`);
        }
        
        if (stats.newestFile) {
          this.logger.log(`  - 最新文件: ${stats.newestFile.path} (${stats.newestFile.created.toISOString()})`);
        }

        // 显示按月统计
        const sortedMonths = Object.keys(stats.filesByMonth).sort().reverse();
        this.logger.log(`  - 按月分布 (最近6个月):`);
        sortedMonths.slice(0, 6).forEach(month => {
          const monthData = stats.filesByMonth[month];
          const monthSizeMB = (monthData.size / 1024 / 1024).toFixed(2);
          this.logger.log(`    ${month}: ${monthData.count}个文件, ${monthSizeMB}MB`);
        });
      } else {
        this.logger.error(`[统计报告] 统计获取失败: ${stats.error}`);
      }
    } catch (error) {
      this.logger.error(`[统计报告] 统计任务异常: ${error.message}`);
    }
  }

  /**
   * 手动触发清理任务（用于测试）
   */
  async triggerManualCleanup(daysOld: number = 90, dryRun: boolean = true) {
    this.logger.log(`[手动清理] 触发手动清理任务，天数: ${daysOld}, 试运行: ${dryRun}`);
    
    try {
      const result = await this.firebaseService.cleanupOldFiles(daysOld, dryRun);
      
      if (result.success) {
        const savedSpaceMB = (result.savedSpace / 1024 / 1024).toFixed(2);
        this.logger.log(`[手动清理] 清理${dryRun ? '试运行' : ''}完成 - ${dryRun ? '预计' : '实际'}删除${result.deletedFiles}个文件，${dryRun ? '预计' : '实际'}节省${savedSpaceMB}MB空间`);
      } else {
        this.logger.error(`[手动清理] 清理失败: ${result.error}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`[手动清理] 清理任务异常: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取定时任务状态
   */
  getSchedulerStatus() {
    const firebaseConfig = this.configService.get('firebase');
    
    return {
      autoCleanupEnabled: firebaseConfig?.uploadConfig?.autoCleanupEnabled !== false,
      weeklyStatsEnabled: firebaseConfig?.uploadConfig?.weeklyStatsEnabled !== false,
      cleanupDays: firebaseConfig?.uploadConfig?.cleanupDays || 90,
      cleanupSchedule: '每月1日凌晨2点',
      statsSchedule: '每周日凌晨3点',
      timezone: 'Asia/Shanghai',
    };
  }
}
