-- 添加Firebase相关字段到delivery_order_main表
-- 请在PostgreSQL数据库中执行此脚本

-- 检查表是否存在
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'delivery_order_main';

-- 添加Firebase下载链接字段
ALTER TABLE delivery_order_main 
ADD COLUMN IF NOT EXISTS firebase_download_url TEXT;

-- 添加Firebase文件路径字段
ALTER TABLE delivery_order_main 
ADD COLUMN IF NOT EXISTS firebase_file_path VARCHAR(500);

-- 添加Firebase上传时间字段
ALTER TABLE delivery_order_main 
ADD COLUMN IF NOT EXISTS firebase_uploaded_at TIMESTAMP;

-- 添加字段注释
COMMENT ON COLUMN delivery_order_main.firebase_download_url IS 'Firebase Storage下载链接';
COMMENT ON COLUMN delivery_order_main.firebase_file_path IS 'Firebase Storage文件路径';
COMMENT ON COLUMN delivery_order_main.firebase_uploaded_at IS 'Firebase上传时间';

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'delivery_order_main' 
AND column_name LIKE 'firebase%'
ORDER BY column_name;
