import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/document.dart';
import 'package:queue_system/services/api_service.dart';

class DocumentProvider with ChangeNotifier {
  final ApiService _apiService;

  List<Document> _documents = [];
  bool _isLoading = false;
  bool _hasError = false;
  DateTime? _lastSyncTime;
  Timer? _syncTimer;

  List<Document> get documents => _documents;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String? get error => _hasError ? ApiErrorCode.connectionFailed : null;
  DateTime? get lastSyncTime => _lastSyncTime;

  DocumentProvider({required String baseUrl})
      : _apiService = ApiService(baseUrl: baseUrl) {
    // 初始加载数据
    loadDocuments();

    // 设置30分钟定时同步
    _syncTimer = Timer.periodic(const Duration(minutes: 30), (_) => syncData());
  }

  @override
  void dispose() {
    _syncTimer?.cancel();
    super.dispose();
  }

  // 加载文档列表
  Future<void> loadDocuments() async {
    _isLoading = true;
    _hasError = false;
    notifyListeners();

    try {
      _documents = await _apiService.getDocumentsWithDetails();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _hasError = true;
      notifyListeners();
    }
  }

  // 手动触发同步
  Future<void> syncData() async {
    _isLoading = true;
    _hasError = false;
    notifyListeners();

    try {
      await _apiService.triggerSync();
      await loadDocuments();
      _lastSyncTime = DateTime.now();
    } catch (e) {
      _hasError = true;
      _isLoading = false;
      notifyListeners();
    }
  }

  // 显示一个简单的错误提示
  void showErrorSnackBar(BuildContext context) {
    if (_hasError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.t('connection_server_failed')),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
