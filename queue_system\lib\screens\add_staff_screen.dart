import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/auth_provider.dart';

class AddStaffScreen extends StatefulWidget {
  const AddStaffScreen({super.key});

  @override
  State<AddStaffScreen> createState() => _AddStaffScreenState();
}

class _AddStaffScreenState extends State<AddStaffScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _fullNameController = TextEditingController();

  String _selectedLevel = 'regular';
  String _selectedFloor = '1F';
  bool _showFloorSelection = true;

  final List<String> _levels = [
    'regular',
    'supervisor',
    'admin',
    'staff_bom',
    'driver'
  ];
  final List<String> _floors = ['1F', '2F', '3F'];

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _fullNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('add_staff')),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _usernameController,
                decoration: InputDecoration(
                  labelText: context.t('username'),
                  border: const OutlineInputBorder(),
                  hintText: context.t('enter_username_no_spaces'),
                ),
                // 在输入时就过滤空格
                inputFormatters: [
                  FilteringTextInputFormatter.deny(RegExp(r'\s')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.t('please_enter_username');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: context.t('password'),
                  border: const OutlineInputBorder(),
                  hintText: context.t('enter_password_no_spaces'),
                ),
                obscureText: true,
                // 在输入时就过滤空格
                inputFormatters: [
                  FilteringTextInputFormatter.deny(RegExp(r'\s')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.t('please_enter_password');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _fullNameController,
                decoration: InputDecoration(
                  labelText: context.t('fullname'),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.t('please_fill_required');
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: context.t('staff_level'),
                  border: const OutlineInputBorder(),
                ),
                value: _selectedLevel,
                items: _levels.map((level) {
                  return DropdownMenuItem(
                    value: level,
                    child: Text(level == 'supervisor'
                        ? context.t('supervisor')
                        : level == 'admin'
                            ? context.t('admin')
                            : level == 'staff_bom'
                                ? context.t('bom_specialist')
                                : level == 'driver'
                                    ? context.t('driver')
                                    : context.t('regular_staff')),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedLevel = value!;
                    // 如果是主管/管理员/BOM专员/司机，不显示楼层选择，自动设为ALL
                    _showFloorSelection = value == 'regular';
                    if (!_showFloorSelection) {
                      _selectedFloor = 'ALL';
                    } else if (_selectedFloor == 'ALL') {
                      _selectedFloor = '1F'; // 如果切回普通员工，恢复默认楼层
                    }
                  });
                },
              ),
              if (_showFloorSelection) ...[
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: context.t('floor'),
                    border: const OutlineInputBorder(),
                  ),
                  value: _selectedFloor,
                  items: _floors.map((floor) {
                    return DropdownMenuItem(
                      value: floor,
                      child: Text(floor),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFloor = value!;
                    });
                  },
                ),
              ],
              const SizedBox(height: 24),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  if (authProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (authProvider.registerError != null) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(authProvider.registerError!),
                          backgroundColor: Colors.red,
                        ),
                      );
                      authProvider.clearRegisterError();
                    });
                  }

                  if (authProvider.registerSuccess) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(context.t('staff_created_success')),
                          backgroundColor: Colors.green,
                        ),
                      );
                      authProvider.clearRegisterSuccess();
                      Navigator.of(context).pop();
                    });
                  }

                  return ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        authProvider.register(
                          username: _usernameController.text,
                          password: _passwordController.text,
                          level: _selectedLevel,
                          floor: _selectedFloor,
                          fullName: _fullNameController.text,
                        );
                      }
                    },
                    child: Text(context.t('create_staff')),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
