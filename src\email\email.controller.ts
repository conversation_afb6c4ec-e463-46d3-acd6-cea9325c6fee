import { Controller, Post, Body, Query, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiBody, ApiResponse } from '@nestjs/swagger';
import { EmailService } from './email.service';

@ApiTags('email')
@Controller('email')
export class EmailController {
  constructor(private readonly emailService: EmailService) { }

  @Post('send-pdf')
  @ApiOperation({ summary: '发送PDF文件到指定邮箱' })
  @ApiQuery({ name: 'documentNo', description: '文档编号' })
  @ApiQuery({ name: 'date', description: '日期 (YYYY-MM-DD格式)', required: false })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        to: { type: 'string', description: '收件人邮箱' },
        subject: { type: 'string', description: '邮件主题', default: 'Delivery Order PDF' },
        text: { type: 'string', description: '邮件正文', default: 'Please find the attached delivery order PDF.' },
      },
      required: ['to'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '邮件发送成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async sendPdf(
    @Query('documentNo') documentNo: string,
    @Query('date') date: string,
    @Body() body: { to: string; subject?: string; text?: string },
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    if (!body.to) {
      throw new BadRequestException('Recipient email is required');
    }

    console.log(`Email API called with documentNo: ${documentNo}, date: ${date}, to: ${body.to}`);

    const subject = body.subject || `Delivery Order: ${documentNo}`;
    const text = body.text || `Please find the attached delivery order PDF for document ${documentNo}.`;

    try {
      const result = await this.emailService.sendPdfEmailByDocumentNo(
        body.to,
        subject,
        text,
        documentNo,
        date,
      );

      console.log(`Email sending result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      console.error(`Error sending email: ${error.message}`);
      throw error;
    }
  }
}
