# Environment Configuration Setup Script
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("development", "staging", "production")]
    [string]$Environment
)

Write-Host "=== Environment Configuration Setup: $Environment ===" -ForegroundColor Green

# Use current directory as project root
$projectRoot = Get-Location
Write-Host "Using project root: $projectRoot" -ForegroundColor Yellow

# Select configuration file based on environment
$configFile = switch ($Environment) {
    "development" { "ecosystem.config.js" }
    "staging" { "ecosystem.staging.config.js" }
    "production" { "ecosystem.production.config.js" }
}

Write-Host "`n1. Check configuration file" -ForegroundColor Cyan
if (Test-Path $configFile) {
    Write-Host "OK Found configuration file: $configFile" -ForegroundColor Green
} else {
    Write-Host "ERROR Configuration file not found: $configFile" -ForegroundColor Red
    Write-Host "Please create configuration file based on ecosystem.template.config.js" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n2. Validate configuration file content" -ForegroundColor Cyan
$configContent = Get-Content $configFile -Raw
$placeholders = @('{ENVIRONMENT}', '{PROJECT_ROOT_PATH}', '{SQL_SERVER_HOST}', '{SQL_USERNAME}', '{SQL_PASSWORD}')
$hasPlaceholders = $false

foreach ($placeholder in $placeholders) {
    if ($configContent -like "*$placeholder*") {
        Write-Host "ERROR Found unreplaced placeholder: $placeholder" -ForegroundColor Red
        $hasPlaceholders = $true
    }
}

if ($hasPlaceholders) {
    Write-Host "Please complete the configuration file setup first" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "OK Configuration file validation passed" -ForegroundColor Green
}

Write-Host "`n3. Create necessary directories" -ForegroundColor Cyan
$directories = @("PDF_Output", "logs")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "OK Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "OK Directory exists: $dir" -ForegroundColor Yellow
    }
}

Write-Host "`n4. Check project build" -ForegroundColor Cyan
if (!(Test-Path "dist")) {
    Write-Host "Starting project build..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK Project build completed" -ForegroundColor Green
    } else {
        Write-Host "ERROR Project build failed" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "OK dist directory exists" -ForegroundColor Green
}

Write-Host "`n5. Stop existing processes" -ForegroundColor Cyan
$processName = "backend-nestjs-$Environment"
pm2 stop $processName 2>$null
pm2 delete $processName 2>$null
Write-Host "OK Cleanup completed" -ForegroundColor Green

Write-Host "`n6. Start new process" -ForegroundColor Cyan
pm2 start $configFile
if ($LASTEXITCODE -eq 0) {
    Write-Host "OK Process started successfully" -ForegroundColor Green

    # Wait a few seconds for the process to fully start
    Start-Sleep -Seconds 3

    # Show status
    pm2 status

    Write-Host "`n=== Deployment Complete ===" -ForegroundColor Green
    Write-Host "Environment: $Environment" -ForegroundColor Cyan
    Write-Host "Configuration file: $configFile" -ForegroundColor Cyan
    Write-Host "Process name: $processName" -ForegroundColor Cyan

    Write-Host "`nManagement commands:" -ForegroundColor Yellow
    Write-Host "  Check status: pm2 status"
    Write-Host "  View logs: pm2 logs $processName"
    Write-Host "  Restart service: pm2 restart $processName"
    Write-Host "  Stop service: pm2 stop $processName"

} else {
    Write-Host "ERROR Process startup failed" -ForegroundColor Red
    Write-Host "Please check configuration files and logs" -ForegroundColor Yellow
    exit 1
}
