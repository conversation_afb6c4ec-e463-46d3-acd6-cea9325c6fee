import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor() {
    // 记录邮件配置信息（不包含密码）
    this.logger.log(`Email configuration:
      HOST: ${process.env.EMAIL_HOST}
      PORT: ${process.env.EMAIL_PORT}
      SECURE: ${process.env.EMAIL_SECURE}
      USER: ${process.env.EMAIL_USER}
      FROM: ${process.env.EMAIL_FROM}
    `);

    // 创建邮件发送器
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.example.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'password',
      },
    });
  }

  /**
   * 发送带有PDF附件的邮件
   * @param to 收件人邮箱
   * @param subject 邮件主题
   * @param text 邮件正文
   * @param pdfPath PDF文件路径
   * @param documentNo 文档编号
   * @returns 发送结果
   */
  async sendPdfEmail(
    to: string,
    subject: string,
    text: string,
    pdfPath: string,
    documentNo: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 检查文件是否存在
      this.logger.log(`Checking PDF file at path: ${pdfPath}`);
      if (!fs.existsSync(pdfPath)) {
        this.logger.error(`PDF file not found at ${pdfPath}`);
        throw new Error(`PDF file not found at ${pdfPath}`);
      }
      this.logger.log(`PDF file exists at ${pdfPath}`);

      // 记录邮件发送信息
      this.logger.log(`Sending email to: ${to}, subject: ${subject}, with PDF: ${documentNo}.pdf`);

      // 发送邮件
      const info = await this.transporter.sendMail({
        from: process.env.EMAIL_FROM || '"Tregoal System" <<EMAIL>>',
        to,
        subject,
        text,
        attachments: [
          {
            filename: `${documentNo}.pdf`,
            path: pdfPath,
            contentType: 'application/pdf',
          },
        ],
      });

      this.logger.log(`Email sent to ${to} with messageId: ${info.messageId}`);
      return { success: true, message: `Email sent to ${to}` };
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}: ${error.message}`);
      return { success: false, message: error.message };
    }
  }

  /**
   * 发送带有PDF附件的邮件（使用日期和文档编号查找PDF）
   * @param to 收件人邮箱
   * @param subject 邮件主题
   * @param text 邮件正文
   * @param documentNo 文档编号
   * @param date 日期（可选，默认为当天）
   * @returns 发送结果
   */
  async sendPdfEmailByDocumentNo(
    to: string,
    subject: string,
    text: string,
    documentNo: string,
    date?: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 如果没有提供日期，使用当前日期
      let dateStr = date;
      if (!dateStr) {
        const today = new Date();
        dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      }

      // 构建PDF文件路径
      const pdfPath = path.join(process.cwd(), 'PDF_Output', dateStr, `${documentNo}.pdf`);

      return this.sendPdfEmail(to, subject, text, pdfPath, documentNo);
    } catch (error) {
      this.logger.error(`Failed to send email for document ${documentNo}: ${error.message}`);
      return { success: false, message: error.message };
    }
  }
}
