# 权限用户数据读取问题解决方案

## 问题描述

用户反馈：切换到其他权限登录后无法读取数据，页面显示空白。

## 根本原因分析

### 1. **后台刷新缺失权限信息**
在`CheckListProvider`的`_refreshDataInBackground`方法中，调用`loadCheckList`时没有传递`currentStaff`参数，导致权限过滤逻辑失效。

```dart
// 问题代码
await loadCheckList(staffId, forceRefresh: true);
```

### 2. **用户切换时状态残留**
不同权限用户登录时，前一个用户的数据和状态可能残留，影响新用户的数据加载。

### 3. **权限过滤逻辑依赖currentStaff**
系统的数据过滤逻辑严重依赖`currentStaff`参数：
- **普通员工**：只能看到自己楼层的数据
- **主管/管理员/BOM专员/司机**：可以看到所有数据
- **BOM专员**：有特殊的数据过滤规则

## 解决方案

### 1. **保存当前用户信息**

在`CheckListProvider`中添加`_currentStaff`字段：

```dart
// 保存当前用户信息，用于后台刷新时的权限过滤
Staff? _currentStaff;
```

### 2. **修复loadCheckList方法**

确保保存用户信息：

```dart
Future<void> loadCheckList(int staffId,
    {Staff? currentStaff, bool forceRefresh = false}) async {
  // 保存当前用户信息，用于后台刷新时的权限过滤
  if (currentStaff != null) {
    _currentStaff = currentStaff;
  }
  
  // ... 其他逻辑
}
```

### 3. **修复后台刷新方法**

使用保存的用户信息进行权限过滤：

```dart
void _refreshDataInBackground(int staffId, {int delayMs = 500}) {
  Future.delayed(Duration(milliseconds: delayMs), () async {
    try {
      // 使用保存的currentStaff信息进行权限过滤
      await loadCheckList(staffId, currentStaff: _currentStaff, forceRefresh: true);
    } catch (e) {
      // 静默处理错误
    }
  });
}
```

### 4. **修复所有loadCheckList调用**

确保所有调用都传递`currentStaff`参数：

```dart
// rejectStaffCheck方法中
await loadCheckList(staffId, currentStaff: _currentStaff, forceRefresh: true);

// refreshOrder方法中  
await loadCheckList(supervisorId, currentStaff: _currentStaff, forceRefresh: true);
```

### 5. **添加状态清理机制**

添加清理所有数据和状态的方法：

```dart
// 清除所有数据和状态（用于登出时）
void clearAllData() {
  _pendingRegularCheck.clear();
  _pendingBomCheck.clear();
  _pendingSupervisorCheck.clear();
  _checkedItems.clear();
  _waitingList.clear();
  _processingItems.clear();
  _currentStaff = null;
  clearCache();
  _isLoading = false;
  _error = null;
  notifyListeners();
}
```

### 6. **优化登出处理**

在用户登出时清理所有状态：

```dart
// 登出处理
final authProvider = Provider.of<AuthProvider>(context, listen: false);
final checkListProvider = Provider.of<CheckListProvider>(context, listen: false);

// 清理所有数据和状态
checkListProvider.clearAllData();
authProvider.logout();
```

## 权限过滤逻辑说明

### 普通员工 (regular)
- 只能看到自己负责楼层的数据
- 楼层通过`binShelfNo`的第一个数字判断
- 只能看到自己检查过的待确认项目

### 主管 (supervisor)
- 可以看到所有楼层的数据
- 可以执行所有类型的检查和确认操作

### 管理员 (admin)
- 拥有所有主管权限
- 可以进行员工管理、紧急队列等管理功能

### BOM专员 (staff_bom)
- 可以看到所有楼层的数据
- 有特殊的BOM子项过滤规则
- 只关注BOM相关的项目

### 司机 (driver)
- 可以看到所有楼层的数据
- 主要关注出货和配送相关功能

## 技术要点

### 1. **状态一致性**
- 确保用户信息在整个生命周期中保持一致
- 后台刷新时使用正确的权限信息

### 2. **数据隔离**
- 不同权限用户之间的数据完全隔离
- 登出时彻底清理前一用户的数据

### 3. **错误处理**
- 后台刷新失败不影响用户操作
- 权限验证失败时提供明确的错误信息

## 测试验证

### 1. **权限切换测试**
- 使用不同权限账号登录
- 验证数据过滤是否正确
- 确认页面不会显示空白

### 2. **并发操作测试**
- 快速连续操作
- 验证权限过滤在后台刷新时是否正常

### 3. **登出登入测试**
- 登出后重新登录
- 验证数据是否正确加载
- 确认没有前一用户的数据残留

## 总结

通过以上修复，解决了权限用户数据读取问题：

✅ **权限信息保持**：后台刷新时正确传递用户权限信息
✅ **状态清理**：用户切换时彻底清理前一用户状态  
✅ **数据隔离**：不同权限用户数据完全隔离
✅ **错误预防**：避免权限验证失败导致的空白页面
✅ **一致性保证**：确保整个应用中权限过滤逻辑的一致性

这些修改确保了所有权限级别的用户都能正确加载和显示属于自己权限范围内的数据。
