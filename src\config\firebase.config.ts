import { registerAs } from '@nestjs/config';

export default registerAs('firebase', () => ({
  // Firebase项目配置
  projectId: process.env.FIREBASE_PROJECT_ID,

  // 服务账户配置 - 支持两种方式
  // 方式1: 使用环境变量中的密钥信息
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,

  // 方式2: 使用服务账户JSON文件路径
  serviceAccountPath: process.env.FIREBASE_SERVICE_ACCOUNT_PATH || './firebase-service-account.json',

  // Firebase Storage配置
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,

  // 上传配置
  uploadConfig: {
    // PDF文件在Storage中的根目录
    pdfRootPath: process.env.FIREBASE_PDF_ROOT_PATH || 'pdfs',

    // 是否启用Firebase上传 (可用于开发环境关闭)
    enabled: process.env.FIREBASE_UPLOAD_ENABLED !== 'false',

    // 上传重试次数
    retryAttempts: parseInt(process.env.FIREBASE_RETRY_ATTEMPTS || '3', 10),

    // 上传超时时间 (毫秒)
    uploadTimeout: parseInt(process.env.FIREBASE_UPLOAD_TIMEOUT || '30000', 10),

    // 自动清理配置
    autoCleanupEnabled: process.env.FIREBASE_AUTO_CLEANUP_ENABLED !== 'false',
    cleanupDays: parseInt(process.env.FIREBASE_CLEANUP_DAYS || '90', 10), // 默认90天（3个月）
    weeklyStatsEnabled: process.env.FIREBASE_WEEKLY_STATS_ENABLED !== 'false',
  },

  // 日志配置
  logging: {
    enabled: process.env.FIREBASE_LOGGING_ENABLED !== 'false',
    level: process.env.FIREBASE_LOG_LEVEL || 'info', // 'debug', 'info', 'warn', 'error'
  },
}));
