const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接配置
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
  user: process.env.POSTGRES_USERNAME || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DATABASE || 'demo',
});

async function migratePriorityField() {
  const client = await pool.connect();

  try {
    console.log('🚀 开始执行数据库迁移：添加 priority 字段...\n');

    // 1. 检查字段是否已存在
    console.log('1. 检查 priority 字段是否已存在...');
    const checkFieldQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'delivery_order_main' 
      AND column_name = 'priority';
    `;

    const fieldExists = await client.query(checkFieldQuery);

    if (fieldExists.rows.length > 0) {
      console.log('✅ priority 字段已存在，跳过创建');
    } else {
      // 2. 添加 priority 字段
      console.log('2. 添加 priority 字段...');
      await client.query(`
        ALTER TABLE delivery_order_main 
        ADD COLUMN priority INTEGER DEFAULT 0;
      `);
      console.log('✅ priority 字段添加成功');
    }

    // 3. 添加注释
    console.log('3. 添加字段注释...');
    await client.query(`
      COMMENT ON COLUMN delivery_order_main.priority IS '订单优先级，数值越大优先级越高，0为普通优先级';
    `);
    console.log('✅ 字段注释添加成功');

    // 4. 检查索引是否已存在
    console.log('4. 检查索引是否已存在...');
    const checkIndexQuery = `
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'delivery_order_main' 
      AND indexname = 'idx_delivery_order_main_priority_created_at';
    `;

    const indexExists = await client.query(checkIndexQuery);

    if (indexExists.rows.length > 0) {
      console.log('✅ 索引已存在，跳过创建');
    } else {
      // 5. 添加索引以优化排序查询性能
      console.log('5. 添加索引以优化排序查询性能...');
      await client.query(`
        CREATE INDEX idx_delivery_order_main_priority_created_at 
        ON delivery_order_main(priority DESC, created_at ASC);
      `);
      console.log('✅ 索引创建成功');
    }

    // 6. 初始化现有数据的优先级为 0
    console.log('6. 初始化现有数据的优先级...');
    const updateResult = await client.query(`
      UPDATE delivery_order_main 
      SET priority = 0 
      WHERE priority IS NULL;
    `);
    console.log(`✅ 更新了 ${updateResult.rowCount} 条记录的优先级为 0`);

    // 7. 验证迁移结果
    console.log('\n7. 验证迁移结果...');
    const verifyQuery = `
      SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN priority = 0 THEN 1 END) as normal_priority,
        COUNT(CASE WHEN priority > 0 THEN 1 END) as urgent_priority
      FROM delivery_order_main;
    `;

    const verifyResult = await client.query(verifyQuery);
    const stats = verifyResult.rows[0];

    console.log('📊 迁移统计:');
    console.log(`   总记录数: ${stats.total_records}`);
    console.log(`   普通优先级 (0): ${stats.normal_priority}`);
    console.log(`   紧急优先级 (>0): ${stats.urgent_priority}`);

    console.log('\n🎉 数据库迁移完成！');
    console.log('\n📝 迁移内容总结:');
    console.log('   ✅ 添加了 priority 字段 (INTEGER, DEFAULT 0)');
    console.log('   ✅ 添加了字段注释');
    console.log('   ✅ 创建了优化排序的复合索引');
    console.log('   ✅ 初始化了现有数据的优先级');

  } catch (error) {
    console.error('❌ 迁移过程中发生错误:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行迁移
migratePriorityField()
  .then(() => {
    console.log('\n✨ 迁移脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 迁移脚本执行失败:', error);
    process.exit(1);
  });
