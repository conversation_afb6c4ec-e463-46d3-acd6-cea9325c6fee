# PM2 Startup Script - Ensure all necessary directories and permissions are properly set

Write-Host "=== Starting NestJS Backend Service (PM2) ===" -ForegroundColor Green

# Use current directory as project root
$projectRoot = Get-Location

Write-Host "Current working directory: $(Get-Location)" -ForegroundColor Yellow

# Create necessary directories
$directories = @(
    "PDF_Output",
    "logs"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "Directory exists: $dir" -ForegroundColor Yellow
    }
}

# Check if dist directory exists, build if not
if (!(Test-Path "dist")) {
    Write-Host "dist directory not found, starting project build..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed, exiting script" -ForegroundColor Red
        exit 1
    }
    Write-Host "Project build completed" -ForegroundColor Green
}

# Check if PM2 is installed
try {
    pm2 --version | Out-Null
    Write-Host "PM2 is installed" -ForegroundColor Green
} catch {
    Write-Host "PM2 not installed, installing..." -ForegroundColor Yellow
    npm install -g pm2
    if ($LASTEXITCODE -ne 0) {
        Write-Host "PM2 installation failed, exiting script" -ForegroundColor Red
        exit 1
    }
    Write-Host "PM2 installation completed" -ForegroundColor Green
}

# Stop existing PM2 processes
Write-Host "Stopping existing PM2 processes..." -ForegroundColor Yellow
pm2 stop backend-nestjs 2>$null
pm2 delete backend-nestjs 2>$null

# Start PM2 process
Write-Host "Starting PM2 process..." -ForegroundColor Yellow
pm2 start ecosystem.config.js

if ($LASTEXITCODE -eq 0) {
    Write-Host "PM2 started successfully!" -ForegroundColor Green

    # Show PM2 status
    pm2 status

    # Show logs
    Write-Host "`n=== Recent Logs ===" -ForegroundColor Cyan
    pm2 logs backend-nestjs --lines 20

    Write-Host "`n=== Service Information ===" -ForegroundColor Cyan
    Write-Host "Service name: backend-nestjs"

    # 从配置文件中提取端口号
    $port = "3000"  # 默认端口
    if (Test-Path "ecosystem.config.js") {
        $configContent = Get-Content "ecosystem.config.js" -Raw
        if ($configContent -match "PORT:\s*(\d+)") {
            $port = $matches[1]
        }
    }
    Write-Host "Access URL: http://localhost:$port"
    Write-Host "API docs: http://localhost:$port/api-docs"
    Write-Host "Status page: http://localhost:$port/status"
    Write-Host "`nUse the following commands to manage the service:"
    Write-Host "  Check status: pm2 status"
    Write-Host "  View logs: pm2 logs backend-nestjs"
    Write-Host "  Restart service: pm2 restart backend-nestjs"
    Write-Host "  Stop service: pm2 stop backend-nestjs"
    Write-Host "  Delete service: pm2 delete backend-nestjs"
} else {
    Write-Host "PM2 startup failed!" -ForegroundColor Red
    exit 1
}
