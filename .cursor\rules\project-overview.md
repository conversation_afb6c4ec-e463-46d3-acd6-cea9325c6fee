---
title: "队列系统项目概述"
description: "队列系统的整体介绍和架构"
version: "1.0.0"
last_updated: "2025-05-19"
---

# 队列系统项目概述

## 系统简介

队列系统是一个用于仓库管理的综合解决方案，包括货物检查、BOM 管理、订单出货和送货确认等功能。系统由 NestJS 后端和 Flutter 移动应用组成，支持多语言界面和多角色操作。

## 系统架构

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Flutter 移动应用 │◄────►│   NestJS 后端   │◄────►│  SQL Server DB  │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
                                 │
                                 ▼
                         ┌─────────────────┐
                         │                 │
                         │  PostgreSQL DB  │
                         │                 │
                         └─────────────────┘
```

## 主要功能模块

1. **员工管理** - 员工认证、权限控制
2. **仓库检查** - 货物检查、BOM 管理
3. **订单出货** - 订单确认、司机送货
4. **通知系统** - 系统通知、拒绝反馈
5. **PDF 生成** - 订单 PDF 生成、签名和邮件发送

## 用户角色

系统支持以下用户角色，每个角色具有不同的权限和功能：

### 1. 普通员工 (Regular)
- 查看待检查货物列表
- 执行货物初步检查
- 查看检查统计和进度

### 2. BOM 专员 (Staff_BOM)
- 查看待检查 BOM 物品列表
- 执行 BOM 物品二次检查
- 查看 BOM 检查统计

### 3. 主管 (Senior)
- 确认员工和 BOM 专员的检查结果
- 拒绝检查并提供原因
- 标记订单为已出货
- 指派司机送货
- 查看所有订单状态

### 4. 司机 (Driver)
- 查看待送达订单列表
- 确认订单送达
- 获取客户签名
- 生成和发送 PDF 订单

## 技术栈

### 后端
- **NestJS**: Node.js 框架
- **TypeORM**: 数据库 ORM
- **PostgreSQL**: 主数据库
- **SQL Server**: 外部数据源
- **JWT**: 认证
- **Puppeteer**: PDF 生成
- **Nodemailer**: 邮件发送

### 前端
- **Flutter**: 跨平台 UI 框架
- **Provider**: 状态管理
- **Dio**: 网络请求
- **flutter_pdfview**: PDF 查看
- **signature**: 签名功能
- **shared_preferences**: 本地存储

## 业务流程

### 货物检查流程
1. 普通员工检查货物
2. 对于 BOM 物品，BOM 专员进行二次检查
3. 主管确认检查结果
4. 如有问题，主管可拒绝并要求重新检查

### 订单出货流程
1. 主管确认所有货物已检查
2. 主管标记订单为已出货
3. 主管可选择指派司机送货
4. 司机确认送达并获取客户签名
5. 生成 PDF 订单并发送给客户

## 数据同步

系统通过定时任务从 SQL Server 同步数据到 PostgreSQL：
- 从 AR_DO_Main_tbl 同步到 delivery_order_main
- 从 AR_DO_Detail_tbl 同步到 delivery_order_detail
- 对于 BOM 数据，从 SC_Tran_Detail 同步

## 部署架构

系统采用以下部署架构：

```
┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │
│   Web 服务器     │◄────►│  应用服务器      │
│  (Nginx/Apache) │      │  (Node.js)      │
│                 │      │                 │
└─────────────────┘      └─────────────────┘
                                 │
                                 ▼
                         ┌─────────────────┐
                         │                 │
                         │  数据库服务器    │
                         │ (PostgreSQL)    │
                         │                 │
                         └─────────────────┘
```

## 项目文档

详细的项目文档分为以下几个部分：

### 后端文档
- [后端项目概述](backend/project-overview.md)
- [API 端点规范](backend/api-endpoints.md)
- [API 参考文档](backend/api-reference.md)
- [数据模型定义](backend/data-models.md)
- [错误处理规范](backend/error-handling.md)
- [开发规则](backend/development-rules.md)

### 前端文档
- [前端项目概述](frontend/project-overview.md)
- [状态管理说明](frontend/state-management.md)
- [UI 组件说明](frontend/ui-components.md)
- [API 服务实现](frontend/api-services.md)

### 通用文档
- [业务流程说明](business-flow.md)
- [系统安全指南](security-guidelines.md)
