import 'package:json_annotation/json_annotation.dart';

part 'document.g.dart';

// 自定义转换器：将任意类型转换为int
class IntConverter implements JsonConverter<int, dynamic> {
  const IntConverter();

  @override
  int fromJson(dynamic json) {
    if (json is int) return json;
    if (json is String) return int.parse(json);
    if (json is double) return json.toInt();
    return 0; // 默认值
  }

  @override
  dynamic toJson(int object) => object;
}

// 自定义转换器：将任意类型转换为double
class DoubleConverter implements JsonConverter<double, dynamic> {
  const DoubleConverter();

  @override
  double fromJson(dynamic json) {
    if (json is double) return json;
    if (json is int) return json.toDouble();
    if (json is String) {
      if (json.isEmpty) return 0.0;
      return double.parse(json);
    }
    return 0.0; // 默认值
  }

  @override
  dynamic toJson(double object) => object;
}

@JsonSerializable()
class Document {
  @IntConverter()
  final int id;
  @JsonKey(name: 'document_no')
  final String documentNo;
  @JsonKey(name: 'transaction_type')
  final String transactionType;
  @JsonKey(name: 'document_date')
  final DateTime documentDate;
  @JsonKey(name: 'customer_code')
  final String customerCode;
  @JsonKey(name: 'salesman_code')
  final String salesmanCode;
  @JsonKey(name: 'issue_by')
  final String issueBy;
  @JsonKey(name: 'issue_date')
  final DateTime issueDate;
  @JsonKey(name: 'issue_time')
  final DateTime issueTime;
  @JsonKey(name: 'deliver_to')
  final String deliverTo;
  final String remarks;
  @JsonKey(name: 'do_amount')
  @DoubleConverter()
  final double doAmount;
  @JsonKey(name: 'customer_name')
  final String customerName;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'rejection_reason')
  final String? rejectionReason;

  @JsonKey(name: 'details')
  final List<DocumentDetail>? details;

  Document({
    required this.id,
    required this.documentNo,
    required this.transactionType,
    required this.documentDate,
    required this.customerCode,
    required this.salesmanCode,
    required this.issueBy,
    required this.issueDate,
    required this.issueTime,
    required this.deliverTo,
    required this.remarks,
    required this.doAmount,
    required this.customerName,
    required this.createdAt,
    this.rejectionReason,
    this.details,
  });

  factory Document.fromJson(Map<String, dynamic> json) =>
      _$DocumentFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentToJson(this);
}

@JsonSerializable()
class DocumentDetail {
  @IntConverter()
  final int id;
  @JsonKey(name: 'document_no')
  final String documentNo;
  @JsonKey(name: 'stock_code')
  final String stockCode;
  @JsonKey(name: 'location_code')
  final String locationCode;
  @JsonKey(name: 'do_quantity')
  @DoubleConverter()
  final double doQuantity;
  @JsonKey(name: 'brand_code')
  final String? brandCode;
  @JsonKey(name: 'unit_price')
  @DoubleConverter()
  final double? unitPrice;
  @JsonKey(name: 'total_amount')
  @DoubleConverter()
  final double? totalAmount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  DocumentDetail({
    required this.id,
    required this.documentNo,
    required this.stockCode,
    required this.locationCode,
    required this.doQuantity,
    this.brandCode,
    this.unitPrice,
    this.totalAmount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DocumentDetail.fromJson(Map<String, dynamic> json) =>
      _$DocumentDetailFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentDetailToJson(this);
}
