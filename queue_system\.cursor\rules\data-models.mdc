---
description:
globs:
alwaysApply: false
---
# 移动应用数据模型

## 主要数据模型

### 1. 员工模型 (StaffModel)
```dart
class StaffModel {
  final int id;
  final String username;
  final String level; // 'supervisor', 'admin', 'regular', 'staff_bom', 'driver'
  final String floor; // '1F', '2F', '3F', 'ALL'
  final String fullName;
  final String status; // 'active', 'inactive'
  final DateTime? lastLogin;
  final DateTime createdAt;
}
```

### 2. 订单主表模型 (DeliveryOrderMainModel)
```dart
class DeliveryOrderMainModel {
  final int id;
  final String documentNo;
  final DateTime documentDate;
  final String customer;
  final String customerName;
  final String? remarks;
  final bool shipped;
  final DateTime? shippedAt;
  final int? shippedBy;
  final bool delivered;
  final DateTime? deliveredAt;
  final int? deliveredBy;
  final String? deliveryNotes;
  final DateTime createdAt;
  final List<DeliveryOrderDetailModel>? details;
}
```

### 3. 订单明细模型 (DeliveryOrderDetailModel)
```dart
class DeliveryOrderDetailModel {
  final int id;
  final int mainId;
  final String documentNo; // 可能是D00001/1格式
  final int line;
  final String stock;
  final String description;
  final String quantity;
  final String binShelfNo; // 格式: '2-1-G001-1'
  final String? parentCode;
  final bool staffChecked;
  final DateTime? staffCheckedAt;
  final int? staffId;
  final bool bomSpecialistChecked;
  final DateTime? bomSpecialistCheckedAt;
  final int? bomSpecialistId;
  final bool supervisorChecked;
  final DateTime? supervisorCheckedAt;
  final int? supervisorId;
  final DateTime createdAt;
  final DeliveryOrderMainModel? main;
}
```

### 4. 通知模型 (NotificationModel)
```dart
class NotificationModel {
  final int id;
  final String type;
  final String message;
  final int recipientId;
  final int? senderId;
  final String? documentNo;
  final int? line;
  final int? detailId;
  final bool read;
  final DateTime? readAt;
  final String? rejectReason;
  final DateTime createdAt;
}
```

### 5. 用户凭证模型 (AuthCredentialsModel)
```dart
class AuthCredentialsModel {
  final int id;
  final String username;
  final String level;
  final String floor;
  final String fullName;
  final String token; // JWT或其他身份验证令牌
}
```

### 6. 检查项目模型 (CheckItemModel)
```dart
class CheckItemModel {
  final int id;
  final String documentNo;
  final int line;
  final String stock;
  final String description;
  final String quantity;
  final String binShelfNo;
  final String? parentCode;
  final bool staffChecked;
  final DateTime? staffCheckedAt;
  final int? staffId;
  final bool bomSpecialistChecked;
  final DateTime? bomSpecialistCheckedAt;
  final int? bomSpecialistId;
  final bool supervisorChecked;
  final DateTime? supervisorCheckedAt;
  final int? supervisorId;
  final DateTime documentDate;
  final String customer;
  final String customerName;
}
```

### 7. 楼层统计模型 (FloorStatsModel)
```dart
class FloorStatsModel {
  final int total;
  final int checked;
  final int unchecked;
  final int bomChecked;
  final int bomUnchecked;
  final int supervisorChecked;
}
```

## 枚举类型

### 1. 员工级别 (StaffLevel)
```dart
enum StaffLevel {
  supervisor, // 主管
  admin,      // 管理员
  regular,    // 普通员工
  staff_bom,  // BOM管理员
  driver      // 司机
}
```

### 2. 楼层类型 (FloorType)
```dart
enum FloorType {
  f1, // '1F'
  f2, // '2F'
  f3, // '3F'
  all // 'ALL'
}
```

### 3. 通知类型 (NotificationType)
```dart
enum NotificationType {
  reject,      // 检查被拒绝
  complete,    // 订单已完成
  assignment,  // 订单分配
  reminder     // 提醒
}
```
