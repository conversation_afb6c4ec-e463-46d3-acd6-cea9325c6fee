import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';
import 'package:queue_system/l10n/app_localizations.dart';

class SignatureScreen extends StatefulWidget {
  final String documentNo;

  const SignatureScreen({
    super.key,
    required this.documentNo,
  });

  @override
  State<SignatureScreen> createState() => _SignatureScreenState();
}

class _SignatureScreenState extends State<SignatureScreen> {
  final GlobalKey<SfSignaturePadState> _signaturePadKey = GlobalKey();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 禁用返回按钮
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false, // 移除默认的返回按钮
          title: Text('${context.t('signature')} - ${widget.documentNo}'),
          // 移除所有actions，包括清除按钮
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                context.t('please_sign_here'),
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            Container(
              margin: const EdgeInsets.all(16.0),
              width: MediaQuery.of(context).size.width - 32, // 屏幕宽度减去边距
              height: 180, // 固定高度，确保宽大于高的横向矩形
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: SfSignaturePad(
                key: _signaturePadKey,
                backgroundColor: Colors.white,
                strokeColor: Colors.black,
                minimumStrokeWidth: 1.0,
                maximumStrokeWidth: 4.0,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _clearSignature,
                    icon: const Icon(Icons.refresh),
                    label: Text(context.t('clear')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _saveSignature,
                    icon: const Icon(Icons.check),
                    label: Text(context.t('confirm')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              ),
          ],
        ),
      ),
    );
  }

  void _clearSignature() {
    _signaturePadKey.currentState?.clear();
  }

  Future<void> _saveSignature() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String base64Signature = '';

      // 检查签名是否为空
      final bool hasSignature =
          !(_signaturePadKey.currentState?.toPathList().isEmpty ?? true);

      if (hasSignature) {
        // 有签名时，将签名转换为图像
        final ui.Image signatureImage =
            await _signaturePadKey.currentState!.toImage(
          pixelRatio: 3.0, // 提高图像质量
        );
        final ByteData? byteData =
            await signatureImage.toByteData(format: ui.ImageByteFormat.png);

        if (byteData == null) {
          throw Exception('Failed to convert signature to image');
        }

        final Uint8List signatureBytes = byteData.buffer.asUint8List();
        base64Signature = base64Encode(signatureBytes);
      }
      // 如果没有签名，base64Signature 保持为空字符串

      // 返回签名数据（可能为空）
      if (mounted) {
        Navigator.of(context).pop(base64Signature);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('error_saving_signature')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
