<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>Delivery Order</title>
  <style>
    @page {
      size: A4;
      margin: 15mm 4mm;
    }

    html,
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      font-size: 10pt;
      width: 210mm;
    }

    /* 首页样式 */
    .first-page {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      page-break-after: auto;
    }

    /* 后续页面样式 */
    .continuation-page {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      page-break-before: always;
      page-break-after: auto;
    }

    /* 最后一页样式 */
    .last-page {
      position: relative;
      width: 100%;
      box-sizing: border-box;
    }

    .page-content {
      position: relative;
      z-index: 1;
      padding-bottom: 80mm;
      /* 进一步减少页脚预留空间 */
      margin: 0 4mm;
      /* 恢复左右边距 */
      width: 202mm;
      /* 210mm - 8mm = 202mm */
      box-sizing: border-box;
    }

    /* 页脚样式 - 仅在最后一页显示，固定在页面底部 */
    .page-footer {
      position: fixed;
      bottom: 15mm;
      /* 与@page底部边距保持一致 */
      left: 4mm;
      /* 恢复左边距 */
      right: 4mm;
      /* 恢复右边距 */
      width: 202mm;
      /* 210mm - 8mm = 202mm */
      padding: 0;
      /* 移除内边距，使用定位控制边距 */
      background: white;
      box-sizing: border-box;
      z-index: 10;
    }

    .header {
      text-align: center;
      margin-bottom: 8px;
    }

    .company-name {
      font-size: 16pt;
      font-weight: bold;
      margin-bottom: 3px;
    }

    .company-address {
      font-size: 8pt;
      margin-bottom: 2px;
    }

    .company-contact {
      font-size: 8pt;
      margin-bottom: 8px;
    }

    .document-title {
      font-size: 14pt;
      font-weight: bold;
      margin: 10px 0;
      text-align: center;
    }

    .info-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }

    .left-info {
      width: 60%;
    }

    .right-info {
      width: 40%;
      text-align: left;
    }

    .info-row {
      display: flex;
      margin-bottom: 5px;
    }

    .info-label {
      width: 100px;
      font-weight: normal;
    }

    .info-value {
      flex: 1;
    }

    /* 右侧信息区域特殊样式 */
    .right-info .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .right-info .info-label {
      width: 80px;
      text-align: left;
      flex-shrink: 0;
    }

    .right-info .info-colon {
      width: 10px;
      text-align: center;
      flex-shrink: 0;
    }

    .right-info .info-value {
      flex: 1;
      text-align: left;
      padding-left: 5px;
    }

    .table-container {
      margin-top: 15px;
      margin-bottom: 15px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
    }

    th {
      border-top: 1px solid black;
      border-bottom: 1px solid black;
      padding: 3px 5px;
      text-align: left;
      font-size: 9pt;
      background-color: #f2f2f2;
      font-weight: bold;
      line-height: 1.2;
    }

    td {
      border: none;
      padding: 2px 5px;
      text-align: left;
      font-size: 8pt;
      line-height: 1.2;
    }

    /* 优化表格列宽分配 */
    .col-no {
      width: 5%;
    }

    .col-stock-code {
      width: 15%;
    }

    /* 增加宽度以显示25个字符 */
    .col-description {
      width: 30%;
      /* 调整宽度 */
      max-width: 30%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .col-bin {
      width: 15%;
    }

    .col-brand {
      width: 10%;
    }

    .col-qty {
      width: 8%;
    }

    .col-price {
      width: 8%;
    }

    .col-amount {
      width: 9%;
    }

    /* DESCRIPTION列文本截断样式 */
    .description-text {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
    }

    .qty,
    .price,
    .amount {
      text-align: right;
    }

    .remarks {
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .remarks-label {
      font-weight: bold;
    }

    .total-disclaimer-container {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      border-top: 1px solid black;
      padding: 5px;
    }

    .total-row {
      text-align: right;
      width: 30%;
    }

    .total-label {
      font-weight: bold;
      display: inline-block;
      width: 100px;
    }

    .disclaimer {
      font-size: 7pt;
      width: 65%;
      text-align: left;
    }

    .signatures {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-top: 20px;
      min-height: 80px;
      /* 为签名区域预留固定高度 */
    }

    .signature-box {
      width: 30%;
      text-align: center;
      position: relative;
      margin-bottom: 10px;
      height: 80px;
      /* 固定高度确保对齐 */
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }

    /* 签名框内的名字显示在签名线上方 */
    .signature-box>div:first-child {
      position: absolute;
      top: 0px;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 9pt;
      font-weight: normal;
    }

    /* 角色标签显示在底部 */
    .signature-box>div:last-child {
      margin-top: 5px;
      font-size: 9pt;
      font-weight: bold;
    }

    .supervisor-name {
      margin-top: 30px;
      /* 你可以根据需要调整这个数值 */
    }

    .signature-line {
      border-top: 1px solid black;
      margin: 10px 0 5px 0;
      width: 100%;
      height: 1px;
    }

    .issued-by {
      text-align: right;
      width: 100%;
    }

    .footer {
      font-size: 6pt;
      text-align: right;
      margin-top: 10px;
    }

    .signature-image {
      height: 50px;
      margin-bottom: 5px;
      position: relative;
      z-index: 2;
      max-width: 100%;
      object-fit: contain;
    }

    /* 签名线样式 */
    .signature-line-container {
      position: relative;
      height: 20px;
      margin-bottom: 5px;
    }

    /* 签名线，位于RECEIVED BY上方 */
    .signature-line-container::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      border-top: 1px solid black;
      z-index: 1;
    }

    /* 特殊处理RECEIVED BY签名框中的图像 */
    .signature-box .signature-image {
      position: absolute;
      top: 5px;
      left: 50%;
      transform: translateX(-50%);
      height: 40px;
      max-width: 80%;
      object-fit: contain;
    }

    #page-number {
      font-weight: normal;
    }

    /* 分页控制样式 */
    .page-break {
      page-break-before: always;
    }

    .no-page-break {
      page-break-inside: avoid;
    }

    /* 隐藏非最后页的页脚 */
    .hidden-footer {
      display: none;
    }

    /* 显示最后页的页脚 */
    .visible-footer {
      display: block;
    }

    /* Table pagination rules */
    table {
      width: 100%;
      border-collapse: collapse;
      page-break-inside: auto;
      margin-bottom: 0;
    }

    /* Ensure table headers repeat on each page */
    thead {
      display: table-header-group;
    }

    tbody {
      display: table-row-group;
    }

    tfoot {
      display: table-footer-group;
    }

    /* Table row pagination rules */
    tr {
      page-break-inside: avoid;
      page-break-after: auto;
      height: auto;
      margin: 0;
    }

    /* 缩小表格行间距和防止孤立行 */
    tbody tr {
      height: 15px;
      /* 减少固定行高 */
      margin: 0;
      padding: 0;
      page-break-inside: avoid;
    }

    /* Content area pagination */
    .page-content {
      overflow: visible;
    }

    /* Ensure content doesn't overlap with footer */
    .content-area {
      margin-bottom: 0;
      padding-bottom: 0;
    }

    /* Table-specific pagination improvements */
    table tbody {
      /* Allow table body to break across pages */
      page-break-inside: auto;
    }
  </style>
</head>

<body>
  <!-- 首页 -->
  <div class="first-page">
    <div class="page-content">
      <!-- 页眉信息 -->
      <div class="header">
        <div class="company-name">POLYSEALS SDN BHD</div>
        <div class="company-address">NO. 28, JALAN PERUSAHAAN AMARI, AMARI BUSINESS PARK, KAWASAN INDUSTRI BATU CAVES,
          68100 BATU CAVES, SELANGOR</div>
        <div class="company-contact">TEL: 03-6187 8511 FAX: 03-6187 8646 Email: <EMAIL> (ACC)</div>
      </div>

      <div class="document-title">INVOICE</div>

      <div class="info-container">
        <div class="left-info">
          <div class="info-row">
            <div class="info-label">A/C NO.</div>
            <div class="info-value">: {{main.customer}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">BILLING TO</div>
            <div class="info-value">: {{main.customer_name}}</div>
          </div>
          {{#if main.customer_deliver_address}}
          <div class="info-row">
            <div class="info-label"></div>
            <div class="info-value">{{main.customer_deliver_address}}</div>
          </div>
          {{/if}}
          <div class="info-row">
            <div class="info-label">TEL</div>
            <div class="info-value">: {{main.customer_telephone}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">FAX</div>
            <div class="info-value">: {{main.customer_fax}}</div>
          </div>
        </div>
        <div class="right-info">
          <div class="info-row">
            <div class="info-label">INVOICE NO.</div>
            <div class="info-colon">:</div>
            <div class="info-value">{{main.document_no}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">DATE</div>
            <div class="info-colon">:</div>
            <div class="info-value">{{formatDate main.document_date}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">SALESMAN</div>
            <div class="info-colon">:</div>
            <div class="info-value">{{main.salesman_code}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">TERM</div>
            <div class="info-colon">:</div>
            <div class="info-value">{{main.term}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">P/O NO.</div>
            <div class="info-colon">:</div>
            <div class="info-value">{{main.po_no}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">PAGE NO.</div>
            <div class="info-colon">:</div>
            <div class="info-value" id="page-number"><span class="current-page">1</span>/<span
                class="total-pages">1</span></div>
          </div>
        </div>
      </div>

      <!-- 商品明细表格 -->
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="col-no">NO</th>
              <th class="col-stock-code">STOCK CODE</th>
              <th class="col-description">DESCRIPTION</th>
              <th class="col-bin">BIN</th>
              <th class="col-brand">BRAND</th>
              <th class="col-qty qty">QTY</th>
              <th class="col-price price">U/PRICE</th>
              <th class="col-amount amount">AMOUNT</th>
            </tr>
          </thead>
          <tbody>
            {{#each details}}
            <tr>
              <td class="col-no">{{line}}</td>
              <td class="col-stock-code">{{stock}}</td>
              <td class="col-description">
                <span class="description-text" title="{{description}}">
                  {{truncateText description 30}}
                </span>
              </td>
              <td class="col-bin">{{bin_shelf_no}}{{#if staffName}} ({{staffName}}){{/if}}</td>
              <td class="col-brand">{{brand}}</td>
              <td class="col-qty qty">{{formatQuantity quantity}} {{uom}}</td>
              <td class="col-price price">{{formatNumber unitPrice}}</td>
              <td class="col-amount amount">{{formatNumber amount}}</td>
            </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
    </div>

    <!-- 页脚 - 仅在最后一页显示 -->
    <div class="page-footer visible-footer">
      {{#if main.remarks}}
      <div class="remarks">
        <span class="remarks-label">REMARKS :</span> {{main.remarks}}
      </div>
      {{/if}}

      <div class="total-disclaimer-container">
        <div class="disclaimer">
          No claim or goods return will be accepted after 7 days from date of delivery.<br>
          Responsibility of seller ceased with deliver of merchandise in good order & condition<br>
          to public carrier or consignee.<br>
          <b>PUBLIC BANK ACCOUNT NO: **********</b><br>
          <b>MAYBANK ACCOUNT NO: ************</b>
        </div>
        <div class="total-row">
          <span class="total-label">TOTAL : RM</span>
          <span>{{formatNumber totalAmount}}</span>
        </div>
      </div>

      <div class="signatures">
        <div class="signature-box">
          <div class="supervisor-name">{{supervisorName}}</div>
          <div class="signature-line"></div>
          <div>CHECKED BY</div>
        </div>
        <div class="signature-box">
          <div class="supervisor-name">{{driverName}}</div>
          <div class="signature-line"></div>
          <div>DELIVERED BY</div>
        </div>
        <div class="signature-box">
          {{#if signatureBase64}}
          <!-- 有签名图像时，先显示图像 -->
          <img src="data:image/png;base64,{{signatureBase64}}" class="signature-image">
          <!-- 签名线容器，线条会显示在图像下方 -->
          <div class="signature-line-container"></div>
          {{else}}
          <!-- 无签名图像时，显示普通签名线 -->
          <div class="signature-line"></div>
          {{/if}}
          <div>RECEIVED BY</div>
        </div>
      </div>
      <div class="issued-by" style="text-align: right; margin-top: 10px;">
        ISSUED BY : {{issuedBy}}
      </div>
    </div>
  </div>

  <script>
    // 页码和分页处理脚本 - 在Puppeteer中执行
    document.addEventListener('DOMContentLoaded', function () {
      // 分页逻辑处理
      const handlePagination = () => {
        const tableRows = document.querySelectorAll('tbody tr');
        const pageHeight = 267; // A4页面可用高度(mm): 297 - 30 = 267
        const pageHeightPx = pageHeight * 3.78; // 转换为像素

        let currentPageHeight = 0;
        let pageNumber = 1;
        const headerHeight = document.querySelector('.header').offsetHeight;
        const infoHeight = document.querySelector('.info-container').offsetHeight;
        const tableHeaderHeight = document.querySelector('thead').offsetHeight;

        // 计算首页已用高度 - 进一步减少预留空间，让内容更接近页脚
        currentPageHeight = headerHeight + infoHeight + tableHeaderHeight + 40; // 进一步减少预留空间

        tableRows.forEach((row, index) => {
          const rowHeight = row.offsetHeight;

          // 检查是否需要分页
          if (currentPageHeight + rowHeight > pageHeightPx) {
            // 需要分页，重置当前页高度
            currentPageHeight = tableHeaderHeight + rowHeight + 40; // 进一步减少新页面预留空间
            pageNumber++;
          } else {
            currentPageHeight += rowHeight;
          }
        });

        // 更新总页数
        const totalPagesElements = document.querySelectorAll('.total-pages');
        totalPagesElements.forEach(element => {
          element.textContent = pageNumber;
        });

        // 处理页脚显示逻辑
        const pageFooters = document.querySelectorAll('.page-footer');
        pageFooters.forEach((footer, index) => {
          if (index === pageFooters.length - 1) {
            // 最后一页显示页脚
            footer.classList.remove('hidden-footer');
            footer.classList.add('visible-footer');
          } else {
            // 其他页面隐藏页脚
            footer.classList.add('hidden-footer');
            footer.classList.remove('visible-footer');
          }
        });
      };

      // 更新页码
      const updatePageNumbers = () => {
        const currentPageElements = document.querySelectorAll('.current-page');
        currentPageElements.forEach((element, index) => {
          element.textContent = index + 1;
        });
      };

      // 执行分页处理
      handlePagination();
      updatePageNumbers();

      // 监听窗口大小变化，重新计算分页
      window.addEventListener('resize', () => {
        handlePagination();
        updatePageNumbers();
      });
    });
  </script>
</body>

</html>