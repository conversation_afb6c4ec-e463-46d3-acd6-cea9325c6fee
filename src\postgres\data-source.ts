import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { Main } from './entities/main.entity';
import { Detail } from './entities/detail.entity';
import { Staff } from './entities/staff.entity';
import { Notification } from './entities/notification.entity';
import { CnMain } from './entities/cn-main.entity';
import { CnDetail } from './entities/cn-detail.entity';
import { StockReturnTracking } from './entities/stock-return-tracking.entity';

// 加载环境变量
config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
  username: process.env.POSTGRES_USERNAME || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DATABASE || 'postgres',
  synchronize: false, // 在生产环境中应该为false
  logging: true,
  entities: [
    Main,
    Detail,
    Staff,
    Notification,
    CnMain,
    CnDetail,
    StockReturnTracking,
  ],
  migrations: ['src/postgres/migrations/*.ts'],
  migrationsTableName: 'migrations',
});
