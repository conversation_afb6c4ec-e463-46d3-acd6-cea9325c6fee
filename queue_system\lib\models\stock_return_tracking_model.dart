import 'package:flutter/foundation.dart';

class StockReturnTrackingModel {
  final int id;
  final String documentNo;
  final int line;
  final String stockCode;
  final String binShelfNo;
  final double quantity;
  final String triggerReason;
  final int triggeredBy;
  final int assignedTo;
  final bool isCompleted;
  final int? completedBy;
  final DateTime? completedAt;
  final String? notes;
  final DateTime createdAt;
  
  // 关联的员工信息
  final String? triggerStaffName;
  final String? assignedStaffName;
  final String? completedStaffName;

  StockReturnTrackingModel({
    required this.id,
    required this.documentNo,
    required this.line,
    required this.stockCode,
    required this.binShelfNo,
    required this.quantity,
    required this.triggerReason,
    required this.triggeredBy,
    required this.assignedTo,
    required this.isCompleted,
    this.completedBy,
    this.completedAt,
    this.notes,
    required this.createdAt,
    this.triggerStaffName,
    this.assignedStaffName,
    this.completedStaffName,
  });

  factory StockReturnTrackingModel.fromJson(Map<String, dynamic> json) {
    try {
      return StockReturnTrackingModel(
        id: json['id'],
        documentNo: json['document_no'],
        line: json['line'],
        stockCode: json['stock_code'],
        binShelfNo: json['bin_shelf_no'],
        quantity: json['quantity'] != null
            ? double.parse(json['quantity'].toString())
            : 0.0,
        triggerReason: json['trigger_reason'],
        triggeredBy: json['triggered_by'],
        assignedTo: json['assigned_to'],
        isCompleted: json['is_completed'] ?? false,
        completedBy: json['completed_by'],
        completedAt: json['completed_at'] != null 
            ? DateTime.parse(json['completed_at']) 
            : null,
        notes: json['notes'],
        createdAt: DateTime.parse(json['created_at']),
        triggerStaffName: json['trigger_staff']?['username'],
        assignedStaffName: json['assigned_staff']?['username'],
        completedStaffName: json['completed_staff']?['username'],
      );
    } catch (e) {
      debugPrint('Error parsing StockReturnTrackingModel: $e');
      debugPrint('JSON: $json');
      // 返回一个默认的模型，避免应用崩溃
      return StockReturnTrackingModel(
        id: 0,
        documentNo: '',
        line: 0,
        stockCode: '',
        binShelfNo: '未分配货架',
        quantity: 0,
        triggerReason: 'unknown',
        triggeredBy: 0,
        assignedTo: 0,
        isCompleted: false,
        createdAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'document_no': documentNo,
      'line': line,
      'stock_code': stockCode,
      'bin_shelf_no': binShelfNo,
      'quantity': quantity,
      'trigger_reason': triggerReason,
      'triggered_by': triggeredBy,
      'assigned_to': assignedTo,
      'is_completed': isCompleted,
      'completed_by': completedBy,
      'completed_at': completedAt?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // 获取触发原因的本地化文本
  String getTriggerReasonText() {
    switch (triggerReason) {
      case 'order_refresh':
        return '订单重新同步';
      case 'order_deleted':
        return '订单已删除';
      case 'quantity_reduced':
        return '数量减少';
      case 'item_rejected':
        return '物品被拒绝';
      default:
        return triggerReason;
    }
  }

  // 获取状态文本
  String getStatusText() {
    if (isCompleted) {
      return '已完成';
    } else {
      return '待归还';
    }
  }

  // 获取状态颜色
  int getStatusColor() {
    if (isCompleted) {
      return 0xFF4CAF50; // 绿色
    } else {
      return 0xFFFF9800; // 橙色
    }
  }

  // 复制对象并修改某些字段
  StockReturnTrackingModel copyWith({
    int? id,
    String? documentNo,
    int? line,
    String? stockCode,
    String? binShelfNo,
    double? quantity,
    String? triggerReason,
    int? triggeredBy,
    int? assignedTo,
    bool? isCompleted,
    int? completedBy,
    DateTime? completedAt,
    String? notes,
    DateTime? createdAt,
    String? triggerStaffName,
    String? assignedStaffName,
    String? completedStaffName,
  }) {
    return StockReturnTrackingModel(
      id: id ?? this.id,
      documentNo: documentNo ?? this.documentNo,
      line: line ?? this.line,
      stockCode: stockCode ?? this.stockCode,
      binShelfNo: binShelfNo ?? this.binShelfNo,
      quantity: quantity ?? this.quantity,
      triggerReason: triggerReason ?? this.triggerReason,
      triggeredBy: triggeredBy ?? this.triggeredBy,
      assignedTo: assignedTo ?? this.assignedTo,
      isCompleted: isCompleted ?? this.isCompleted,
      completedBy: completedBy ?? this.completedBy,
      completedAt: completedAt ?? this.completedAt,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      triggerStaffName: triggerStaffName ?? this.triggerStaffName,
      assignedStaffName: assignedStaffName ?? this.assignedStaffName,
      completedStaffName: completedStaffName ?? this.completedStaffName,
    );
  }

  @override
  String toString() {
    return 'StockReturnTrackingModel{id: $id, documentNo: $documentNo, stockCode: $stockCode, isCompleted: $isCompleted}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockReturnTrackingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
