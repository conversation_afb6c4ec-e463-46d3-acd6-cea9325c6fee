// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Document _$DocumentFromJson(Map<String, dynamic> json) => Document(
      id: const IntConverter().fromJson(json['id']),
      documentNo: json['document_no'] as String,
      transactionType: json['transaction_type'] as String,
      documentDate: DateTime.parse(json['document_date'] as String),
      customerCode: json['customer_code'] as String,
      salesmanCode: json['salesman_code'] as String,
      issueBy: json['issue_by'] as String,
      issueDate: DateTime.parse(json['issue_date'] as String),
      issueTime: DateTime.parse(json['issue_time'] as String),
      deliverTo: json['deliver_to'] as String,
      remarks: json['remarks'] as String,
      doAmount: const DoubleConverter().fromJson(json['do_amount']),
      customerName: json['customer_name'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      rejectionReason: json['rejection_reason'] as String?,
      details: (json['details'] as List<dynamic>?)
          ?.map((e) => DocumentDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DocumentToJson(Document instance) => <String, dynamic>{
      'id': const IntConverter().toJson(instance.id),
      'document_no': instance.documentNo,
      'transaction_type': instance.transactionType,
      'document_date': instance.documentDate.toIso8601String(),
      'customer_code': instance.customerCode,
      'salesman_code': instance.salesmanCode,
      'issue_by': instance.issueBy,
      'issue_date': instance.issueDate.toIso8601String(),
      'issue_time': instance.issueTime.toIso8601String(),
      'deliver_to': instance.deliverTo,
      'remarks': instance.remarks,
      'do_amount': const DoubleConverter().toJson(instance.doAmount),
      'customer_name': instance.customerName,
      'created_at': instance.createdAt.toIso8601String(),
      'rejection_reason': instance.rejectionReason,
      'details': instance.details,
    };

DocumentDetail _$DocumentDetailFromJson(Map<String, dynamic> json) =>
    DocumentDetail(
      id: const IntConverter().fromJson(json['id']),
      documentNo: json['document_no'] as String,
      stockCode: json['stock_code'] as String,
      locationCode: json['location_code'] as String,
      doQuantity: const DoubleConverter().fromJson(json['do_quantity']),
      brandCode: json['brand_code'] as String?,
      unitPrice: const DoubleConverter().fromJson(json['unit_price']),
      totalAmount: const DoubleConverter().fromJson(json['total_amount']),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$DocumentDetailToJson(DocumentDetail instance) =>
    <String, dynamic>{
      'id': const IntConverter().toJson(instance.id),
      'document_no': instance.documentNo,
      'stock_code': instance.stockCode,
      'location_code': instance.locationCode,
      'do_quantity': const DoubleConverter().toJson(instance.doQuantity),
      'brand_code': instance.brandCode,
      'unit_price': _$JsonConverterToJson<dynamic, double>(
          instance.unitPrice, const DoubleConverter().toJson),
      'total_amount': _$JsonConverterToJson<dynamic, double>(
          instance.totalAmount, const DoubleConverter().toJson),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
