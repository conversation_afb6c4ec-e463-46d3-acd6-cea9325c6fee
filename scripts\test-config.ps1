# Test Configuration Script

Write-Host "=== Configuration Test ===" -ForegroundColor Green

# Check if ecosystem.production.config.js exists
if (!(Test-Path "ecosystem.production.config.js")) {
    Write-Host "ERROR: ecosystem.production.config.js not found" -ForegroundColor Red
    exit 1
}

Write-Host "OK: ecosystem.production.config.js found" -ForegroundColor Green

# Read and check content
$configContent = Get-Content "ecosystem.production.config.js" -Raw

Write-Host "`nChecking for placeholders..." -ForegroundColor Cyan

$placeholders = @(
    "your-production-sql-server",
    "your-prod-username", 
    "your-prod-password",
    "your-production-postgres",
    "your-prod-pg-username",
    "your-prod-pg-password",
    "your-production-email-host",
    "<EMAIL>",
    "{YOUR_",
    "your-prod-email-password"
)

$foundPlaceholders = @()
# 只检查env配置部分，忽略注释和说明文档
$envSection = $configContent -split "env:\s*\{" | Select-Object -Skip 1 | Select-Object -First 1
if ($envSection) {
    $envSection = $envSection -split "\}" | Select-Object -First 1
    foreach ($placeholder in $placeholders) {
        if ($envSection -like "*$placeholder*") {
            $foundPlaceholders += $placeholder
            Write-Host "  FOUND: $placeholder" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  WARNING: Could not parse env section" -ForegroundColor Yellow
}

if ($foundPlaceholders.Count -eq 0) {
    Write-Host "OK: No placeholders found" -ForegroundColor Green
} else {
    Write-Host "ERROR: Found $($foundPlaceholders.Count) placeholders" -ForegroundColor Red
}

Write-Host "`nConfiguration summary:" -ForegroundColor Cyan

# Extract SQL Server Host
Write-Host "  SQL Server Host: " -NoNewline
if ($configContent -like "*SQL_SERVER_HOST:*") {
    # 直接从配置内容中提取值
    if ($configContent -match "SQL_SERVER_HOST:\s*'([^']+)'") {
        Write-Host $matches[1] -ForegroundColor Yellow
    } elseif ($configContent -match 'SQL_SERVER_HOST:\s*"([^"]+)"') {
        Write-Host $matches[1] -ForegroundColor Yellow
    } else {
        Write-Host "PARSE ERROR" -ForegroundColor Red
    }
} else {
    Write-Host "NOT FOUND" -ForegroundColor Red
}

# Extract Database
Write-Host "  Database: " -NoNewline
if ($configContent -like "*SQL_SERVER_DATABASE:*") {
    # 直接从配置内容中提取值
    if ($configContent -match "SQL_SERVER_DATABASE:\s*'([^']+)'") {
        Write-Host $matches[1] -ForegroundColor Yellow
    } elseif ($configContent -match 'SQL_SERVER_DATABASE:\s*"([^"]+)"') {
        Write-Host $matches[1] -ForegroundColor Yellow
    } else {
        Write-Host "PARSE ERROR" -ForegroundColor Red
    }
} else {
    Write-Host "NOT FOUND" -ForegroundColor Red
}

# Extract Working Directory
Write-Host "  Working Directory: " -NoNewline
if ($configContent -like "*cwd:*") {
    # 直接从配置内容中提取值
    if ($configContent -match "cwd:\s*'([^']+)'") {
        Write-Host $matches[1] -ForegroundColor Yellow
    } elseif ($configContent -match 'cwd:\s*"([^"]+)"') {
        Write-Host $matches[1] -ForegroundColor Yellow
    } else {
        Write-Host "PARSE ERROR" -ForegroundColor Red
    }
} else {
    Write-Host "NOT FOUND" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
