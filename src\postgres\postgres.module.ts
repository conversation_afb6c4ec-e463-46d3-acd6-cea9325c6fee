import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Main } from './entities/main.entity';
import { Detail } from './entities/detail.entity';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const config = configService.get('database.postgres');
        return {
          ...config,
          name: 'postgres',
          autoLoadEntities: true,
        };
      },
    }),
    TypeOrmModule.forFeature([Main, Detail], 'postgres'),
  ],
  exports: [TypeOrmModule],
})
export class PostgresModule {} 