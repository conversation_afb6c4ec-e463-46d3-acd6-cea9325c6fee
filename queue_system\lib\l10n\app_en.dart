class AppLocalizationsEn {
  static Map<String, String> get values => {
        // Login page
        'login': 'Login',
        'username': '<PERSON>rna<PERSON>',
        'password': 'Password',
        'please_enter_username': 'Please enter username',
        'please_enter_password': 'Please enter password',
        'register_new_staff': 'Register New Staff',
        'switch_language': 'Switch Language (中文/Bahasa Melayu)',

        // Home page
        'warehouse_system': 'Warehouse Inspection System',
        'refresh': 'Refresh Data',
        'logout': 'Logout',
        'supervisor': 'Supervisor',
        'admin': 'Administrator',
        'regular_staff': 'Regular Staff',
        'login_first': 'Please login first',
        'loading': 'Loading...',
        'connection_failed': 'Connection Failed',
        'unable_connect_server':
            'Unable to connect to server, please check network',
        'retry': 'Retry',
        'no_items_to_check': 'No items to check',
        'no_items_to_confirm': 'No items to confirm',
        'to_check_bom': 'To Check BOM',
        'bom_status': 'BOM Status',
        'error_loading_bom_status': 'Error loading BOM status',
        'no_bom_children_found': 'No BOM children found',
        'waiting_for_staff_bom': 'Waiting for BOM specialist',

        // Tabs
        'pending_check': 'Pending Check',
        'pending_confirm': 'Pending Confirmation',
        'to_check': 'To Check',
        'to_confirm': 'To Confirm',
        'to_ship': 'To Ship',
        'to_deliver': 'To Deliver',
        'to_delivery': 'To Deliver',
        'delivery': 'Delivery Status',
        'delivery_status': 'Delivery Status',
        'bom_review': 'BOM Review',
        'queue': 'Queue',
        'no_pending_items': 'No pending items',

        // Check items
        'line': 'Line',
        'quantity': 'Quantity',
        'shelf': 'Shelf',
        'checked': 'Checked',
        'unknown_time': 'Unknown time',
        'note': 'Note',
        'check': 'Check',
        'confirm': 'Confirm',
        'waiting_for_supervisor': 'Waiting for supervisor',
        'waiting_for_staff': 'Waiting for staff check',
        'already_checked': 'Checked',
        'not_checked': 'Not checked',

        // Dialogs
        'confirm_check': 'Confirm Check',
        'confirm_check_message':
            'Are you sure you want to mark this item as checked?',
        'confirm_verify': 'Confirm Verification',
        'confirm_verify_message': 'Are you sure you want to verify this item?',
        'cancel': 'Cancel',
        'confirm': 'Confirm',
        'operation_success': 'Operation successful',
        'operation_failed': 'Operation failed',
        'refresh_failed': 'Refresh failed',
        'loading_failed': 'Loading data failed',
        'failed': 'Failed',
        'data_refreshed': 'Data refreshed successfully',

        // Logout confirmation
        'logout_confirmation': 'Logout',
        'logout_message': 'Are you sure you want to logout?',

        // Add staff
        'add_staff': 'Add Staff',
        'fullname': 'Full Name',
        'email': 'Email',
        'staff_level': 'Staff Level',
        'floor': 'Floor',
        'create_staff': 'Create Staff',
        'please_fill_required': 'Please fill in all required fields',
        'staff_created_success': 'Staff created successfully',

        // Document details
        'document_no': 'Document No',
        'date': 'Date',
        'line_no': 'Line',
        'items': 'items',
        'pending_items': 'pending items',
        'documents': 'documents',
        'quantity_short': 'Qty',
        'shelf_location': 'Shelf',
        'parent_code': 'BOM',
        'checked_status': 'Checked',
        'description': 'Description',
        'no_shelf_assigned': 'Not assigned',

        // Network diagnostic
        'network_diagnostic': 'Network Diagnostic',
        'start_diagnostic': 'Start Diagnostic',
        'running_diagnostics': 'Running network diagnostics...',
        'error_running_diagnostics': 'Error running diagnostics',
        'network_diagnostic_explanation':
            'This tool checks your network connectivity and server access.',
        'run_diagnostics_again': 'Run Diagnostics Again',
        'back': 'Back',

        // Authentication errors
        'error_empty_response': 'Server returned empty response',
        'error_login_failed': 'Login failed',
        'error_invalid_credentials': 'Invalid username or password',
        'error_network': 'Network connection failed',
        'error_create_staff': 'Failed to create staff',
        'error_invalid_data': 'Invalid input data',

        // Service errors
        'error_get_checklist': 'Failed to get check list',
        'error_staff_check': 'Staff check failed',
        'error_supervisor_check': 'Supervisor confirmation failed',
        'error_connection': 'Connection failed',
        'error_sync': 'Synchronization failed',
        'success_sync': 'Synchronization successful',
        'connection_server_failed':
            'Failed to connect to server, please check network',

        // Document screen
        'document_title': 'Document',
        'detail_items': 'Detail Items',
        'no_detail_data': 'No detail data',
        'document_number': 'Document No.',
        'transaction_type': 'Transaction Type',
        'document_date': 'Document Date',
        'customer_code': 'Customer Code',
        'customer_name': 'Customer Name',
        'salesman_code': 'Salesman',
        'issuer': 'Issuer',
        'issue_date': 'Issue Date',
        'issue_time': 'Issue Time',
        'delivery_address': 'Delivery Address',
        'remarks': 'Remarks',
        'amount': 'Amount',
        'created_at': 'Created At',
        'location': 'Location',
        'quantity_full': 'Quantity',

        // App title
        'inventory_sync_system': 'Inventory Synchronization System',
        'app_title': 'Warehouse Management System',

        // 员工类型
        'bom_specialist': 'BOM Specialist',
        'driver': 'Driver',
        'suggested_driver': 'Suggested Driver',

        // 验证错误
        'username_no_spaces': 'Username cannot contain spaces',
        'password_no_spaces': 'Password cannot contain spaces',
        'enter_username_no_spaces': 'Enter username (no spaces allowed)',
        'enter_password_no_spaces': 'Enter password (no spaces allowed)',

        // 出货和送货相关
        'ready_to_ship': 'Ready to Ship',
        'not_ready': 'Not Ready',
        'check_failed': 'Check Failed',
        'waiting_for_check': 'Waiting for Check',
        'no_ready_documents': 'No documents ready to ship',
        'no_delivery_documents': 'No documents to deliver',
        'no_shipments': 'No shipments found',
        'ship': 'Ship',
        'ship_without_driver': 'Ship without Driver',
        'assign_driver': 'Assign Driver',
        'driver_id': 'Driver ID',
        'confirm_ship_without_driver': 'Ship without Driver',
        'confirm_ship_with_driver': 'Assign Driver and Ship',
        'confirm_shipment': 'Confirm Shipment',
        'will_be_shipped_without_driver': 'will be shipped without a driver',
        'will_be_shipped_and_assigned_to_driver':
            'will be shipped and assigned to driver',
        'shipment_completed_successfully': 'Shipment completed successfully',
        'shipment_failed': 'Shipment failed',
        'document': 'Document',
        'customer': 'Customer',
        'delivered': 'Delivered',
        'rejected': 'Rejected',
        'in_transit': 'In Transit',
        'no_driver_assigned': 'No driver assigned',

        // 送达相关
        'confirm_delivery': 'Confirm Delivery',
        'cannot_deliver': 'Cannot Deliver',
        'delivery_notes': 'Delivery Notes',
        'delivery_notes_hint': 'Enter delivery notes (optional)',
        'delivered_at': 'Delivered At',
        'delivery_confirmed': 'Delivery confirmed',
        'delivery_confirmation_failed': 'Delivery confirmation failed',
        'reject_delivery': 'Reject Delivery',
        'confirm_rejection': 'Confirm Rejection',
        'rejection_reason': 'Rejection Reason',
        'rejection_reason_hint': 'Please enter reason for delivery rejection',
        'rejection_reason_required': 'Rejection reason is required',
        'delivery_rejected': 'Delivery rejected',
        'rejection_failed': 'Rejection failed',

        // BOM Specialist
        'confirm_bom_check': 'Confirm BOM Check',
        'confirm_bom_check_message':
            'Are you sure this BOM item is checked correctly?',
        'confirm_bom_child_check_message':
            'Confirm checking this BOM child item?',
        'bom_check': 'BOM Check',
        'bom_items': 'BOM items',
        'no_bom_items': 'No BOM items to review',
        'reject_staff_check': 'Reject Staff Check',
        'enter_reject_reason': 'Enter rejection reason',
        'reject_reason': 'Rejection Reason',
        'confirm_reject': 'Confirm Rejection',
        'confirm_reject_message':
            'Confirm rejection of this staff check? Staff will need to check again.',
        'reject': 'Reject',
        'error_bom_check': 'BOM check failed',
        'error_reject_check': 'Reject check failed',
        'feature_requires_backend':
            'This feature requires backend support, currently simulated',
        'unassigned_shelf': 'Unassigned Shelf',
        'special_item': 'Special Item',
        'stock': 'Stock',

        // API错误
        'error_get_ready_documents': 'Failed to get ready documents',
        'error_complete_document': 'Failed to complete document',
        'error_get_delivery_list': 'Failed to get delivery list',
        'error_confirm_delivery': 'Failed to confirm delivery',
        'error_get_shipments': 'Failed to get shipments',
        'error_reject_delivery': 'Failed to reject delivery',
        'error_reject_shipment': 'Failed to reject shipment',

        // Shipment rejection
        'reject_shipment': 'Reject Shipment',
        'reject_shipment_warning':
            'This will reset all checks and require staff to check all items again.',
        'shipment_rejected': 'Shipment rejected successfully',
        'reject_failed': 'Rejection failed',
        'processing': 'Processing...',
        'no_reason_provided': 'No reason provided',
        'confirm_reject_shipment': 'Confirm Reject Shipment',
        'confirm_reject_shipment_message':
            'Are you sure you want to reject this shipment? This will reset all checks and require staff to check all items again.',

        // Notification related
        'notifications': 'Notifications',
        'notification': 'Notification',
        'mark_as_read': 'Mark as Read',
        'mark_all_as_read': 'Mark All as Read',
        'marked_as_read': 'Marked as read',
        'all_marked_as_read': 'All notifications marked as read',
        'no_notifications': 'No unread notifications',
        'check_rejected': 'Check Rejected',
        'reason': 'Reason',
        'you': 'You',
        'auto_assign_driver_message':
            'You will be automatically assigned as the delivery driver',
        'view': 'View',
        'view_pdf': 'View PDF',
        'download_pdf': 'Download PDF',
        'view_pdf_history': 'View PDF History',
        'pdf_history': 'PDF History',
        'no_pdf_documents': 'No PDF documents available',
        'error_open_pdf': 'Failed to open PDF',
        'downloading_pdf': 'Downloading PDF',
        'pdf_download_success': 'PDF downloaded successfully',
        'pdf_download_failed': 'PDF download failed',
        'shipped_at': 'Shipped At',
        'opening_pdf': 'Opening PDF',
        'unknown_error': 'Unknown error',
        'error': 'Error',
        'close': 'Close',
        'pdf_open_help':
            'Make sure you have a PDF viewer installed on your device. If the problem persists, try again later or contact support.',
        'choose_pdf_view_method': 'How would you like to view the PDF?',
        'view_native': 'View in app',
        'view_in_browser': 'View in browser',

        // Signature related
        'signature': 'Signature',
        'please_sign_here': 'Please sign here',
        'clear': 'Clear',
        'signature_required': 'Signature is required',
        'error_saving_signature': 'Error saving signature',
        'error_processing_signature': 'Error processing signature',

        // Email related
        'send_pdf_email': 'Send PDF by Email',
        'email_required': 'Email is required',
        'invalid_email': 'Invalid email format',
        'send': 'Send',
        'sending_email': 'Sending Email',
        'email_sent_successfully': 'Email sent successfully',
        'email_sent_failed': 'Failed to send email',
        'send_pdf': 'Send PDF',
        'pdf_options': 'PDF Options',
        'pdf_email_options': 'PDF Email Options',
        'pdf_created_successfully': 'PDF created successfully',
        'skip_email': 'Skip Email',
        'send_email': 'Send Email',

        // WhatsApp related
        'send_to_whatsapp': 'Send to WhatsApp',
        'phone_number': 'Phone Number',
        'please_enter_phone_number': 'Please enter phone number',
        'please_enter_valid_phone_format':
            'Please enter a valid phone number format',
        'message_preview': 'Message preview to be sent:',
        'whatsapp_not_installed': 'WhatsApp Not Installed',
        'whatsapp_not_installed_message':
            'WhatsApp app is not installed on your device.\n\nPlease install WhatsApp first, then try sending the message again.\n\nAlternatively, you can choose to share the PDF link through other methods.',
        'got_it': 'Got it',
        'download_whatsapp': 'Download WhatsApp',
        'whatsapp': 'WhatsApp',
        'getting_pdf_download_link': 'Getting PDF download link...',
        'pdf_not_uploaded_to_cloud':
            'PDF file has not been uploaded to cloud, cannot share via WhatsApp',
        'whatsapp_opened_complete_sending':
            'WhatsApp opened, please complete sending the message',
        'whatsapp_share_failed': 'WhatsApp sharing failed',
        'whatsapp_message_template':
            'Hello! Here is the PDF document link for your order {documentNo}:\n\n{pdfUrl}\n\nPlease click the link to view or download your order details.\n\nThank you!',

        // Stock Return Related
        'stock_return': 'Stock Return',
        'cn_return': 'CN Return',
        'return_tracking': 'Return Tracking',
        'no_pending_stock_returns': 'No pending stock returns',
        'no_pending_cn_returns': 'No pending CN returns',
        'confirm_stock_return': 'Confirm Stock Return',
        'confirm_return': 'Confirm Return',
        'return_confirmed': 'Return confirmed',
        'confirm_stock_return_message':
            'I have returned this stock to its original location',
        'stock_return_confirmed': 'Stock return confirmed',
        'error_get_stock_returns': 'Failed to get stock returns',
        'error_confirm_stock_return': 'Failed to confirm stock return',
        'completed_by': 'Completed by',

        // Order Refresh Related
        'refresh_order': 'Refresh Order',
        'refresh_order_warning': 'Warning:',
        'refresh_order_warning_1': 'Will delete all existing check records',
        'refresh_order_warning_2': 'Re-sync latest data from system',
        'refresh_order_warning_3': 'Staff need to re-check all items',
        'refresh_order_reason_hint': 'Order information updated...',
        'confirm_refresh': 'Confirm Refresh',
        'refreshing_order': 'Refreshing order...',
        'order_refreshed_successfully': 'Order refreshed successfully',
        'refresh_order_failed': 'Order refresh failed',
        'deleted': 'Deleted',
        'inserted': 'Inserted',
        'notifications_sent': 'Notifications sent',
        'optional': 'Optional',

        // Pagination related
        'load_more': 'Load More',
        'loading_more': 'Loading more...',
        'no_more_data': 'No more data',

        // Search related
        'search_documents': 'Search documents, customers, stock...',
        'search_results': 'Search Results',
        'no_search_results': 'No matching results found',
        'clear_search': 'Clear Search',
        'search_keyword': 'Search Keyword',

        // Urgent priority feature
        'set_urgent_status': 'Set Urgent Status',
        'current_priority': 'Current Priority',
        'priority_normal': 'Normal',
        'priority_urgent': 'Urgent',
        'select_new_priority': 'Select New Priority',
        'set_to_normal': 'Set to Normal',
        'set_to_urgent': 'Set to Urgent',
        'cancel_urgent': 'Cancel Urgent',
        'urgent_status_updated': 'Priority Updated',
        'set_to_normal_priority': 'Set to normal priority',
        'set_to_urgent_priority': 'Set to urgent priority',
        'failed_to_set_priority': 'Failed to set priority',
        'urgent_label': 'Urgent',
      };
}
