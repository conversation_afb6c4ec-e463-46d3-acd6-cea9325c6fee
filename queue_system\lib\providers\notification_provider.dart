import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:queue_system/models/notification_model.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/services/api_service.dart';
import 'package:queue_system/services/notification_service.dart';
import 'package:queue_system/utils/app_config.dart';

class NotificationProvider with ChangeNotifier {
  final NotificationService _notificationService;
  final AuthProvider _authProvider;

  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;

  NotificationProvider({required AuthProvider authProvider})
      : _notificationService = NotificationService(
            apiService: ApiService(baseUrl: AppConfig.baseUrl)),
        _authProvider = authProvider;

  List<NotificationModel> get notifications => _notifications;
  int get unreadCount => _notifications.where((n) => !n.read).length;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchNotifications() async {
    if (_authProvider.currentStaff == null) {
      _error = '用户未登录';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _notifications = await _notificationService
          .getUnreadNotifications(_authProvider.currentStaff!.id);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<bool> markAsRead(int notificationId) async {
    if (_authProvider.currentStaff == null) {
      _error = '用户未登录';
      notifyListeners();
      return false;
    }

    try {
      final updatedNotification =
          await _notificationService.markAsRead(notificationId);

      // 更新本地通知状态
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = updatedNotification;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> markAllAsRead() async {
    if (_authProvider.currentStaff == null) {
      _error = '用户未登录';
      notifyListeners();
      return false;
    }

    try {
      await _notificationService.markAllAsRead(_authProvider.currentStaff!.id);

      // 更新所有通知为已读
      _notifications = _notifications.map((notification) {
        return NotificationModel(
          id: notification.id,
          type: notification.type,
          message: notification.message,
          recipientId: notification.recipientId,
          senderId: notification.senderId,
          documentNo: notification.documentNo,
          line: notification.line,
          detailId: notification.detailId,
          read: true,
          readAt: DateTime.now(),
          rejectReason: notification.rejectReason,
          createdAt: notification.createdAt,
        );
      }).toList();

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('标记所有通知为已读失败: $e');

      // 尝试判断是否实际上成功了
      if (e.toString().contains('201') ||
          e.toString().contains('success') ||
          e.toString().contains('成功')) {
        // 可能实际上成功了，但抛出了异常
        // 更新所有通知为已读
        _notifications = _notifications.map((notification) {
          return NotificationModel(
            id: notification.id,
            type: notification.type,
            message: notification.message,
            recipientId: notification.recipientId,
            senderId: notification.senderId,
            documentNo: notification.documentNo,
            line: notification.line,
            detailId: notification.detailId,
            read: true,
            readAt: DateTime.now(),
            rejectReason: notification.rejectReason,
            createdAt: notification.createdAt,
          );
        }).toList();

        notifyListeners();
        return true;
      }

      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
