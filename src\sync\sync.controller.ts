import { Controller, Post, Query, HttpCode, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { SyncService } from './sync.service';

@ApiTags('Sync')
@Controller('sync')
export class SyncController {
  constructor(private readonly syncService: SyncService) { }

  @ApiOperation({ summary: '手动触发送货单数据同步' })
  @ApiResponse({
    status: 200,
    description: '手动触发从SQL Server到PostgreSQL的送货单数据同步，返回同步结果。'
  })
  @HttpCode(200)
  @Post()
  async syncData() {
    try {
      await this.syncService.syncData();
      return { success: true, message: '送货单数据同步完成' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  @ApiOperation({ summary: '手动触发贷项通知单数据同步' })
  @ApiResponse({
    status: 200,
    description: '手动触发从SQL Server到PostgreSQL的贷项通知单数据同步，返回同步结果。'
  })
  @HttpCode(200)
  @Post('cn')
  async syncCnData() {
    try {
      await this.syncService.syncCnData();
      return { success: true, message: '贷项通知单数据同步完成' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // 注释掉 /sync/detail API 端点 - 2025-06-18
  /*
  @ApiOperation({ summary: '同步单个明细记录' })
  @ApiQuery({ name: 'documentNo', description: '单据编号' })
  @ApiQuery({ name: 'line', description: '明细行号' })
  @ApiResponse({
    status: 200,
    description: '同步单个明细记录，返回同步结果和变更信息'
  })
  @HttpCode(200)
  @Post('detail')
  async syncDetail(
    @Query('documentNo') documentNo: string,
    @Query('line') line: number
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    if (!line) {
      throw new BadRequestException('Line number is required');
    }

    try {
      const result = await this.syncService.syncDetail(documentNo, parseInt(line.toString()));
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to sync detail: ${error.message}`);
    }
  }
  */

  @ApiOperation({ summary: '重新同步整个订单' })
  @ApiQuery({ name: 'documentNo', description: '单据编号' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiQuery({ name: 'reason', description: '重新同步原因', required: false })
  @ApiResponse({
    status: 200,
    description: '订单重新同步成功',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Order refreshed successfully' },
        statistics: {
          type: 'object',
          properties: {
            deletedCount: { type: 'number', example: 15 },
            insertedCount: { type: 'number', example: 18 },
            checkedItemsDeleted: { type: 'number', example: 3 },
            notificationsSent: { type: 'number', example: 2 }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '权限不足，只有主管可以执行此操作'
  })
  @ApiResponse({
    status: 404,
    description: '订单不存在'
  })
  @HttpCode(200)
  @Post('refresh-order')
  async refreshOrder(
    @Query('documentNo') documentNo: string,
    @Query('supervisorId') supervisorId: number,
    @Query('reason') reason?: string
  ) {
    if (!documentNo || !supervisorId) {
      throw new BadRequestException('Document number and supervisor ID are required');
    }

    try {
      const result = await this.syncService.refreshOrder(
        documentNo,
        parseInt(supervisorId.toString()),
        reason || ''
      );
      return result;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Failed to refresh order: ${error.message}`);
    }
  }
}
