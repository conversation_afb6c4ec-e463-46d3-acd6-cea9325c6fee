import { Controller, Post, Get, Query, Res, Body, BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiResponse, ApiBody } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PdfService } from './pdf.service';
import { HtmlPdfService } from './html-pdf.service';
import { Main } from '../postgres/entities/main.entity';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';

@ApiTags('PDF')
@Controller('pdf')
export class PdfController {
  constructor(
    private readonly pdfService: PdfService,
    private readonly htmlPdfService: HtmlPdfService,
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
  ) { }

  /**
   * 智能确定PDF文件路径
   * 根据订单信息确定正确的PDF文件路径，优先使用出货日期，其次使用文档日期
   */
  private async findPdfPath(documentNo: string, providedDate?: string): Promise<string> {
    const baseDir = process.env.PDF_OUTPUT_DIR || path.join(__dirname, '..', '..', 'PDF_Output');

    // 如果提供了日期，直接使用
    if (providedDate) {
      const pdfPath = path.join(baseDir, providedDate, `${documentNo}.pdf`);
      console.log(`[PDF Controller] 使用提供的日期: ${providedDate}, 路径: ${pdfPath}`);
      return pdfPath;
    }

    // 从数据库获取订单信息
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!main) {
      // 如果找不到订单，使用当前日期作为fallback
      const today = new Date();
      const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      const pdfPath = path.join(baseDir, dateStr, `${documentNo}.pdf`);
      console.log(`[PDF Controller] 订单不存在，使用当前日期: ${dateStr}, 路径: ${pdfPath}`);
      return pdfPath;
    }

    // 尝试多个可能的日期路径
    const possibleDates: Date[] = [];

    // 优先使用出货日期
    if (main.shipped_at) {
      possibleDates.push(main.shipped_at);
    }

    // 其次使用文档日期
    if (main.document_date) {
      possibleDates.push(main.document_date);
    }

    // 最后使用创建日期
    if (main.created_at) {
      possibleDates.push(main.created_at);
    }

    // 检查每个可能的日期路径
    for (const date of possibleDates) {
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const pdfPath = path.join(baseDir, dateStr, `${documentNo}.pdf`);

      console.log(`[PDF Controller] 检查路径: ${pdfPath}`);
      if (fs.existsSync(pdfPath)) {
        console.log(`[PDF Controller] 找到PDF文件: ${pdfPath}`);
        return pdfPath;
      }
    }

    // 如果都没找到，使用出货日期或文档日期作为默认路径
    const defaultDate = main.shipped_at || main.document_date || main.created_at || new Date();
    const dateStr = `${defaultDate.getFullYear()}-${String(defaultDate.getMonth() + 1).padStart(2, '0')}-${String(defaultDate.getDate()).padStart(2, '0')}`;
    const pdfPath = path.join(baseDir, dateStr, `${documentNo}.pdf`);

    console.log(`[PDF Controller] 使用默认日期路径: ${pdfPath}`);
    return pdfPath;
  }

  @ApiOperation({ summary: '生成订单PDF' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiResponse({
    status: 200,
    description: '成功生成PDF',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'PDF generated successfully' },
        filePath: { type: 'string', example: 'PDF_Output/2023-05-20/D00001.pdf' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '订单不存在'
  })
  @Post('generate')
  async generatePdf(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      const filePath = await this.pdfService.generateOrderPdf(documentNo);
      return {
        success: true,
        message: 'PDF generated successfully',
        filePath: filePath.replace(/\\/g, '/').replace(process.cwd().replace(/\\/g, '/'), ''),
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  @ApiOperation({ summary: '获取订单PDF文件' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiQuery({ name: 'date', description: '日期 (YYYY-MM-DD格式)', required: false })
  @ApiResponse({
    status: 200,
    description: '返回PDF文件',
  })
  @ApiResponse({
    status: 404,
    description: 'PDF文件不存在'
  })
  @Get('view')
  async viewPdf(
    @Query('documentNo') documentNo: string,
    @Query('date') date: string,
    @Res() res: Response
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      // 智能确定PDF文件路径
      const pdfPath = await this.findPdfPath(documentNo, date);
      console.log(`[PDF Controller] 智能确定的PDF文件路径: ${pdfPath}`);

      // 检查文件是否存在
      if (!fs.existsSync(pdfPath)) {
        console.log(`PDF文件不存在: ${pdfPath}，尝试使用HTML模板生成`);

        try {
          // 尝试使用HTML模板生成PDF
          await this.htmlPdfService.generateOrderPdf(documentNo);
          console.log(`使用HTML模板生成PDF成功`);

          // 再次检查文件是否存在
          if (!fs.existsSync(pdfPath)) {
            console.log(`生成后PDF文件仍不存在，尝试使用原始方式生成`);

            // 如果HTML模板生成失败，尝试使用原始方式
            await this.pdfService.generateOrderPdf(documentNo);
            console.log(`使用原始方式生成PDF成功`);

            // 最后检查文件是否存在
            if (!fs.existsSync(pdfPath)) {
              throw new NotFoundException(`PDF file for document ${documentNo} could not be generated`);
            }
          }
        } catch (genError) {
          console.error(`生成PDF失败: ${genError.message}`);
          throw new NotFoundException(`PDF file for document ${documentNo} not found and could not be generated`);
        }
      }

      // 设置响应头并发送文件
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${documentNo}.pdf"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // 创建文件流并发送
      const fileStream = fs.createReadStream(pdfPath);
      fileStream.pipe(res);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Error retrieving PDF: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '添加签名到PDF文件' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiQuery({ name: 'date', description: '日期 (YYYY-MM-DD格式)', required: false })
  @ApiBody({
    description: '签名数据',
    schema: {
      properties: {
        signatureBase64: { type: 'string', description: '签名的Base64编码' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '成功添加签名',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Signature added successfully' },
        filePath: { type: 'string', example: 'PDF_Output/2023-05-20/D00001.pdf' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'PDF文件不存在'
  })
  @Post('add-signature')
  async addSignature(
    @Query('documentNo') documentNo: string,
    @Query('date') date: string,
    @Body() body: { signatureBase64: string }
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    // 允许空白签名，不再验证签名数据是否为空
    // if (!body.signatureBase64) {
    //   throw new BadRequestException('Signature data is required');
    // }

    try {
      const filePath = await this.pdfService.addSignatureToPdf(documentNo, body.signatureBase64, date);
      return {
        success: true,
        message: 'Signature added successfully',
        filePath: filePath.replace(/\\/g, '/').replace(process.cwd().replace(/\\/g, '/'), ''),
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  // 邮件发送功能已移至 EmailController
  // 请使用 /email/send-pdf 端点发送PDF邮件

  @ApiOperation({ summary: '使用HTML模板生成订单PDF' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiResponse({
    status: 200,
    description: '成功生成PDF',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'PDF generated successfully' },
        filePath: { type: 'string', example: 'PDF_Output/2023-05-20/D00001.pdf' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '订单不存在'
  })
  @Post('generate-html')
  async generateHtmlPdf(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      const filePath = await this.htmlPdfService.generateOrderPdf(documentNo);
      return {
        success: true,
        message: 'PDF generated successfully using HTML template',
        filePath: filePath.replace(/\\/g, '/').replace(process.cwd().replace(/\\/g, '/'), ''),
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  @Post('generate-html-detailed')
  @ApiOperation({ summary: '生成订单PDF（HTML模板）并返回详细结果' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiResponse({
    status: 200,
    description: 'PDF生成成功，返回详细结果包含Firebase上传信息',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        filePath: { type: 'string' },
        firebase: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            downloadUrl: { type: 'string' },
            firebasePath: { type: 'string' },
            error: { type: 'string' },
            skipped: { type: 'boolean' },
            skipReason: { type: 'string' },
          },
        },
        downloadUrl: { type: 'string' },
      },
    },
  })
  async generateHtmlPdfDetailed(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      const result = await this.htmlPdfService.generateOrderPdfWithDetails(documentNo);

      // 获取数据库中的Firebase下载链接
      const main = await this.mainRepository.findOne({
        where: { document_no: documentNo },
        select: ['firebase_download_url'],
      });

      return {
        success: true,
        message: 'PDF generated successfully using HTML template',
        filePath: result.pdfPath.replace(/\\/g, '/').replace(process.cwd().replace(/\\/g, '/'), ''),
        firebase: result.firebaseUpload,
        downloadUrl: main?.firebase_download_url || null,
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  @ApiOperation({ summary: '使用HTML模板添加签名到PDF文件' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiQuery({ name: 'date', description: '日期 (YYYY-MM-DD格式)', required: false })
  @ApiBody({
    description: '签名数据',
    schema: {
      properties: {
        signatureBase64: { type: 'string', description: '签名的Base64编码' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '成功添加签名',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Signature added successfully' },
        filePath: { type: 'string', example: 'PDF_Output/2023-05-20/D00001.pdf' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'PDF文件不存在'
  })
  @Post('add-signature-html')
  async addSignatureHtml(
    @Query('documentNo') documentNo: string,
    @Query('date') date: string,
    @Body() body: { signatureBase64: string }
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    // 允许空白签名，不再验证签名数据是否为空
    // if (!body.signatureBase64) {
    //   throw new BadRequestException('Signature data is required');
    // }

    try {
      const filePath = await this.htmlPdfService.addSignatureToPdf(documentNo, body.signatureBase64, date);
      return {
        success: true,
        message: 'Signature added successfully using HTML template',
        filePath: filePath.replace(/\\/g, '/').replace(process.cwd().replace(/\\/g, '/'), ''),
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  @ApiOperation({ summary: '下载订单PDF文件' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiQuery({ name: 'date', description: '日期 (YYYY-MM-DD格式)', required: false })
  @ApiResponse({
    status: 200,
    description: '下载PDF文件',
  })
  @ApiResponse({
    status: 404,
    description: 'PDF文件不存在'
  })
  @Get('download')
  async downloadPdf(
    @Query('documentNo') documentNo: string,
    @Query('date') date: string,
    @Res() res: Response
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      // 智能确定PDF文件路径
      const pdfPath = await this.findPdfPath(documentNo, date);
      console.log(`[PDF Controller] 智能确定的下载PDF文件路径: ${pdfPath}`);

      // 从路径中提取日期用于文件名
      const pathParts = pdfPath.split(path.sep);
      const dateStr = pathParts[pathParts.length - 2] || date || new Date().toISOString().split('T')[0];

      // 检查文件是否存在
      if (!fs.existsSync(pdfPath)) {
        console.log(`PDF文件不存在: ${pdfPath}，尝试使用HTML模板生成`);

        try {
          // 尝试使用HTML模板生成PDF
          await this.htmlPdfService.generateOrderPdf(documentNo);
          console.log(`使用HTML模板生成PDF成功`);

          // 再次检查文件是否存在
          if (!fs.existsSync(pdfPath)) {
            console.log(`生成后PDF文件仍不存在，尝试使用原始方式生成`);

            // 如果HTML模板生成失败，尝试使用原始方式
            await this.pdfService.generateOrderPdf(documentNo);
            console.log(`使用原始方式生成PDF成功`);

            // 最后检查文件是否存在
            if (!fs.existsSync(pdfPath)) {
              throw new NotFoundException(`PDF file for document ${documentNo} could not be generated`);
            }
          }
        } catch (genError) {
          console.error(`生成PDF失败: ${genError.message}`);
          throw new NotFoundException(`PDF file for document ${documentNo} not found and could not be generated`);
        }
      }

      // 生成下载文件名：订单号_日期.pdf
      const downloadFileName = `${documentNo}_${dateStr}.pdf`;

      // 设置下载响应头
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${downloadFileName}"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // 创建文件流并发送
      const fileStream = fs.createReadStream(pdfPath);
      fileStream.pipe(res);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Error downloading PDF: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '调试总金额计算' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiResponse({
    status: 200,
    description: '返回总金额计算的详细信息',
  })
  @Get('debug-total')
  async debugTotalAmount(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      return await this.htmlPdfService.debugTotalAmount(documentNo);
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException(error.message);
      }
      throw error;
    }
  }

  @Get('firebase-download-url')
  @ApiOperation({ summary: '获取订单的Firebase下载链接' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiResponse({
    status: 200,
    description: '返回Firebase下载链接信息',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        documentNo: { type: 'string' },
        downloadUrl: { type: 'string' },
        firebasePath: { type: 'string' },
        uploadedAt: { type: 'string' },
        message: { type: 'string' },
      },
    },
  })
  async getFirebaseDownloadUrl(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    try {
      // 从数据库获取Firebase信息
      const main = await this.mainRepository.findOne({
        where: { document_no: documentNo },
        select: ['document_no', 'firebase_download_url', 'firebase_file_path', 'firebase_uploaded_at'],
      });

      if (!main) {
        throw new NotFoundException(`Document ${documentNo} not found`);
      }

      if (!main.firebase_download_url) {
        return {
          success: false,
          documentNo,
          downloadUrl: null,
          message: 'PDF文件尚未上传到Firebase Storage',
        };
      }

      return {
        success: true,
        documentNo,
        downloadUrl: main.firebase_download_url,
        firebasePath: main.firebase_file_path,
        uploadedAt: main.firebase_uploaded_at,
        message: 'Firebase下载链接获取成功',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get Firebase download URL: ${error.message}`);
    }
  }
}
