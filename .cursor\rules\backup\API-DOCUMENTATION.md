# SQL-Sync 应用 API 文档

**版本**: 1.6.0
**更新日期**: 2025-04-22

## 文档更新历史

| 版本  | 日期       | 描述                                                                                                                                                              |
| ----- | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.0.0 | 2024-07-01 | 初始版本                                                                                                                                                          |
| 1.1.0 | 2024-07-15 | 增加员工和主管权限描述                                                                                                                                            |
| 1.2.0 | 2025-04-15 | 优化主管检查逻辑，添加同步过滤规则，添加主管拒绝检查功能与通知系统，移除员工 email，添加 UOM 字段，限制楼层选项，主管 floor 支持 ALL 值，拒绝检查 reason 变为可选 |
| 1.3.0 | 2025-04-15 | 添加文档出货完成功能，允许标记整个订单为已出货                                                                                                                    |
| 1.4.0 | 2025-04-21 | 添加两类新员工类型：司机(drive)和 BOM 管理员(staff_bom)，拥有与主管相同的楼层权限                                                                                 |
| 1.5.0 | 2025-04-21 | 更新了数据结构和关系，支持 BOM 数据同步，delivery_order_detail 表 document_no 现支持格式如"D00001/1"，使用 main_id 关联主表                                       |
| 1.6.0 | 2025-04-22 | 添加独立的BOM专员字段：bom_specialist_checked、bom_specialist_checked_at 和 bom_specialist_id，明确区分普通员工检查和BOM专员检查                                  |

## 基础信息

- 基础 URL: `http://localhost:3000`
- 所有请求和响应均为 JSON 格式

### 重要提示：处理包含斜杠的文档编号

由于文档编号现在支持格式如 `D00001/1`，在使用 URL 路径参数时会出现冲突问题。**请使用以下推荐方法**:

- **方法 1: 使用查询参数**

  ```
  POST /check/staff?documentNo=D00001/1&line=3
  ```

  这是处理含有斜杠文档编号的推荐方法

- **方法 2: 使用请求体参数**

  ```
  POST /check/item
  Content-Type: application/json

  {
    "documentNo": "D00001/1",
    "line": 3,
    "staffId": 1
  }
  ```

- **不推荐: URL 编码** (可能在某些情况下不工作)
  ```
  POST /check/staff/D00001%2F1/3
  ```

> **注意**: 文档中所有使用 `:documentNo` 作为路径参数的 API 端点都应使用上述推荐方法访问，特别是当文档编号包含斜杠时。

## 内容索引

1. [常见使用场景](#常见使用场景)
2. [员工权限说明](#员工权限说明)
3. [员工管理 API](#员工管理-api)
4. [仓库检查 API](#仓库检查-api)
5. [订单出货 API](#订单出货-api)
6. [通知系统 API](#通知系统-api)
7. [数据同步机制](#数据同步机制)

## 数据结构更新

系统已更新数据结构以支持 BOM 数据同步：

1. **明细表主键变更**

   - delivery_order_detail 表不再使用 document_no 和 line 的组合作为唯一约束
   - 现在仅使用 id 主键字段，允许存在重复的 document_no 和 line 组合

2. **Document No 格式变更**

   - 支持 document_no 格式如"D00001/1"、"D00001/2"等，表示 BOM 分组
   - 部分保留原始格式以便于追踪和调试

3. **表关系更新**

   - 添加 main_id 字段关联到 delivery_order_main 表的 id
   - 不再使用 document_no 作为外键关联
   - 查询使用 main_id 建立两表关系

4. **BOM 支持**

   - 添加 parent_code 字段，用于存储 BOM 的父级编码
   - 父级为 NULL 表示非 BOM 项目
   - 支持与 SC_Tran_Detail 数据的同步

5. **BOM 专员字段**

   - 添加专用字段以明确区分普通员工和BOM专员的检查步骤
   - 新增字段：
     - `bom_specialist_checked`: 布尔值，表示是否已经被BOM专员检查
     - `bom_specialist_checked_at`: 日期时间，表示BOM专员检查时间
     - `bom_specialist_id`: 整数，表示执行检查的BOM专员ID

6. **BOM 检查流程**

   - 非BOM物品：普通员工检查 → 主管确认
   - BOM物品：普通员工初步检查 → BOM管理员二次检查 → 主管确认
   - BOM管理员可以查看所有已被普通员工检查过但未被BOM管理员检查的BOM物品

7. **司机送货流程**
   - 订单出货后：主管确认出货(可选择指派司机) → [如指派司机]司机送货 → 司机确认送达
   - 若无指派司机，订单在出货后直接完成
   - 若指派司机，订单需要司机确认送达后才算完成
   - 主表添加字段：
     - `delivered`: 布尔值，表示订单是否已送达
     - `delivered_at`: 日期时间，表示送达时间
     - `delivered_by`: 整数，表示确认送达的司机ID
     - `delivery_notes`: 字符串，表示送达备注

## 常见使用场景

### 员工登录和待检查列表获取

1. 员工登录

   ```http
   POST /staff/login
   Content-Type: application/json

   {
     "username": "john.doe",
     "password": "secure_password"
   }
   ```

2. 登录成功后，使用返回的员工 ID 获取待检查列表
   ```http
   GET /check/list?staffId=1
   ```
3. 应用显示待检查列表，区分"待员工检查"和"待主管确认"两部分

   - 普通员工只能看到自己楼层的物品
   - 主管可以看到所有楼层的物品

4. 员工执行检查

   ```http
   POST /check/staff?documentNo=D00001/1&line=3&staffId=1
   ```

5. 主管执行确认

   ```http
   POST /check/supervisor?documentNo=D00001/1&line=3&supervisorId=2
   ```

### 员工检查货物

- **URL**: `/check/staff`
- **方法**: `POST`
- **描述**: 标记货物为员工已检查（仅限员工负责的楼层）
- **查询参数**:
  - `documentNo`: 单据编号（支持 D00001/1 格式）
  - `line`: 明细行号
  - `staffId`: 员工ID
- **权限要求**:
  - 只有普通员工和BOM管理员可以执行此操作，主管不能执行员工检查
  - 普通员工只能检查自己负责楼层的货物
  - 对于非BOM物品（parent_code为NULL），普通员工检查后即可提交给主管验证
  - 对于BOM物品（有parent_code的物品），需要经过以下流程：
    1. 普通员工进行初步检查
    2. BOM管理员进行二次检查
    3. 提交给主管进行最终验证
  - BOM管理员可以对所有楼层的BOM物品进行检查
- **响应**:
  ```json
  {
    "success": true,
    "message": "Item checked by staff john.doe (requires BOM specialist check)",
    "detail": {
      "id": 1,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "3-1-G001-1",
      "parent_code": "BOM001",
      "staff_checked": true,
      "staff_checked_at": "2025-04-08T22:05:10.123Z",
      "staff_id": 1,
      "supervisor_checked": false,
      "supervisor_checked_at": null,
      "supervisor_id": null,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1,
      "main": {
        "id": 1,
        "document_no": "D00001",
        "document_date": "2025-04-06T16:00:00.000Z",
        "customer": "A0001",
        "customer_name": "Atas Com",
        "remarks": "ABC",
        "created_at": "2025-04-08T21:57:46.248Z"
      },
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com"
    }
  }
  ```

### BOM管理员检查物品

- **URL**: `/check/staff`
- **方法**: `POST`
- **描述**: BOM管理员对BOM物品进行二次检查（使用与普通员工相同的API）
- **查询参数**:
  - `documentNo`: 单据编号（支持 D00001/1 格式）
  - `line`: 明细行号
  - `staffId`: BOM管理员的ID
- **权限要求**:
  - 仅限BOM管理员(staff_bom)权限用户使用
  - 只能检查已被普通员工初步检查过的BOM物品(parent_code不为NULL)
  - BOM管理员可以检查所有楼层的BOM物品
- **响应**:
  ```json
  {
    "success": true,
    "message": "Item checked by BOM specialist bob.bom",
    "detail": {
      "id": 1,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "3-1-G001-1",
      "parent_code": "BOM001",
      "staff_checked": true,
      "staff_checked_at": "2025-04-08T21:05:10.123Z",
      "staff_id": 1,
      "bom_specialist_checked": true,
      "bom_specialist_checked_at": "2025-04-08T22:05:10.123Z",
      "bom_specialist_id": 3,
      "supervisor_checked": false,
      "supervisor_checked_at": null,
      "supervisor_id": null,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1,
      "main": {
        "id": 1,
        "document_no": "D00001",
        "document_date": "2025-04-06T16:00:00.000Z",
        "customer": "A0001",
        "customer_name": "Atas Com",
        "remarks": "ABC",
        "created_at": "2025-04-08T21:57:46.248Z"
      },
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com"
    }
  }
  ```

### 订单出货流程

1. 检查订单是否可以出货（所有项目都已被主管或司机确认）

   ```http
   GET /check/document-ready?documentNo=D00001&staffId=2
   ```

2. 获取所有可以出货的订单列表

   ```http
   GET /check/ready-documents?staffId=2
   ```

3. 标记订单为已出货

   ```http
   POST /check/complete?documentNo=D00001&supervisorId=2&driverId=3
   ```

### 拒绝检查和通知流程

1. 主管或司机拒绝员工的检查

   ```http
   POST /check/reject?documentNo=D00001/1&line=3&supervisorId=2&reason=数量不符，请重新检查
   ```

2. 员工登录后检查未读通知列表

   ```http
   GET /notification/unread?userId=1
   ```

3. 员工阅读通知详情，了解被拒绝的原因

4. 员工标记通知为已读

   ```http
   POST /notification/read?id=123
   ```

5. 员工重新检查该物品并再次提交

   ```http
   POST /check/staff?documentNo=D00001/1&line=3&staffId=1
   ```

6. 主管或司机再次审核员工的检查结果

## 员工权限说明

### 员工级别 (level)

- `senior`: 主管级别
  - 可以检查所有楼层的货物
  - 可以验证其他员工已检查的货物
  - 可以查看所有楼层的待检查货物
  - 可以看到所有未被主管检查过的货物，无论是否已被员工检查过
  - 可以在出货时选择是否指派司机负责送货
- `drive`: 司机级别
  - 拥有与主管相同的楼层权限
  - 可以查看所有楼层的货物
  - 可以访问所有楼层
  - 可以像主管一样检查并确认货物
  - 可以像主管一样标记订单为已出货
  - 负责出货订单的送达确认
  - 可以标记由自己送达的订单为"已送达"，完成整个订单流程
- `staff_bom`: BOM 管理员级别
  - 拥有与主管相同的楼层权限
  - 可以查看所有楼层的货物
  - 可以访问所有楼层
  - 负责检查BOM物品（具有parent_code的物品）
  - 普通员工检查过的BOM物品需要经过BOM管理员二次检查
  - 只有BOM管理员确认后的BOM物品才能提交给主管检查
- `regular`: 普通员工级别
  - 只能检查自己负责楼层的货物
  - 不能验证其他员工已检查的货物
  - 只能查看自己负责楼层的待检查货物
  - 可以进行BOM物品的初步检查，但需要BOM管理员进行二次检查

### 楼层权限 (floor)

- 每个员工只能操作自己负责楼层的货物
- 楼层格式限定为以下选项：`1F`, `2F`, `3F`
- 主管、司机和 BOM 管理员可以使用`ALL`值表示有权操作所有楼层的货物
- 主管、司机和 BOM 管理员可以操作所有楼层的货物

### 货架位置编码 (bin_shelf_no)

- 格式为：`楼层-区域-货架-位置`，例如 `2-1-G001-1`
- 第一部分表示楼层编号（如 `2` 表示二楼）
- 系统通过第一个数字识别货物所在楼层
- 普通员工只能操作与自己负责楼层一致的货物
- 如果员工的 `floor` 是 "2F"，则只能操作 bin_shelf_no 以 "2-" 开头的货物

## 员工管理 API

### 获取所有员工

- **URL**: `/staff`
- **方法**: `GET`
- **描述**: 获取所有员工信息（不包含密码）
- **响应**:
  ```json
  [
    {
      "id": 1,
      "username": "john.doe",
      "level": "senior",
      "floor": "3F",
      "full_name": "John Doe",
      "status": "active",
      "last_login": "2023-08-01T09:00:00.000Z",
      "created_at": "2023-01-01T00:00:00.000Z"
    }
  ]
  ```

### 根据 ID 获取员工

- **URL**: `/staff/:id`
- **方法**: `GET`
- **描述**: 获取指定 ID 的员工信息（不包含密码）
- **参数**:
  - `id`: 员工 ID
- **响应**:
  ```json
  {
    "id": 1,
    "username": "john.doe",
    "level": "senior",
    "floor": "3F",
    "full_name": "John Doe",
    "status": "active",
    "last_login": "2023-08-01T09:00:00.000Z",
    "created_at": "2023-01-01T00:00:00.000Z"
  }
  ```

### 创建新员工

- **URL**: `/staff`
- **方法**: `POST`
- **描述**: 创建一个新员工
- **请求体**:
  ```json
  {
    "username": "jane.smith",
    "password": "secure_password",
    "level": "regular",
    "floor": "2F", // 普通员工必须是1F、2F或3F；主管、司机和BOM管理员可使用"ALL"表示所有楼层
    "full_name": "Jane Smith"
  }
  ```
- **响应**:
  ```json
  {
    "id": 2,
    "username": "jane.smith",
    "level": "regular",
    "floor": "2F",
    "full_name": "Jane Smith",
    "status": "active",
    "created_at": "2023-08-02T10:00:00.000Z"
  }
  ```

### 员工登录

- **URL**: `/staff/login`
- **方法**: `POST`
- **描述**: 验证员工凭据并返回员工信息
- **请求体**:
  ```json
  {
    "username": "john.doe",
    "password": "secure_password"
  }
  ```
- **响应**:
  ```json
  {
    "id": 1,
    "username": "john.doe",
    "level": "senior",
    "floor": "3F",
    "full_name": "John Doe",
    "status": "active",
    "last_login": "2023-08-02T11:00:00.000Z",
    "created_at": "2023-01-01T00:00:00.000Z"
  }
  ```

## 仓库检查 API

> **注意**：所有仓库检查相关接口都会返回明细记录与主表关联的信息，包括：
>
> - 完整的主表对象 `main`，包含所有主表字段
> - 单独的主表字段 `document_date`、`customer` 和 `customer_name`（为方便直接访问）
>
> 这些字段通过 `delivery_order_detail` 表的 `main_id` 字段与 `delivery_order_main` 表关联查询获得。

### 获取楼层检查统计数据

- **URL**: `/check/floor-stats/:floor`
- **方法**: `GET`
- **描述**: 获取指定楼层的检查统计数据
- **参数**:
  - `floor`: 楼层编号（例如 "1"、"2"、"3"）
- **响应**:
  ```json
  {
    "pendingStaffCheck": 5,
    "pendingSupervisorCheck": 3,
    "totalPending": 8
  }
  ```

### 获取员工待检查列表

- **URL**: `/check/list`
- **方法**: `GET`
- **描述**: 获取特定员工可检查的货物列表（基于楼层权限）
- **查询参数**:
  - `staffId`: 员工ID
- **权限要求**:
  - 普通员工只能看到自己负责楼层的待检查货物
  - BOM管理员可以看到所有楼层中已被普通员工检查过但需要BOM专员检查的BOM物品
  - 主管和司机可以看到所有楼层的待检查货物
  - 主管和司机可以看到所有未被主管检查过的货物，无论是否已被员工检查过
- **响应**:
  ```json
  {
    "pendingStaffCheck": [
      {
        "id": 7,
        "document_no": "D00001/1",
        "line": 1,
        "stock": "stock123",
        "description": "123",
        "quantity": "1.00",
        "bin_shelf_no": "3-2-G002-1",
        "parent_code": "BOM001",
        "staff_checked": false,
        "staff_checked_at": null,
        "staff_id": null,
        "supervisor_checked": false,
        "supervisor_checked_at": null,
        "supervisor_id": null,
        "created_at": "2025-04-08T21:57:46.273Z",
        "main_id": 5,
        "main": {
          "id": 5,
          "document_no": "D00001",
          "document_date": "2025-04-06T16:00:00.000Z",
          "customer": "A0001",
          "customer_name": "Atas Com",
          "remarks": "ABC",
          "created_at": "2025-04-08T21:57:46.248Z"
        },
        "document_date": "2025-04-06T16:00:00.000Z",
        "customer": "A0001",
        "customer_name": "Atas Com"
      }
    ],
    "pendingSupervisorCheck": [
      {
        "id": 8,
        "document_no": "D00002/1",
        "line": 1,
        "stock": "stock456",
        "description": "测试商品2",
        "quantity": "5.00",
        "bin_shelf_no": "2-1-G002-1",
        "parent_code": null,
        "staff_checked": true,
        "staff_checked_at": "2024-02-20T10:30:00Z",
        "staff_id": 1,
        "supervisor_checked": false,
        "supervisor_checked_at": null,
        "supervisor_id": null,
        "created_at": "2025-04-08T21:57:46.283Z",
        "main_id": 6,
        "main": {
          "id": 6,
          "document_no": "D00002",
          "document_date": "2025-04-07T16:00:00.000Z",
          "customer": "B0002",
          "customer_name": "Batas Com",
          "remarks": "DEF",
          "created_at": "2025-04-08T21:57:46.248Z"
        },
        "document_date": "2025-04-07T16:00:00.000Z",
        "customer": "B0002",
        "customer_name": "Batas Com"
      }
    ],
    "total": 2
  }
  ```

### 主管检查货物

- **URL**: `/check/supervisor`
- **方法**: `POST`
- **描述**: 标记货物为主管已确认
- **查询参数**:
  - `documentNo`: 单据编号（支持 D00001/1 格式）
  - `line`: 明细行号
  - `supervisorId`: 主管ID
- **权限要求**:
  - 主管级别和司机级别的用户可以执行此操作
  - 主管级别和司机级别用户可以确认所有楼层的货物
  - 必须先由员工检查过
  - 对于BOM物品（有parent_code的物品），必须由BOM管理员检查过才能由主管和司机确认
- **响应**:
  ```json
  {
    "success": true,
    "message": "Item confirmed by supervisor jane.smith",
    "detail": {
      "id": 1,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "3-1-G001-1",
      "parent_code": "BOM001",
      "staff_checked": true,
      "staff_checked_at": "2025-04-08T22:05:10.123Z",
      "staff_id": 1,
      "supervisor_checked": true,
      "supervisor_checked_at": "2025-04-08T22:10:15.456Z",
      "supervisor_id": 2,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1
    }
  }
  ```

### 拒绝员工检查

- **URL**: `/check/reject`
- **方法**: `POST`
- **描述**: 主管或司机拒绝员工的检查，重置员工检查状态并创建通知
- **查询参数**:
  - `documentNo`: 单据编号（支持 D00001/1 格式）
  - `line`: 明细行号
  - `supervisorId`: 主管ID
  - `reason`: 拒绝原因（可选）
- **权限要求**:
  - 主管级别和司机级别的用户可以执行此操作
  - 只能拒绝已经被员工检查过的货物
- **响应**:
  ```json
  {
    "success": true,
    "message": "Staff check for item D00001/1-1 has been rejected by supervisor jane.smith",
    "detail": {
      "id": 1,
      "document_no": "D00001/1",
      "line": 1,
      "stock": "stock123",
      "description": "测试货品1",
      "quantity": "10.00",
      "bin_shelf_no": "3-1-G001-1",
      "parent_code": "BOM001",
      "staff_checked": false,
      "staff_checked_at": null,
      "staff_id": null,
      "supervisor_checked": false,
      "supervisor_checked_at": null,
      "supervisor_id": null,
      "created_at": "2025-04-08T21:57:46.273Z",
      "main_id": 1
    },
    "notification": {
      "id": 1,
      "type": "reject",
      "message": "您对单据 D00001/1 第 1 行的检查被主管 jane.smith 退回，原因：数量不符，请重新检查",
      "recipient_id": 1,
      "sender_id": 2,
      "document_no": "D00001/1",
      "line": 1,
      "detail_id": 1,
      "read": false,
      "reject_reason": "数量不符，请重新检查",
      "created_at": "2025-04-08T22:15:30.789Z",
      "read_at": null
    }
  }
  ```

## 数据同步机制

### 同步流程更新

系统已更新数据同步机制，支持从 SC_Tran_Detail 表同步 BOM 数据：

1. **主表同步**:

   - 系统从 SQL Server 的 AR_DO_Main_tbl 获取新记录
   - 保存到 PostgreSQL 的 delivery_order_main 表

2. **明细表同步**:

   - 从 SQL Server 的 SC_Tran_Detail 表查询相关记录
   - 查询格式为`Document No LIKE 'D00001/%'`
   - 保留原始的 Document No 格式（如"D00001/1"）
   - 设置 main_id 字段关联到主表 ID

3. **BOM 信息处理**:

   - 保存 Parent Code 字段，表示 BOM 关系
   - NULL 值表示非 BOM 项目
   - 同一个 BOM 组的 items 共享相同的 Document No 前缀

4. **BOM 检查流程**

   - 非BOM物品：普通员工检查 → 主管确认
   - BOM物品：普通员工初步检查 → BOM管理员二次检查 → 主管确认
   - BOM管理员可以查看所有已被普通员工检查过但未被BOM管理员检查的BOM物品

5. **特殊记录处理**

   - 对于bin_shelf_no为空的记录，跳过普通员工检查，直接由主管确认
   - 对于have_stock=false的记录，同样跳过普通员工检查，直接由主管确认
   - 这些特殊记录在主管的待确认列表中会有特殊标识
   - 在前端UI中，bin_shelf_no为空的记录会显示为"未分配货架"并使用橙色标记

6. **司机送货流程**

   - 订单出货后：主管确认出货(可选择指派司机) → [如指派司机]司机送货 → 司机确认送达
   - 若无指派司机，订单在出货后直接完成
   - 若指派司机，订单需要司机确认送达后才算完成
   - 主表添加字段：
     - `delivered`: 布尔值，表示订单是否已送达
     - `delivered_at`: 日期时间，表示送达时间
     - `delivered_by`: 整数，表示确认送达的司机ID
     - `delivery_notes`: 字符串，表示送达备注

7. **同步范围**:

   - 基于DocumentNo顺序进行同步，不再基于时间
   - 系统启动时会检查PostgreSQL中最新记录的DocumentNo
   - 如果数据库为空，则从SQL Server中获取最新的DocumentNo作为起点
   - 所有大于最后同步DocumentNo的记录都将被同步

### 自动同步

系统每分钟自动检查一次 SQL Server 中的新记录，并将它们同步到 PostgreSQL 数据库。同步基于以下逻辑：

1. **时区处理**:

   - 系统自动调整为马来西亚时区（UTC+8）
   - 所有日期操作都考虑时区差异

2. **同步范围**:

   - 基于DocumentNo顺序进行同步，不再基于时间
   - 系统启动时会检查PostgreSQL中最新记录的DocumentNo
   - 如果数据库为空，则从SQL Server中获取最新的DocumentNo作为起点
   - 所有大于最后同步DocumentNo的记录都将被同步

3. **同步标识**:

   - 使用文档日期(`DocumentDate`)和文档编号(`DocumentNo`)组合作为唯一标识
   - 确保不会重复同步相同记录

4. **特殊记录处理**:

   - 系统不会跳过货架号(`bin_shelf_no`)为空或空白的记录，而是将它们保存到PostgreSQL中
   - 对于bin_shelf_no为空的记录，系统会将它们直接分配给主管(Supervisor)处理，无需经过普通员工检查
   - 这些记录会在日志中记录警告信息，但不会影响其他记录的同步
   - 在前端UI中，这些记录会显示为"未分配货架"并使用橙色标记

5. **错误处理**:
   - 包含完整的错误处理机制
   - 记录详细的错误日志
   - 单条记录同步失败不会影响其他记录的处理

### 手动同步

除了自动同步外，系统还提供手动触发同步的 API：

- **URL**: `/sync`
- **方法**: `POST`
- **描述**: 手动触发数据同步流程
- **响应**:
  ```json
  {
    "message": "同步完成",
    "results": {
      "mainRecords": 5,
      "detailRecords": 10
    }
  }
  ```

## 通知系统 API

通知系统已更新，使用 detail_id 而不是复合键(document_no, line)来引用 detail 记录。该变更对 API 接口不造成变化，但增强了系统内部数据完整性。

### 获取用户未读通知

- **URL**: `/notification/unread/:userId`
- **方法**: `GET`
- **描述**: 获取用户未读的通知
- **参数**:
  - `userId`: 用户 ID
- **响应**:
  ```json
  [
    {
      "id": 1,
      "type": "reject",
      "message": "您对单据 D00001/1 第 1 行的检查被主管 jane.smith 退回，原因：数量不符，请重新检查",
      "recipient_id": 1,
      "sender_id": 2,
      "document_no": "D00001/1",
      "line": 1,
      "detail_id": 1,
      "read": false,
      "reject_reason": "数量不符，请重新检查",
      "created_at": "2025-05-15T10:15:30.123Z",
      "read_at": null
    }
  ]
  ```

### 获取用户所有通知

- **URL**: `/notification/all/:userId`
- **方法**: `GET`
- **描述**: 获取用户所有通知（已读和未读）
- **参数**:
  - `userId`: 用户 ID
- **响应**:
  ```json
  [
    {
      "id": 1,
      "type": "reject",
      "message": "您对单据 D00001/1 第 1 行的检查被主管 jane.smith 退回，原因：数量不符，请重新检查",
      "recipient_id": 1,
      "sender_id": 2,
      "document_no": "D00001/1",
      "line": 1,
      "detail_id": 1,
      "read": true,
      "reject_reason": "数量不符，请重新检查",
      "created_at": "2025-05-15T10:15:30.123Z",
      "read_at": "2025-05-15T10:20:45.789Z"
    }
  ]
  ```

### 标记通知为已读

- **URL**: `/notification/read/:id`
- **方法**: `POST`
- **描述**: 标记单个通知为已读
- **参数**:
  - `id`: 通知 ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "通知已标记为已读"
  }
  ```

### 标记所有通知为已读

- **URL**: `/notification/read-all`
- **方法**: `POST`
- **描述**: 标记用户所有未读通知为已读
- **查询参数**:
  - `userId`: 用户ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "所有通知已标记为已读",
    "count": 5
  }
  ```

## 订单出货 API

### 获取待出货单据列表

- **URL**: `/shipping/list`
- **方法**: `GET`
- **描述**: 获取待出货的订单列表（仅限主管和司机）
- **查询参数**:
  - `supervisorId`: 主管ID
- **权限要求**:
  - 只有主管级别和司机级别的用户可以执行此操作
- **响应**:
  ```json
  [
    {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "total_items": 5,
      "ready": true
    }
  ]
  ```

### 执行单据出货操作

- **URL**: `/shipping/ship`
- **方法**: `POST`
- **描述**: 执行单据出货操作（仅限主管和司机）
- **查询参数**:
  - `documentNo`: 单据编号（只支持主表单据编号，如"D00001"）
  - `supervisorId`: 主管ID
- **权限要求**:
  - 只有主管级别和司机级别的用户可以执行此操作
- **响应**:
  ```json
  {
    "success": true,
    "message": "Document D00001 shipped successfully",
    "document": {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "shipped": true,
      "shipped_at": "2025-04-08T23:10:15.456Z",
      "shipped_by": 2
    }
  }
  ```

### 获取司机待送达订单列表

- **URL**: `/shipping/delivery-list`
- **方法**: `GET`
- **描述**: 获取待送达的订单列表（仅限司机）
- **查询参数**:
  - `driverId`: 司机ID
- **权限要求**:
  - 只有司机级别的用户可以执行此操作
- **响应**:
  ```json
  [
    {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "shipped": true,
      "shipped_at": "2025-04-08T23:10:15.456Z",
      "shipped_by": 3,
      "delivered": false,
      "delivered_at": null
    }
  ]
  ```

### 获取主管指派的订单列表

- **URL**: `/shipping/supervisor-shipments`
- **方法**: `GET`
- **描述**: 获取主管出货并指派给司机的订单列表及其送达状态（仅限主管）
- **查询参数**:
  - `supervisorId`: 主管ID
- **权限要求**:
  - 只有主管级别的用户可以执行此操作
- **响应**:
  ```json
  [
    {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "shipped": true,
      "shipped_at": "2025-04-08T23:10:15.456Z",
      "shipped_by": 3,
      "driver_username": "tom.driver",
      "delivered": true,
      "delivered_at": "2025-04-09T14:25:33.789Z",
      "delivered_by": 3,
      "delivery_notes": "已送达客户仓库，验收完毕"
    },
    {
      "document_no": "D00002",
      "document_date": "2025-04-07T16:00:00.000Z",
      "customer": "B0002",
      "customer_name": "Batas Com",
      "shipped": true,
      "shipped_at": "2025-04-09T10:20:30.456Z",
      "shipped_by": 4,
      "driver_username": "alex.driver",
      "delivered": false,
      "delivered_at": null,
      "delivered_by": null,
      "delivery_notes": null
    }
  ]
  ```

### 确认订单送达

- **URL**: `/shipping/confirm-delivery`
- **方法**: `POST`
- **描述**: 司机确认订单已送达（仅限司机）
- **查询参数**:
  - `documentNo`: 单据编号（只支持主表单据编号，如"D00001"）
  - `driverId`: 司机ID
  - `notes`: 送达备注（可选）
- **权限要求**:
  - 只有司机级别的用户可以执行此操作
  - 只能确认指派给自己(shipped_by字段等于司机ID)的订单
  - 订单必须处于"已出货"但未"已送达"状态
- **响应**:
  ```json
  {
    "success": true,
    "message": "Document D00001 has been delivered by driver tom.smith",
    "document": {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "shipped": true,
      "shipped_at": "2025-04-08T23:10:15.456Z",
      "shipped_by": 3,
      "delivered": true,
      "delivered_at": "2025-04-09T14:25:33.789Z",
      "delivered_by": 3,
      "delivery_notes": "已送达客户仓库，验收完毕"
    }
  }
  ```

### 检查订单是否可以出货

- **URL**: `/check/document-ready`
- **方法**: `GET`
- **描述**: 检查订单是否可以出货（所有项目都已被主管或司机确认）
- **查询参数**:
  - `documentNo`: 单据编号（只支持主表单据编号，如"D00001"）
  - `staffId`: 员工ID（主管或司机）
- **权限要求**:
  - 只有主管级别和司机级别的用户可以执行此操作
- **响应**:
  ```json
  {
    "ready": true,
    "total": 5,
    "checked": 5,
    "message": "Document is ready for shipping"
  }
  ```

### 获取所有可出货订单

- **URL**: `/check/ready-documents`
- **方法**: `GET`
- **描述**: 获取所有可以出货的订单列表（所有项目都已被主管或司机确认）
- **查询参数**:
  - `staffId`: 员工ID（主管或司机）
- **权限要求**:
  - 只有主管级别和司机级别的用户可以执行此操作
- **响应**:
  ```json
  [
    {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "total_items": 5,
      "checked_items": 5
    }
  ]
  ```

### 标记订单为已出货

- **URL**: `/check/complete`
- **方法**: `POST`
- **描述**: 标记整个订单为已出货，可选择指派司机负责送货，或者司机自己指派自己出货
- **查询参数**:
  - `documentNo`: 单据编号（只支持主表单据编号，如"D00001"）
  - `supervisorId`: 主管ID
  - `driverId`: 司机ID
- **权限要求**:
  - 只有主管级别或司机级别的用户可以执行此操作
  - 主管和司机都可以标记订单为已出货
  - 订单所有物品必须已通过主管或司机确认
  - 如指定司机，该司机必须存在且具有 drive 权限
  - 司机也可以自己出货并负责送达，此时supervisorId和driverId可以是同一个ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "Document D00001 has been marked as shipped by jane.smith and assigned to driver tom.driver for delivery",
    "document": {
      "document_no": "D00001",
      "document_date": "2025-04-06T16:00:00.000Z",
      "customer": "A0001",
      "customer_name": "Atas Com",
      "is_shipped": true,
      "shipped_at": "2025-04-08T23:10:15.456Z",
      "shipped_by": 3,
      "delivered": false,
      "delivered_at": null,
      "delivered_by": null
    }
  }
  ```

## 司机拒绝分配的订单

**URL**: `/check/reject-delivery`

**Method**: `POST`

**Description**: 司机拒绝已分配的送货订单，将订单还原为未出货状态

**Query Parameters**:

| 参数       | 类型   | 描述     | 必填 |
| ---------- | ------ | -------- | ---- |
| documentNo | string | 文档编号 | 是   |
| driverId   | number | 司机ID   | 是   |
| reason     | string | 拒绝原因 | 否   |

**Permission Requirements**:

- 只有司机可以拒绝订单
- 司机只能拒绝分配给自己的订单
- 订单必须是已出货状态才能被拒绝

**Response**:

```json
{
  "success": true,
  "message": "Document D00001 has been rejected by driver tom.driver and reset to unshipped state",
  "document": {
    "document_no": "D00001",
    "is_shipped": false,
    "rejected_by": 3,
    "rejection_reason": "车辆维修无法送达"
  }
}
```

## 订单出货流程图

订单处理完整流程如下:

```
货物同步 → 员工检查 → [BOM物品需BOM专员检查] → 主管或者司机确认 →
→ 出货 → [可选：指派司机] → [如有司机：司机送货确认] → 订单完成
```

**说明**:

1. 括号 `[]` 内为可选步骤
2. 如果出货时未指派司机，订单在出货后直接完成
3. 如果出货时指派了司机，则订单需要司机确认送达后才算完成
