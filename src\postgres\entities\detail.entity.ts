import { <PERSON>ti<PERSON>, Column, PrimaryGeneratedC<PERSON>umn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Main } from './main.entity';

@Entity('delivery_order_detail')
export class Detail {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '文档编号', example: 'D00001/1' })
  @Column({ type: 'varchar', length: 50 })
  document_no: string;

  @ApiProperty({ description: '主表ID', example: 1 })
  @Column({ nullable: true })
  main_id: number;

  @ApiProperty({ description: '行号', example: 1 })
  @Column()
  line: number;

  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @Column()
  stock: string;

  @ApiProperty({ description: '产品描述', example: '办公用品' })
  @Column()
  description: string;

  @ApiProperty({ description: '零件号', example: 'PART001', nullable: true })
  @Column({ nullable: true })
  part_no: string;

  @ApiProperty({ description: '数量', example: 10 })
  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @ApiProperty({ description: '计量单位', example: 'PCS' })
  @Column()
  uom: string;

  @ApiProperty({ description: '存放位置', example: '3-1-G001-1' })
  @Column()
  bin_shelf_no: string;

  @ApiProperty({ description: 'BOM的库存代码', example: 'BOM001', nullable: true })
  @Column({ nullable: true })
  parent_code: string;

  @ApiProperty({ description: '品牌代码', example: 'BRAND001', nullable: true })
  @Column({ nullable: true })
  brand_code: string;

  @ApiProperty({ description: '单价', example: 10.5, nullable: true })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  unit_price: number;

  @ApiProperty({ description: '总金额', example: 105, nullable: true })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  total_amount: number;

  @ApiProperty({ description: '普通员工勾选', example: false })
  @Column({ default: false })
  staff_checked: boolean;

  @ApiProperty({ description: '普通员工勾选时间', example: '2025-04-04T12:00:00Z' })
  @Column({ nullable: true })
  staff_checked_at: Date;

  @ApiProperty({ description: '勾选的普通员工ID', example: 1 })
  @Column({ nullable: true })
  staff_id: number;

  @ApiProperty({ description: 'BOM专员勾选', example: false })
  @Column({ default: false })
  bom_specialist_checked: boolean;

  @ApiProperty({ description: 'BOM专员勾选时间', example: '2025-04-04T12:00:00Z' })
  @Column({ nullable: true })
  bom_specialist_checked_at: Date;

  @ApiProperty({ description: '勾选的BOM专员ID', example: 3 })
  @Column({ nullable: true })
  bom_specialist_id: number;

  @ApiProperty({ description: '高级员工勾选', example: false })
  @Column({ default: false })
  supervisor_checked: boolean;

  @ApiProperty({ description: '高级员工勾选时间', example: '2025-04-04T12:00:00Z' })
  @Column({ nullable: true })
  supervisor_checked_at: Date;

  @ApiProperty({ description: '勾选的高级员工ID', example: 2 })
  @Column({ nullable: true })
  supervisor_id: number;

  @ApiProperty({ description: '是否有库存', example: true })
  @Column({ default: false })
  have_stock: boolean;

  @ApiProperty({ description: '是否为BOM父项', example: false })
  @Column({ default: false })
  is_bom_parent: boolean;

  @ApiProperty({ description: 'BOM父项ID', example: 1, nullable: true })
  @Column({ nullable: true })
  bom_parent_id: number;

  @ApiProperty({ description: 'BOM分组标识', example: 'D00001', nullable: true })
  @Column({ nullable: true, length: 50 })
  bom_group: string;

  @ApiProperty({ description: 'BOM子项是否全部完成检查（仅BOM父项使用）', example: false })
  @Column({ default: false })
  bom_children_completed: boolean;

  @ApiProperty({ description: '创建时间', example: '2025-04-04T12:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  // 提取基本文档编号，用于关联main表
  get baseDocumentNo(): string {
    if (this.document_no && this.document_no.includes('/')) {
      return this.document_no.split('/')[0];
    }
    return this.document_no;
  }

  @ManyToOne(() => Main, main => main.details, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  })
  @JoinColumn({ name: 'main_id' })
  main: Main;
}