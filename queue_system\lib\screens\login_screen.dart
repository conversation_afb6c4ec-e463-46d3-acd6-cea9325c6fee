import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/locale_provider.dart';
import 'package:queue_system/screens/home_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('login')),
        actions: [
          IconButton(
            icon: const Icon(Icons.language),
            tooltip: context.t('switch_language'),
            onPressed: () {
              final localeProvider =
                  Provider.of<LocaleProvider>(context, listen: false);
              localeProvider.toggleLocale();
            },
          ),
        ],
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 应用图标 - 使用圆角
                Center(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(32), // 半径 = 宽度/高度的一半
                    child: SizedBox(
                      width: 200,
                      height: 200,
                      child: Image.asset(
                        'assets/images/Icon_TaskPickr-removebg.png',
                        fit: BoxFit.cover, // 或 BoxFit.contain 看你需求
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                TextFormField(
                  controller: _usernameController,
                  decoration: InputDecoration(
                    labelText: context.t('username'),
                    border: const OutlineInputBorder(),
                    hintText: context.t('enter_username_no_spaces'),
                  ),
                  // 在输入时就过滤空格
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(RegExp(r'\s')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.t('please_enter_username');
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _passwordController,
                  decoration: InputDecoration(
                    labelText: context.t('password'),
                    border: const OutlineInputBorder(),
                    hintText: context.t('enter_password_no_spaces'),
                  ),
                  obscureText: true,
                  // 在输入时就过滤空格
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(RegExp(r'\s')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.t('please_enter_password');
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    if (authProvider.isLoading) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (authProvider.error != null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(context.t(authProvider.error!)),
                            backgroundColor: Colors.red,
                            duration: const Duration(seconds: 3),
                          ),
                        );
                        authProvider.clearError();
                      });
                    }

                    return ElevatedButton(
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          await authProvider.login(
                            _usernameController.text,
                            _passwordController.text,
                          );

                          if (authProvider.isAuthenticated) {
                            if (!mounted) return;
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (context) => const HomeScreen(),
                              ),
                            );
                          }
                        }
                      },
                      child: Text(context.t('login')),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
