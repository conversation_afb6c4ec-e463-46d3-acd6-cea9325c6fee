import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Main } from '../postgres/entities/main.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { NotificationService } from '../notification/notification.service';

@Injectable()
export class ShippingService {
  constructor(
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    private readonly notificationService: NotificationService
  ) { }

  // 获取司机待送达订单列表
  async getDeliveryList(driverId: number): Promise<Main[]> {
    // 检查司机是否存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('司机不存在');
    }

    // 检查级别
    if (driver.level !== 'driver') {
      throw new ForbiddenException('只有司机可以查看此列表');
    }

    // 查找已出货、未送达、并且是由该司机负责的订单，限制数量避免性能问题
    const deliveryList = await this.mainRepository.find({
      where: {
        is_shipped: true,
        delivered: false,
        driver_id: driverId
      },
      order: {
        shipped_at: 'DESC'
      },
      take: 50 // 限制最多返回50个订单
    });

    return deliveryList;
  }

  // 获取主管指派的订单列表及其送达状态 - 支持搜索
  async getSupervisorShipments(supervisorId: number, page: number = 1, limit: number = 10, searchQuery?: string): Promise<{ data: any[], total: number, hasMore: boolean }> {
    // 检查主管是否存在
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    if (!supervisor) {
      throw new NotFoundException('主管不存在');
    }

    // 检查级别
    if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin' && supervisor.level !== 'driver') {
      throw new ForbiddenException('只有主管或司机可以查看此列表');
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 构建查询条件
    let whereCondition: any = { is_shipped: true };

    // 如果有搜索查询，添加搜索条件
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = `%${searchQuery.trim()}%`;
      whereCondition = [
        { is_shipped: true, document_no: Like(searchTerm) },
        { is_shipped: true, customer: Like(searchTerm) },
        { is_shipped: true, customer_name: Like(searchTerm) },
        { is_shipped: true, remarks: Like(searchTerm) }
      ];
    }

    // 获取符合条件的总数
    const total = await this.mainRepository.count({
      where: whereCondition
    });

    // 查找已出货的订单（分页）
    const shipmentsList = await this.mainRepository.find({
      where: whereCondition,
      order: {
        shipped_at: 'DESC' // 按出货时间降序排序
      },
      skip,
      take: limit
    });

    // 扩展结果，添加司机信息
    const shipmentsWithDriverInfo = await Promise.all(
      shipmentsList.map(async shipment => {
        // 如果有司机负责送货，获取司机用户名
        let driverUsername = null;
        if (shipment.driver_id) {
          const driver = await this.staffRepository.findOne({
            where: { id: shipment.driver_id },
            select: ['username', 'full_name']
          });
          if (driver) {
            driverUsername = driver.username;
          }
        }

        // 返回带有司机信息的对象
        return {
          ...shipment,
          driver_username: driverUsername
        };
      })
    );

    // 计算是否还有更多数据
    const hasMore = skip + shipmentsList.length < total;

    return {
      data: shipmentsWithDriverInfo,
      total,
      hasMore
    };
  }

  // 获取所有司机列表
  async getAllDrivers(): Promise<Staff[]> {
    // 查找所有level为drive且状态为active的员工
    const drivers = await this.staffRepository.find({
      where: {
        level: 'driver',
        status: 'active'
      },
      select: ['id', 'username', 'full_name', 'level', 'status', 'created_at', 'last_login']
    });

    return drivers;
  }

  // 确认订单送达
  async confirmDelivery(documentNo: string, driverId: number, notes: string): Promise<{ success: boolean, message: string, document: Main }> {
    // 检查文档是否存在
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 检查司机是否存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // 检查级别
    if (driver.level !== 'driver') {
      throw new ForbiddenException('Only drivers can confirm delivery');
    }

    // 检查订单状态
    if (!main.is_shipped) {
      throw new ForbiddenException(`Document ${documentNo} has not been shipped yet`);
    }

    // 检查是否已送达
    if (main.delivered) {
      throw new BadRequestException(`Document ${documentNo} has already been delivered`);
    }

    // 检查是否是由该司机负责出货的订单
    if (main.driver_id != driverId) {
      throw new ForbiddenException(`This document is not assigned to you. You cannot confirm delivery`);
    }

    // 标记为已送达
    main.delivered = true;
    main.delivered_at = new Date();
    main.delivered_by = driverId;
    main.delivery_notes = notes;

    const savedDocument = await this.mainRepository.save(main);

    // 创建通知
    await this.notificationService.createNotification({
      type: 'delivery_confirmed',
      message: `Driver ${driver.username} has confirmed delivery of document ${documentNo}. Notes: ${notes || 'None'}`,
      recipient_id: 1, // 发送给管理员
      sender_id: driverId,
      document_no: documentNo,
      line: 0,
      detail_id: 0
    });

    return {
      success: true,
      message: `Document ${documentNo} has been delivered by driver ${driver.username}`,
      document: savedDocument
    };
  }

  // 司机拒绝分配的订单，让订单回到未出货的状态
  async rejectDelivery(documentNo: string, driverId: number, reason: string) {
    // 验证司机存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // 验证司机权限
    if (driver.level !== 'driver') {
      throw new ForbiddenException('Only drivers can reject deliveries');
    }

    // 查找对应的主单据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 验证单据是否已出货
    if (!main.is_shipped) {
      throw new BadRequestException('This document has not been shipped yet');
    }

    // 验证是否是分配给这个司机的订单
    if (main.driver_id != driverId) {
      throw new ForbiddenException('You are not authorized to reject this delivery');
    }

    // 重置出货状态
    main.is_shipped = false;
    main.shipped_at = null;
    main.shipped_by = null;
    main.driver_id = null;
    main.rejection_reason = reason;
    main.rejected_by = driverId;
    main.rejected_at = new Date();

    // 保存更新
    await this.mainRepository.save(main);

    // 通知相关人员
    await this.notificationService.createNotification({
      type: 'delivery_rejected',
      message: `Driver ${driver.username} has rejected the delivery of document ${documentNo}. Reason: ${reason}`,
      recipient_id: main.shipped_by || 1, // 发给原出货人，如果为空则发给ID为1的默认管理员
      sender_id: driver.id,
      document_no: documentNo,
      line: 0,
      detail_id: 0,
      reject_reason: reason
    });

    // 返回结果
    return {
      success: true,
      message: `Document ${documentNo} has been rejected by driver ${driver.username} and reset to unshipped state`,
      document: {
        document_no: main.document_no,
        is_shipped: main.is_shipped,
        rejected_by: main.rejected_by,
        rejection_reason: main.rejection_reason
      }
    };
  }

  // 主管拒绝已完成检查但未出货的订单，重置所有detail记录为未检查状态
  async rejectShipment(documentNo: string, supervisorId: number, reason: string) {
    // 验证主管存在
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    if (!supervisor) {
      throw new NotFoundException('Supervisor not found');
    }

    // 验证主管权限
    if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin') {
      throw new ForbiddenException('Only supervisors can reject shipments');
    }

    // 查找对应的主单据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 验证单据是否未出货
    if (main.is_shipped) {
      throw new BadRequestException('This document has already been shipped. Cannot reject.');
    }

    // 查找所有相关的detail记录
    const details = await this.detailRepository.find({
      where: { main_id: main.id }
    });

    if (!details || details.length === 0) {
      throw new NotFoundException(`No details found for document ${documentNo}`);
    }

    // 只重置主管检查状态，保留staff和bom_specialist的检查状态
    for (const detail of details) {
      // 只重置主管检查状态
      detail.supervisor_checked = false;
      detail.supervisor_checked_at = null;
      detail.supervisor_id = null;
    }

    // 保存所有更新的detail记录
    await this.detailRepository.save(details);

    // 更新main记录的拒绝信息
    main.rejection_reason = reason;
    main.rejected_by = supervisorId;
    main.rejected_at = new Date();

    // 保存更新的main记录
    await this.mainRepository.save(main);

    // 创建通知给所有相关员工
    const uniqueStaffIds = new Set<number>();
    details.forEach(detail => {
      if (detail.staff_id) uniqueStaffIds.add(detail.staff_id);
      if (detail.bom_specialist_id) uniqueStaffIds.add(detail.bom_specialist_id);
    });

    // 为每个相关员工创建通知
    const notificationPromises = Array.from(uniqueStaffIds).map(staffId =>
      this.notificationService.createNotification({
        type: 'shipment_rejected',
        message: `Supervisor ${supervisor.username} has rejected the shipment of document ${documentNo}. Supervisor checks have been reset. Reason: ${reason}`,
        recipient_id: staffId,
        sender_id: supervisorId,
        document_no: documentNo,
        line: 0,
        detail_id: 0,
        reject_reason: reason
      })
    );

    await Promise.all(notificationPromises);

    // 返回结果
    return {
      success: true,
      message: `Document ${documentNo} has been rejected by supervisor ${supervisor.username}. Supervisor checks have been reset.`,
      document: {
        document_no: main.document_no,
        rejected_by: main.rejected_by,
        rejection_reason: main.rejection_reason
      }
    };
  }
}