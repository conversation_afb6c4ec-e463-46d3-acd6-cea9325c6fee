# PDF Fix Verification Test Script

# Import common path functions
. "$PSScriptRoot\common-paths.ps1"

Write-Host "=== PDF Fix Verification Test ===" -ForegroundColor Green

# Get project paths
$paths = Get-ProjectPaths

# Validate project structure
if (!(Test-ProjectStructure -Paths $paths)) {
    exit 1
}

# Test parameters
$testDocumentNo = "TEST001"

# 从配置文件中提取端口号
$port = "3000"  # 默认端口
$configFiles = @("ecosystem.production.config.js", "ecosystem.config.js")
foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        $configContent = Get-Content $configFile -Raw
        if ($configContent -match "PORT:\s*(\d+)") {
            $port = $matches[1]
            break
        }
    }
}
$baseUrl = "http://localhost:$port"

Write-Host "`n1. Check if service is running" -ForegroundColor Cyan
try {
    $healthCheck = Invoke-WebRequest -Uri "$baseUrl/api-docs" -Method GET -TimeoutSec 5
    if ($healthCheck.StatusCode -eq 200) {
        Write-Host "OK Service is running" -ForegroundColor Green
    }
} catch {
    Write-Host "ERROR Service not running or inaccessible" -ForegroundColor Red
    Write-Host "Please start the service first: npm run pm2:start" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n2. Test PDF generation API" -ForegroundColor Cyan
try {
    $generateResponse = Invoke-WebRequest -Uri "$baseUrl/pdf/generate-html?documentNo=$testDocumentNo" -Method POST -TimeoutSec 30
    if ($generateResponse.StatusCode -eq 200 -or $generateResponse.StatusCode -eq 201) {
        Write-Host "OK PDF generation API responded normally" -ForegroundColor Green
        $responseContent = $generateResponse.Content | ConvertFrom-Json
        Write-Host "  Response: $($responseContent.message)" -ForegroundColor Gray
    }
} catch {
    Write-Host "ERROR PDF generation API test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n3. Test PDF view API" -ForegroundColor Cyan
try {
    $viewResponse = Invoke-WebRequest -Uri "$baseUrl/pdf/view?documentNo=$testDocumentNo" -Method GET -TimeoutSec 30
    if ($viewResponse.StatusCode -eq 200) {
        Write-Host "OK PDF view API responded normally" -ForegroundColor Green
        Write-Host "  Content type: $($viewResponse.Headers.'Content-Type')" -ForegroundColor Gray
        Write-Host "  Content length: $($viewResponse.Headers.'Content-Length') bytes" -ForegroundColor Gray
    }
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode -eq 404) {
        Write-Host "ERROR PDF view API returned 404 - this is the issue we need to fix!" -ForegroundColor Red
    } else {
        Write-Host "ERROR PDF view API test failed (status code: $statusCode): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n4. Check if PDF file was actually generated" -ForegroundColor Cyan
$today = Get-Date -Format "yyyy-MM-dd"
$pdfPath = Join-Path $paths.PDFOutput "$today\$testDocumentNo.pdf"
if (Test-Path $pdfPath) {
    $fileInfo = Get-Item $pdfPath
    Write-Host "OK PDF file generated: $pdfPath" -ForegroundColor Green
    Write-Host "  File size: $($fileInfo.Length) bytes" -ForegroundColor Gray
    Write-Host "  Creation time: $($fileInfo.CreationTime)" -ForegroundColor Gray
} else {
    Write-Host "ERROR PDF file not found: $pdfPath" -ForegroundColor Red
}

Write-Host "`n5. Stress test - continuous requests" -ForegroundColor Cyan
$successCount = 0
$failCount = 0
$testCount = 5

for ($i = 1; $i -le $testCount; $i++) {
    try {
        $testDoc = "TEST00$i"
        Write-Host "  Test $i/$testCount : $testDoc" -NoNewline
        
        # First generate PDF
        Invoke-WebRequest -Uri "$baseUrl/pdf/generate-html?documentNo=$testDoc" -Method POST -TimeoutSec 15 | Out-Null
        
        # Then view PDF
        $response = Invoke-WebRequest -Uri "$baseUrl/pdf/view?documentNo=$testDoc" -Method GET -TimeoutSec 15
        
        if ($response.StatusCode -eq 200) {
            Write-Host " OK" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host " ERROR (status code: $($response.StatusCode))" -ForegroundColor Red
            $failCount++
        }
    } catch {
        Write-Host " ERROR (exception: $($_.Exception.Message))" -ForegroundColor Red
        $failCount++
    }
    
    Start-Sleep -Seconds 1
}

Write-Host "`n=== Test Results ===" -ForegroundColor Green
Write-Host "Success: $successCount/$testCount" -ForegroundColor Green
Write-Host "Failed: $failCount/$testCount" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "`nAll tests passed! PDF fix successful!" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "`nSome tests passed, there may be intermittent issues" -ForegroundColor Yellow
    Write-Host "Recommend running diagnostic script: npm run diagnose" -ForegroundColor Yellow
} else {
    Write-Host "`nAll tests failed, issue still exists" -ForegroundColor Red
    Write-Host "Please check:" -ForegroundColor Yellow
    Write-Host "1. PM2 process status: npm run pm2:status" -ForegroundColor Yellow
    Write-Host "2. View logs: npm run pm2:logs" -ForegroundColor Yellow
    Write-Host "3. Run diagnostics: npm run diagnose" -ForegroundColor Yellow
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
