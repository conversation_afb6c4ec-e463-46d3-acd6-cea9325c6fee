import 'dart:io';
import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

class NativePdfViewerScreen extends StatefulWidget {
  final String pdfUrl;
  final String title;

  const NativePdfViewerScreen({
    super.key,
    required this.pdfUrl,
    required this.title,
  });

  @override
  State<NativePdfViewerScreen> createState() => _NativePdfViewerScreenState();
}

class _NativePdfViewerScreenState extends State<NativePdfViewerScreen> {
  String? _localPath;
  bool _isLoading = true;
  String? _error;
  int _totalPages = 0;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _downloadPdf();
  }

  Future<void> _downloadPdf() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 获取临时目录
      final dir = await getTemporaryDirectory();
      // 创建文件名
      final fileName = '${widget.title.replaceAll('/', '_')}.pdf';
      final filePath = '${dir.path}/$fileName';

      // 检查文件是否已存在，如果存在则删除
      final file = File(filePath);
      if (await file.exists()) {
        try {
          await file.delete();
          debugPrint('删除旧的PDF文件: $filePath');
        } catch (e) {
          debugPrint('删除旧的PDF文件失败: $e');
        }
      }

      // 下载PDF文件
      final dio = Dio();
      await dio.download(
        widget.pdfUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            debugPrint('下载进度: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      setState(() {
        _localPath = filePath;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _downloadPdf,
            tooltip: context.t('refresh'),
          ),
        ],
      ),
      body: Stack(
        children: [
          if (_localPath != null)
            PDFView(
              filePath: _localPath!,
              enableSwipe: true,
              swipeHorizontal: false,
              autoSpacing: true,
              pageFling: true,
              pageSnap: true,
              onRender: (pages) {
                setState(() {
                  _totalPages = pages!;
                });
              },
              onError: (error) {
                setState(() {
                  _error = error.toString();
                });
              },
              onPageChanged: (page, total) {
                setState(() {
                  _currentPage = page!;
                  _totalPages = total!;
                });
              },
            ),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
          if (_error != null)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(context.t('loading_failed')),
                  const SizedBox(height: 8),
                  Text(_error!),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _downloadPdf,
                    child: Text(context.t('retry')),
                  ),
                ],
              ),
            ),
        ],
      ),
      bottomNavigationBar: _localPath != null &&
              _error == null &&
              _totalPages > 0
          ? Container(
              height: 50,
              color: Colors.grey.shade200,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _currentPage > 0 ? () {} : null,
                  ),
                  Text('${_currentPage + 1} / $_totalPages'),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: _currentPage < _totalPages - 1 ? () {} : null,
                  ),
                ],
              ),
            )
          : null,
    );
  }
}
