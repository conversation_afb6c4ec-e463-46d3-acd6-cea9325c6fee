---
title: "UI 组件说明"
description: "队列系统 Flutter 移动应用的可复用 UI 组件"
version: "1.0.0"
last_updated: "2025-05-19"
---

# UI 组件说明

## 概述

队列系统移动应用使用一系列自定义 UI 组件，确保界面风格统一、代码复用和易于维护。这些组件位于 `lib/widgets/` 目录下，按功能分类组织。

## 通用组件

### AppButton

标准按钮组件，支持多种样式和状态。

```dart
AppButton(
  text: '确认',
  onPressed: () => handleConfirm(),
  type: ButtonType.primary,
  isLoading: isLoading,
)
```

**属性**:
- `text`: 按钮文本
- `onPressed`: 点击回调函数
- `type`: 按钮类型 (primary, secondary, danger)
- `isLoading`: 加载状态
- `disabled`: 禁用状态
- `icon`: 可选图标

### AppCard

卡片容器组件，用于展示信息块。

```dart
AppCard(
  title: '订单详情',
  child: OrderDetails(order: order),
  onTap: () => navigateToDetails(),
)
```

**属性**:
- `title`: 卡片标题
- `child`: 卡片内容
- `onTap`: 点击回调函数
- `elevation`: 阴影高度
- `padding`: 内边距

### LoadingIndicator

加载指示器组件，用于显示加载状态。

```dart
LoadingIndicator(
  message: '正在加载...',
  color: Theme.of(context).primaryColor,
)
```

**属性**:
- `message`: 加载提示文本
- `color`: 指示器颜色
- `size`: 指示器大小

### EmptyState

空状态组件，当列表为空时显示。

```dart
EmptyState(
  icon: Icons.inbox,
  message: '没有待检查的货物',
  actionText: '刷新',
  onAction: () => refreshList(),
)
```

**属性**:
- `icon`: 显示图标
- `message`: 提示文本
- `actionText`: 操作按钮文本
- `onAction`: 操作回调函数

## 业务组件

### OrderListItem

订单列表项组件，用于显示订单摘要信息。

```dart
OrderListItem(
  order: order,
  onTap: () => navigateToOrderDetails(order),
  showStatus: true,
)
```

**属性**:
- `order`: 订单数据模型
- `onTap`: 点击回调函数
- `showStatus`: 是否显示状态标签
- `showActions`: 是否显示操作按钮

### CheckListItem

检查列表项组件，用于显示待检查货物信息。

```dart
CheckListItem(
  detail: detail,
  onCheck: () => checkItem(detail),
  isBOM: detail.parentCode != null,
)
```

**属性**:
- `detail`: 明细数据模型
- `onCheck`: 检查回调函数
- `isBOM`: 是否为 BOM 物品
- `isChecked`: 是否已检查

### SignaturePad

签名板组件，用于获取客户签名。

```dart
SignaturePad(
  onSigned: (signatureData) => handleSignature(signatureData),
  width: double.infinity,
  height: 200,
)
```

**属性**:
- `onSigned`: 签名完成回调函数
- `width`: 签名板宽度
- `height`: 签名板高度
- `backgroundColor`: 背景颜色
- `strokeColor`: 笔画颜色

### NotificationItem

通知列表项组件，用于显示通知信息。

```dart
NotificationItem(
  notification: notification,
  onTap: () => handleNotification(notification),
  onMarkAsRead: () => markAsRead(notification.id),
)
```

**属性**:
- `notification`: 通知数据模型
- `onTap`: 点击回调函数
- `onMarkAsRead`: 标记已读回调函数

## 布局组件

### AppScaffold

应用脚手架组件，提供统一的页面布局。

```dart
AppScaffold(
  title: '待检查列表',
  body: CheckListScreen(),
  showBackButton: true,
  actions: [
    IconButton(icon: Icon(Icons.refresh), onPressed: () => refreshData()),
  ],
)
```

**属性**:
- `title`: 页面标题
- `body`: 页面内容
- `showBackButton`: 是否显示返回按钮
- `actions`: 应用栏操作按钮
- `floatingActionButton`: 浮动操作按钮

### TabLayout

标签页布局组件，用于切换不同内容视图。

```dart
TabLayout(
  tabs: ['待检查', '已检查', '已拒绝'],
  children: [
    PendingCheckList(),
    CheckedList(),
    RejectedList(),
  ],
)
```

**属性**:
- `tabs`: 标签页标题列表
- `children`: 标签页内容列表
- `initialIndex`: 初始选中索引
- `onTabChanged`: 标签页切换回调函数

## 表单组件

### AppTextField

文本输入框组件，支持各种输入类型和验证。

```dart
AppTextField(
  label: '用户名',
  hint: '请输入用户名',
  controller: usernameController,
  validator: (value) => value.isEmpty ? '用户名不能为空' : null,
  prefixIcon: Icons.person,
)
```

**属性**:
- `label`: 输入框标签
- `hint`: 提示文本
- `controller`: 文本控制器
- `validator`: 验证函数
- `prefixIcon`: 前缀图标
- `suffixIcon`: 后缀图标
- `obscureText`: 是否隐藏文本（用于密码）

### AppDropdown

下拉选择框组件，用于从列表中选择项目。

```dart
AppDropdown<int>(
  label: '选择司机',
  items: drivers.map((d) => DropdownItem(value: d.id, text: d.fullName)).toList(),
  value: selectedDriverId,
  onChanged: (value) => setSelectedDriver(value),
)
```

**属性**:
- `label`: 选择框标签
- `items`: 选项列表
- `value`: 当前选中值
- `onChanged`: 选择变更回调函数
- `hint`: 提示文本

### SearchBar

搜索栏组件，用于过滤列表内容。

```dart
SearchBar(
  hint: '搜索订单号或客户名称',
  onSearch: (query) => filterOrders(query),
  debounceTime: Duration(milliseconds: 300),
)
```

**属性**:
- `hint`: 提示文本
- `onSearch`: 搜索回调函数
- `debounceTime`: 防抖时间
- `initialValue`: 初始搜索文本

## 使用指南

1. **组件导入**：使用相对路径导入组件
   ```dart
   import '../widgets/app_button.dart';
   ```

2. **主题集成**：组件自动使用应用主题，确保一致的视觉风格
   ```dart
   // 在 main.dart 中设置主题
   MaterialApp(
     theme: ThemeData(
       primaryColor: Colors.blue,
       // 其他主题设置
     ),
     // ...
   )
   ```

3. **响应式设计**：组件支持响应式布局，适应不同屏幕尺寸
   ```dart
   // 使用 MediaQuery 获取屏幕尺寸
   final screenWidth = MediaQuery.of(context).size.width;
   ```

4. **组件扩展**：可以通过继承基础组件创建特定业务需求的组件
   ```dart
   class DriverSelectionDropdown extends StatelessWidget {
     // 实现特定业务逻辑的下拉框
   }
   ```
