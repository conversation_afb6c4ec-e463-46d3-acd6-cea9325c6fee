module.exports = {
  apps: [
    {
      name: 'backend-nestjs',
      script: 'dist/main.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        // 确保工作目录正确
        PWD: 'D:\\Project\\QueueSystem\\backend_nestjs',
        // PDF输出目录
        PDF_OUTPUT_DIR: 'D:\\Project\\QueueSystem\\backend_nestjs\\PDF_Output',

        // 🔧 开发环境数据库配置 - 直接在这里设置，不依赖.env
        // 注意：PM2启动时不会自动读取.env文件！
        SQL_SERVER_HOST: 'DESKTOP-BIVU6SG',
        SQL_SERVER_PORT: '1433',
        SQL_SERVER_USERNAME: 'Test',
        SQL_SERVER_PASSWORD: '1234',
        SQL_SERVER_DATABASE: 'Demo',
        POSTGRES_HOST: 'localhost',
        POSTGRES_PORT: '5432',
        POSTGRES_USERNAME: 'postgres',
        POSTGRES_PASSWORD: 'postgres',
        POSTGRES_DATABASE: 'demo',

        // 📧 开发环境邮件配置
        EMAIL_HOST: 'mail.pentatech.com.my',
        EMAIL_PORT: '465',
        EMAIL_SECURE: 'true',
        EMAIL_USER: '<EMAIL>',
        EMAIL_PASS: 'Lee@02101216',
        EMAIL_FROM: '<EMAIL>'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        PWD: 'D:\\Project\\QueueSystem\\backend_nestjs',
        PDF_OUTPUT_DIR: 'D:\\Project\\QueueSystem\\backend_nestjs\\PDF_Output'
      },
      // 设置工作目录
      cwd: 'D:\\Project\\QueueSystem\\backend_nestjs',
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      // 自动重启配置
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      // 进程管理
      kill_timeout: 5000,
      listen_timeout: 8000,
      // Windows特定配置
      windowsHide: true,
      // 忽略监听文件变化
      ignore_watch: [
        'node_modules',
        'logs',
        'PDF_Output',
        '.git'
      ],
      // 合并日志
      merge_logs: true,
      // 时间戳
      time: true
    }
  ]
};
