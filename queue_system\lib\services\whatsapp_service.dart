import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:queue_system/l10n/app_localizations.dart';

class WhatsAppService {
  /// 打开WhatsApp并发送消息
  /// [phoneNumber] 目标手机号码（包含国家代码，如 +60123456789）
  /// [message] 要发送的消息内容
  /// 返回是否成功打开WhatsApp
  static Future<bool> sendMessage({
    required String phoneNumber,
    required String message,
  }) async {
    try {
      // 清理手机号码，移除所有非数字字符（除了+号）
      String cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // 确保手机号码格式正确
      if (!cleanedPhone.startsWith('+')) {
        debugPrint('WhatsApp服务 - 手机号码格式错误: $phoneNumber');
        return false;
      }

      // URL编码消息内容
      String encodedMessage = Uri.encodeComponent(message);

      // 构建WhatsApp URL
      // 格式: https://wa.me/{phone_number}?text={encoded_message}
      String whatsappUrl = 'whatsapp://send?$cleanedPhone?text=$encodedMessage';

      debugPrint('WhatsApp服务 - 构建的URL: $whatsappUrl');

      final uri = Uri.parse(whatsappUrl);

      // 检查是否可以启动URL
      bool canLaunch = await canLaunchUrl(uri);
      debugPrint('WhatsApp服务 - 可以启动URL: $canLaunch');

      if (canLaunch) {
        // 使用外部应用程序模式启动WhatsApp
        bool launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        debugPrint('WhatsApp服务 - 启动结果: $launched');
        return launched;
      } else {
        // 如果无法启动，尝试使用通用的WhatsApp Web链接
        debugPrint('WhatsApp服务 - 无法启动应用，尝试WhatsApp Web');
        return await _launchWhatsAppWeb(cleanedPhone, encodedMessage);
      }
    } catch (e) {
      debugPrint('WhatsApp服务 - 发送消息时发生错误: $e');
      return false;
    }
  }

  /// 尝试使用WhatsApp Web作为备用方案
  static Future<bool> _launchWhatsAppWeb(
      String phoneNumber, String encodedMessage) async {
    try {
      String webUrl =
          'https://web.whatsapp.com/send?phone=$phoneNumber&text=$encodedMessage';
      final uri = Uri.parse(webUrl);

      debugPrint('WhatsApp服务 - 尝试WhatsApp Web: $webUrl');

      return await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      debugPrint('WhatsApp服务 - WhatsApp Web启动失败: $e');
      return false;
    }
  }

  /// 验证手机号码格式
  /// [phoneNumber] 要验证的手机号码
  /// 返回是否为有效格式
  static bool isValidPhoneNumber(String phoneNumber) {
    // 移除所有空格和特殊字符，只保留数字和+号
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // 基本的手机号码格式验证：以+开头，后跟10-15位数字
    final phoneRegex = RegExp(r'^\+\d{10,15}$');
    return phoneRegex.hasMatch(cleaned);
  }

  /// 格式化手机号码为标准格式
  /// [phoneNumber] 原始手机号码
  /// [defaultCountryCode] 默认国家代码（如 +60 for Malaysia）
  /// 返回格式化后的手机号码
  static String formatPhoneNumber(String phoneNumber,
      {String defaultCountryCode = '+60'}) {
    // 移除所有空格和特殊字符，只保留数字和+号
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // 如果不是以+开头，添加默认国家代码
    if (!cleaned.startsWith('+')) {
      // 移除前导0（如果存在）
      if (cleaned.startsWith('0')) {
        cleaned = cleaned.substring(1);
      }
      cleaned = '$defaultCountryCode$cleaned';
    }

    return cleaned;
  }

  /// 检查设备是否安装了WhatsApp
  /// 注意：这个方法在某些平台上可能不准确
  static Future<bool> isWhatsAppInstalled() async {
    try {
      // 尝试构建一个简单的WhatsApp URL
      final uri = Uri.parse('https://wa.me/');
      return await canLaunchUrl(uri);
    } catch (e) {
      debugPrint('WhatsApp服务 - 检查WhatsApp安装状态时出错: $e');
      return false;
    }
  }

  /// 显示WhatsApp未安装的提示对话框
  static void showWhatsAppNotInstalledDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.warning, color: Colors.orange),
              const SizedBox(width: 8),
              Text(context.t('whatsapp_not_installed')),
            ],
          ),
          content: Text(context.t('whatsapp_not_installed_message')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.t('got_it')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 可以在这里添加跳转到应用商店下载WhatsApp的逻辑
                _openAppStore();
              },
              child: Text(context.t('download_whatsapp')),
            ),
          ],
        );
      },
    );
  }

  /// 打开应用商店下载WhatsApp
  static Future<void> _openAppStore() async {
    try {
      // Android Play Store
      const androidUrl =
          'https://play.google.com/store/apps/details?id=com.whatsapp';
      // iOS App Store
      const iosUrl =
          'https://apps.apple.com/app/whatsapp-messenger/id310633997';

      // 这里简化处理，直接使用Android链接
      // 在实际项目中，可以根据平台选择不同的链接
      final uri = Uri.parse(androidUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('WhatsApp服务 - 打开应用商店失败: $e');
    }
  }
}
