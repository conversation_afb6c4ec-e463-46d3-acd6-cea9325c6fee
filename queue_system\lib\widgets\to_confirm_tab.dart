import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/widgets/check_item_card.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';
import 'package:queue_system/utils/search_utils.dart';

class ToConfirmTab extends StatefulWidget {
  final List<CheckItem> items;
  final int staffId;
  final bool isSenior;
  final bool isBomSpecialist;

  // 确认操作回调函数
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;

  const ToConfirmTab({
    super.key,
    required this.items,
    required this.staffId,
    required this.isSenior,
    required this.isBomSpecialist,
    required this.onConfirmAction,
  });

  @override
  State<ToConfirmTab> createState() => _ToConfirmTabState();
}

class _ToConfirmTabState extends State<ToConfirmTab> {
  // 用于保持滚动位置
  final ScrollController _scrollController = ScrollController();

  // 用于保存当前展开的文档组
  final Set<String> _expandedGroups = <String>{};

  // 用于保存精确的位置信息
  double? _savedScrollOffset;
  String? _currentOperatingItemKey;
  bool _isRestoring = false;

  // 用于保存 BOM 子项目的精确位置信息
  final Map<String, GlobalKey> _itemKeys = {};
  final Map<String, double> _itemOffsets = {};

  // 搜索相关状态
  String _searchQuery = '';
  List<CheckItem> _filteredItems = [];
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    _updateFilteredItems();
  }

  @override
  void didUpdateWidget(ToConfirmTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _updateFilteredItems();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 更新过滤后的项目列表
  void _updateFilteredItems() {
    setState(() {
      _filteredItems = SearchUtils.searchCheckItems(widget.items, _searchQuery);
    });
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 立即更新搜索查询状态（用于UI显示）
    setState(() {
      _searchQuery = query;
    });

    // 设置新的防抖定时器
    _searchDebounceTimer = Timer(const Duration(seconds: 1), () {
      // 1秒后执行搜索
      _updateFilteredItems();
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    setState(() {
      _searchQuery = '';
      _updateFilteredItems();
    });
  }

  // 刷新数据方法
  Future<void> _refreshData() async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    await checkListProvider.loadCheckList(
      widget.staffId,
      currentStaff: authProvider.currentStaff,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.verified_outlined, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(context.t('no_items_to_confirm')),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshData,
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 搜索栏
        SearchBarWidget(
          hintText: context.t('search_documents'),
          onSearchChanged: _onSearchChanged,
          onClear: _onSearchCleared,
          initialValue: _searchQuery,
        ),
        // 显示搜索结果统计
        if (_searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '${context.t('search_results')}: ${_filteredItems.length} / ${widget.items.length}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        // 项目列表
        Expanded(child: _buildItemsList()),
      ],
    );
  }

  Widget _buildItemsList() {
    // 使用过滤后的项目列表
    final sourceItems = _filteredItems.isNotEmpty || _searchQuery.isNotEmpty
        ? _filteredItems
        : widget.items;

    // 如果搜索后没有结果，显示无结果提示
    if (_searchQuery.isNotEmpty && _filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              context.t('no_search_results'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              '${context.t('search_keyword')}: "$_searchQuery"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    // 过滤BOM项目，只显示父项和普通项目
    final List<CheckItem> processedItems = [];
    final bomChildrenByGroup = <String, List<CheckItem>>{};
    final hasBomParents = <String>{}; // 用于跟踪哪些bomGroup有父项

    // 首先，收集所有BOM子项，按bomGroup分组
    for (var item in sourceItems) {
      if (!item.isBomParent &&
          item.parentCode != null &&
          item.parentCode!.isNotEmpty) {
        // 按bomGroup分组
        String bomGroup = item.bomGroup ?? '';
        if (bomGroup.isEmpty && item.documentNo.contains('/')) {
          // 如果bomGroup为空，尝试从documentNo提取主文档编号作为bomGroup
          bomGroup = item.documentNo.split('/').first;
        }

        if (bomGroup.isNotEmpty) {
          if (!bomChildrenByGroup.containsKey(bomGroup)) {
            bomChildrenByGroup[bomGroup] = [];
          }
          bomChildrenByGroup[bomGroup]!.add(item);
        }
      } else if (item.isBomParent && item.bomGroup != null) {
        // 记录哪些bomGroup有父项
        hasBomParents.add(item.bomGroup!);
      }
    }

    // 检查是否是主管或BOM专员
    final bool isSupervisorOrBomSpecialist =
        widget.isSenior || widget.isBomSpecialist;

    // 添加所有项目
    for (var item in sourceItems) {
      // 如果是BOM父项，检查是否有子项被检查过
      if (item.isBomParent) {
        bool hasCheckedChildren = false;

        // 获取父项的bomGroup
        String bomGroup = item.bomGroup ?? item.documentNo;

        // 检查通过bomGroup关联的子项
        if (bomChildrenByGroup.containsKey(bomGroup)) {
          // 修改逻辑：只要还有任何子项没有被supervisor检查完，就应该显示父项
          hasCheckedChildren = bomChildrenByGroup[bomGroup]!.any((child) {
            // 检查是否为特殊条件的BOM子项（直接由Supervisor处理）
            bool isSpecialBomItem =
                child.binShelfNo.trim().isEmpty || !child.haveStock;

            if (isSpecialBomItem) {
              // 特殊BOM子项：只要还没被supervisor检查，就需要显示父项
              return !child.supervisorChecked;
            } else {
              // 正常流程的子项：只要还没被supervisor检查，就需要显示父项
              // 不管staff和BOM专员是否检查过，supervisor都应该能看到
              return !child.supervisorChecked;
            }
          });
        }

        // 如果有子项被检查过，添加父项
        if (hasCheckedChildren) {
          processedItems.add(item);
        }
        continue;
      }

      // 如果是BOM子项
      if (item.parentCode != null && item.parentCode!.isNotEmpty) {
        String bomGroup = item.bomGroup ?? '';
        if (bomGroup.isEmpty && item.documentNo.contains('/')) {
          bomGroup = item.documentNo.split('/').first;
        }

        // 如果是普通员工且没有对应的父项，则显示子项
        if (!isSupervisorOrBomSpecialist && !hasBomParents.contains(bomGroup)) {
          processedItems.add(item);
        }
        continue;
      }

      // 普通项目，正常添加
      processedItems.add(item);
    }

    final filteredItems = processedItems;

    // 首先按main表的created_at时间排序（越早的排越前面）
    final sortedItems = List<CheckItem>.from(filteredItems)
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    // 按document_no和customer分组，而不是按created_at分组
    final documentCustomerGroups = <String, List<CheckItem>>{};
    for (final item in sortedItems) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentCustomerGroups.containsKey(groupKey)) {
        documentCustomerGroups[groupKey] = [];
      }
      documentCustomerGroups[groupKey]!.add(item);
    }

    // 分组完成

    // 将分组转换为列表并按创建时间排序（较早的在前）
    final sortedGroups = documentCustomerGroups.entries.toList()
      ..sort((a, b) {
        // 使用每组中第一个项目的创建时间进行排序
        final aTime = a.value.first.createdAt;
        final bTime = b.value.first.createdAt;
        return aTime.compareTo(bTime);
      });

    // 在数据重新加载后恢复滚动位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _restoreScrollPosition();
    });

    return ListView.builder(
      controller: _scrollController,
      itemCount: sortedGroups.length,
      itemBuilder: (context, index) {
        final groupEntry = sortedGroups[index];
        final groupItems = groupEntry.value;

        // 获取该组的第一个项目的创建时间，用于显示
        final createdAt =
            _formatDate(groupItems.first.createdAt, format: 'yyyy-MM-dd HH:mm');

        // 直接构建文档组，不再需要中间的客户分组
        return _buildDocumentGroup(context, groupItems, createdAt);
      },
    );
  }

  // 构建文档组卡片
  Widget _buildDocumentGroup(
      BuildContext context, List<CheckItem> items, String createdAtStr) {
    // 获取文档编号和客户信息（从第一个项目中提取）
    final firstItem = items.first;
    final documentNo = firstItem.documentNo.contains('/')
        ? firstItem.documentNo.split('/').first
        : firstItem.documentNo;
    final customerCode = firstItem.customer;
    final customerName = firstItem.customerName;
    final documentDate = firstItem.documentDate;

    // 创建唯一的组键
    final groupKey = '${documentNo}_$customerCode';

    // 检查是否为紧急订单
    final isUrgent = items.any((item) => item.priority > 0);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isUrgent ? Colors.red.shade50 : null,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                border: Border(left: BorderSide(color: Colors.red, width: 4)),
                borderRadius: BorderRadius.circular(4),
              )
            : null,
        child: ExpansionTile(
          key: ValueKey(groupKey),
          title: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$customerCode - $customerName',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${context.t('document_no')}: $documentNo',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.purple.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Created: $createdAtStr',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.purple.shade800,
                  ),
                ),
              ),
            ],
          ),
          subtitle: Text(
              'Document Date: ${_formatDate(documentDate, format: 'dd/MM/yyyy')} | ${items.length} ${items.length == 1 ? 'item' : context.t('items')}'),
          // 使用保存的展开状态
          initiallyExpanded: _expandedGroups.contains(groupKey),
          onExpansionChanged: (isExpanded) {
            setState(() {
              if (isExpanded) {
                _expandedGroups.add(groupKey);
              } else {
                _expandedGroups.remove(groupKey);
              }
            });
          },
          children: items.map((item) {
            return Column(
              children: [
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final isBomSpecialist =
                        authProvider.currentStaff?.level == 'staff_bom';
                    return CheckItemCard(
                      item: item,
                      staffId: widget.staffId,
                      isSenior: widget.isSenior,
                      isStaffCheck: false,
                      isBomReview: false,
                      isBomSpecialist: isBomSpecialist,
                      onConfirmAction: _createCustomConfirmAction(item),
                    );
                  },
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  // 恢复滚动位置
  void _restoreScrollPosition() {
    if (_savedScrollOffset != null &&
        _scrollController.hasClients &&
        mounted &&
        !_isRestoring) {
      _isRestoring = true;

      // 使用多次尝试的策略，确保滚动位置能够正确恢复
      _attemptScrollRestore(0);
    }
  }

  // 尝试恢复滚动位置（带重试机制）
  void _attemptScrollRestore(int attemptCount) {
    const maxAttempts = 8; // 增加最大尝试次数，因为 BOM 子项目需要更多时间加载

    if (attemptCount >= maxAttempts || !mounted || _savedScrollOffset == null) {
      _isRestoring = false;
      return;
    }

    // 对于 BOM 子项目，使用更长的延迟
    final isBoMChild = _currentOperatingItemKey != null &&
        _itemOffsets.containsKey(_currentOperatingItemKey);
    final baseDelay = isBoMChild ? 200 : 100;
    final delay = baseDelay + (attemptCount * (isBoMChild ? 150 : 100));

    Future.delayed(Duration(milliseconds: delay), () {
      if (!mounted ||
          !_scrollController.hasClients ||
          _savedScrollOffset == null) {
        _isRestoring = false;
        return;
      }

      // 检查ListView是否已经完全构建
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      if (maxScrollExtent <= 0 && attemptCount < maxAttempts - 1) {
        // ListView还没有完全构建，继续尝试
        _attemptScrollRestore(attemptCount + 1);
        return;
      }

      // 计算目标滚动位置
      double targetOffset = _savedScrollOffset!;

      // 如果是 BOM 子项目，尝试进行更精确的位置计算
      if (_currentOperatingItemKey != null &&
          _itemOffsets.containsKey(_currentOperatingItemKey)) {
        final relativePosition = _itemOffsets[_currentOperatingItemKey]!;
        final viewportHeight = _scrollController.position.viewportDimension;

        // 基于相对位置重新计算目标偏移量
        // 这有助于在数据重新加载后保持更精确的位置
        final adjustedOffset = relativePosition * viewportHeight;

        // 使用调整后的位置，但仍然以原始保存的位置为主
        // 这是一个渐进式的改进，避免过度调整
        targetOffset = (targetOffset + adjustedOffset) / 2;

        debugPrint(
            'ToConfirmTab: BOM子项目精确定位 - 原始位置: $_savedScrollOffset, 调整后位置: $targetOffset, 相对位置: $relativePosition');
      }

      // 确保滚动位置在有效范围内
      targetOffset = targetOffset.clamp(0.0, maxScrollExtent);

      // 记录调试信息
      debugPrint(
          'ToConfirmTab: 恢复滚动位置 - 尝试 $attemptCount, 目标位置: $targetOffset, 最大位置: $maxScrollExtent');

      _scrollController
          .animateTo(
        targetOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      )
          .then((_) {
        if (mounted) {
          debugPrint('ToConfirmTab: 滚动位置恢复成功');
          _isRestoring = false;
          // 清除 BOM 子项目的位置信息
          if (_currentOperatingItemKey != null) {
            _itemOffsets.remove(_currentOperatingItemKey);
          }
          _savedScrollOffset = null; // 清除保存的位置
          _currentOperatingItemKey = null;
        }
      }).catchError((error) {
        debugPrint('ToConfirmTab: 滚动动画失败，尝试直接跳转: $error');
        // 如果动画失败，尝试直接跳转
        if (mounted && _scrollController.hasClients) {
          try {
            _scrollController.jumpTo(targetOffset);
            debugPrint('ToConfirmTab: 直接跳转成功');
          } catch (e) {
            debugPrint('ToConfirmTab: 直接跳转也失败: $e');
          }
        }
        _isRestoring = false;
        // 清除 BOM 子项目的位置信息
        if (_currentOperatingItemKey != null) {
          _itemOffsets.remove(_currentOperatingItemKey);
        }
        _savedScrollOffset = null;
        _currentOperatingItemKey = null;
      });
    });
  }

  // 计算更精确的滚动位置，考虑 BOM 子项目的嵌套结构
  double _calculatePreciseScrollOffset(CheckItem item) {
    if (!_scrollController.hasClients) return 0.0;

    final currentOffset = _scrollController.offset;
    final viewportHeight = _scrollController.position.viewportDimension;

    // 记录调试信息
    debugPrint(
        'ToConfirmTab: 保存滚动位置 - Item: ${item.documentNo}/${item.line}, Offset: $currentOffset, ViewportHeight: $viewportHeight');

    // 对于 BOM 子项目，我们需要考虑它在父项目内部的位置
    // 这里我们保存当前的滚动位置，但在恢复时会进行更精确的计算
    if (item.parentCode != null && item.parentCode!.isNotEmpty) {
      // 这是一个 BOM 子项目，保存额外的位置信息
      final itemKey = '${item.documentNo}_${item.line}';

      // 尝试计算子项目在视口中的相对位置
      // 这个位置信息将在恢复时用于更精确的定位
      final relativePosition =
          currentOffset / (viewportHeight > 0 ? viewportHeight : 1);
      _itemOffsets[itemKey] = relativePosition;

      debugPrint(
          'ToConfirmTab: BOM子项目位置信息 - Key: $itemKey, RelativePosition: $relativePosition');
    }

    return currentOffset;
  }

  // 创建自定义的确认操作回调，保持当前视图位置
  Function(BuildContext, String, String, Future<void> Function())
      _createCustomConfirmAction(CheckItem item) {
    return (BuildContext context, String title, String content,
        Future<void> Function() action) {
      // 保存当前滚动位置和操作的项目信息
      if (_scrollController.hasClients) {
        _savedScrollOffset = _calculatePreciseScrollOffset(item);
        _currentOperatingItemKey = '${item.documentNo}_${item.line}';

        // 同时保存当前的展开状态
        // 这确保了在数据重新加载后，展开状态能够正确恢复
      }

      // 调用原始的确认操作
      widget.onConfirmAction(
        context,
        title,
        content,
        action, // 直接传递action，不在这里处理滚动恢复
      );
    };
  }

  // 格式化日期为指定格式
  String _formatDate(DateTime dateTime, {String format = 'yyyy-MM-dd'}) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }
}
