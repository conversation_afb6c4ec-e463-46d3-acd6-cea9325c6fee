import 'package:flutter/foundation.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/models/staff.dart';
import 'package:queue_system/services/check_service.dart';

class CheckListProvider with ChangeNotifier {
  final CheckService _checkService;
  List<CheckItem> _pendingRegularCheck = []; // 改名：普通库存项目
  List<CheckItem> _pendingBomCheck = []; // 新增：BOM子项
  List<CheckItem> _pendingSupervisorCheck = [];
  List<CheckItem> _checkedItems = [];
  List<CheckItem> _waitingList = [];
  bool _isLoading = false;
  String? _error;

  // 搜索相关状态
  String _searchQuery = '';
  List<CheckItem> _allPendingRegularCheck = []; // 存储所有数据用于本地筛选
  List<CheckItem> _allPendingBomCheck = []; // 存储所有BOM数据用于本地筛选
  List<CheckItem> _allPendingSupervisorCheck = []; // 存储所有主管检查数据用于本地筛选
  List<CheckItem> _allWaitingList = []; // 存储所有等待列表数据用于本地筛选

  // 添加缓存机制
  DateTime? _lastCacheTime;
  static const Duration _cacheValidDuration = Duration(minutes: 2);
  Map<String, List<CheckItem>>? _cachedData;

  // 防重复处理机制
  final Set<String> _processingItems = <String>{};

  // 保存当前用户信息，用于后台刷新时的权限过滤
  Staff? _currentStaff;

  CheckListProvider({required String baseUrl})
      : _checkService = CheckService(baseUrl: baseUrl);

  List<CheckItem> get pendingRegularCheck => _pendingRegularCheck;
  List<CheckItem> get pendingBomCheck => _pendingBomCheck;
  List<CheckItem> get pendingStaffCheck => _pendingRegularCheck; // 向后兼容
  List<CheckItem> get waitingList => _waitingList;
  List<CheckItem> get pendingSupervisorCheck => _pendingSupervisorCheck;
  List<CheckItem> get checkedItems => _checkedItems;
  bool get isLoading => _isLoading;
  String? get error => _error;
  CheckService get checkService => _checkService; // 添加getter以便其他Provider使用

  // 搜索相关 getters
  String get searchQuery => _searchQuery;

  bool get isEmpty =>
      _pendingRegularCheck.isEmpty &&
      _pendingBomCheck.isEmpty &&
      _pendingSupervisorCheck.isEmpty &&
      _waitingList.isEmpty &&
      _checkedItems.isEmpty;

  // 简化的加载方法 - 一次性获取所有数据
  Future<void> loadCheckListPaginated(int staffId,
      {Staff? currentStaff,
      bool isRefresh = false,
      String? searchQuery}) async {
    // 保存当前用户信息
    if (currentStaff != null) {
      _currentStaff = currentStaff;
    }

    // 更新搜索查询
    if (searchQuery != null) {
      _searchQuery = searchQuery;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _checkService.getCheckListPaginated(
        staffId,
        searchQuery:
            _searchQuery.trim().isNotEmpty ? _searchQuery.trim() : null,
      );

      final responseData = result['data'] as Map<String, dynamic>;

      // 解析各类型的检查项目并存储到完整数据列表
      _allPendingRegularCheck = responseData['pendingRegularCheck'] ?? [];
      _allPendingBomCheck = responseData['pendingBomCheck'] ?? [];
      _allPendingSupervisorCheck = responseData['pendingSupervisorCheck'] ?? [];
      _allWaitingList = responseData['waitingList'] ?? [];

      // 应用本地搜索筛选
      _applyLocalSearch();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 本地搜索功能
  void searchCheckList(String query) {
    _searchQuery = query;
    _applyLocalSearch();
    notifyListeners();
  }

  // 清除搜索功能
  void clearSearch() {
    _searchQuery = '';
    _applyLocalSearch();
    notifyListeners();
  }

  // 应用本地搜索筛选
  void _applyLocalSearch() {
    if (_searchQuery.trim().isEmpty) {
      // 没有搜索条件，显示所有数据
      _pendingRegularCheck = List.from(_allPendingRegularCheck);
      _pendingBomCheck = List.from(_allPendingBomCheck);
      _pendingSupervisorCheck = List.from(_allPendingSupervisorCheck);
      _waitingList = List.from(_allWaitingList);
    } else {
      // 有搜索条件，进行本地筛选
      final query = _searchQuery.toLowerCase();

      _pendingRegularCheck = _allPendingRegularCheck.where((item) {
        return item.documentNo.toLowerCase().contains(query) ||
            item.stock.toLowerCase().contains(query) ||
            item.description.toLowerCase().contains(query) ||
            item.customerName.toLowerCase().contains(query);
      }).toList();

      _pendingBomCheck = _allPendingBomCheck.where((item) {
        return item.documentNo.toLowerCase().contains(query) ||
            item.stock.toLowerCase().contains(query) ||
            item.description.toLowerCase().contains(query) ||
            item.customerName.toLowerCase().contains(query);
      }).toList();

      _pendingSupervisorCheck = _allPendingSupervisorCheck.where((item) {
        return item.documentNo.toLowerCase().contains(query) ||
            item.stock.toLowerCase().contains(query) ||
            item.description.toLowerCase().contains(query) ||
            item.customerName.toLowerCase().contains(query);
      }).toList();

      _waitingList = _allWaitingList.where((item) {
        return item.documentNo.toLowerCase().contains(query) ||
            item.stock.toLowerCase().contains(query) ||
            item.description.toLowerCase().contains(query) ||
            item.customerName.toLowerCase().contains(query);
      }).toList();
    }
  }

  Future<void> loadCheckList(int staffId,
      {Staff? currentStaff, bool forceRefresh = false}) async {
    // 保存当前用户信息，用于后台刷新时的权限过滤
    if (currentStaff != null) {
      _currentStaff = currentStaff;
    }

    // 检查缓存是否有效，但仍需要应用用户特定的过滤逻辑
    if (!forceRefresh && _isCacheValid() && _cachedData != null) {
      _loadFromCache(staffId, currentStaff);
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _checkService.getCheckList(staffId);

      // 更新缓存
      _cachedData = result;
      _lastCacheTime = DateTime.now();

      // 处理数据并应用过滤逻辑
      _processAndFilterData(result, staffId, currentStaff);

      // 只保留一条显示数据加载情况的日志
      if (currentStaff != null && currentStaff.level == 'staff_bom') {
        // 不需要额外处理
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> staffCheck(String documentNo, int line, int staffId) async {
    // 防重复提交检查
    final itemKey = '${documentNo}_$line';
    if (_processingItems.contains(itemKey)) {
      return; // 如果正在处理，直接返回
    }

    try {
      _processingItems.add(itemKey); // 标记为处理中
      // 移除全局loading状态，由UI层控制，避免影响其他页面
      // _isLoading = true;
      // notifyListeners();

      await _checkService.staffCheck(documentNo, line, staffId);

      // 操作成功，使用局部更新而不是完全重新加载
      _error = null;
      _updateItemStatusLocally(documentNo, line, 'staff_checked');

      // 清除缓存，确保下次刷新获取最新数据
      clearCache();

      // 移除全局通知，由UI层控制更新，避免影响其他页面
      // notifyListeners();

      // 减少后台刷新的频率，避免影响其他页面
      // _refreshDataInBackground(staffId, delayMs: 1000);
    } catch (e) {
      // 设置详细的错误信息
      _error = e.toString();
      // 移除全局通知，错误处理由UI层负责
      // notifyListeners();

      // 重新抛出错误，以便在UI层处理
      rethrow;
    } finally {
      _processingItems.remove(itemKey); // 移除处理标记
      // 移除全局loading状态和通知
      // _isLoading = false;
      // notifyListeners();
    }
  }

  Future<void> supervisorCheck(
      String documentNo, int line, int supervisorId) async {
    try {
      _isLoading = true;
      notifyListeners();

      await _checkService.supervisorCheck(documentNo, line, supervisorId);

      // 操作成功，使用局部更新而不是完全重新加载
      _error = null;
      _updateItemStatusLocally(documentNo, line, 'supervisor_checked');
      notifyListeners();

      // 异步重新加载数据，不阻塞UI响应
      _refreshDataInBackground(supervisorId);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 局部更新项目状态，避免完全重新加载
  void _updateItemStatusLocally(String documentNo, int line, String checkType) {
    // 根据检查类型处理不同的列表更新
    if (checkType == 'staff_checked') {
      // 处理普通员工检查
      _updateStaffCheckLocally(documentNo, line);
    } else if (checkType == 'bom_specialist_checked') {
      // 处理BOM专员检查
      _updateBomCheckLocally(documentNo, line);
    } else if (checkType == 'supervisor_checked') {
      // 处理主管确认
      _updateSupervisorCheckLocally(documentNo, line);
    }
  }

  // 更新普通员工检查状态
  void _updateStaffCheckLocally(String documentNo, int line) {
    // 从待检查列表中移除并添加到待主管确认列表
    for (int i = 0; i < _pendingRegularCheck.length; i++) {
      if (_pendingRegularCheck[i].documentNo == documentNo &&
          _pendingRegularCheck[i].line == line) {
        final item = _pendingRegularCheck[i];
        _pendingRegularCheck.removeAt(i);

        // 创建已检查的项目并添加到待主管确认列表
        final updatedItem = CheckItem(
          documentNo: item.documentNo,
          line: item.line,
          stock: item.stock,
          description: item.description,
          quantity: item.quantity,
          uom: item.uom,
          binShelfNo: item.binShelfNo,
          haveStock: item.haveStock,
          staffChecked: true, // 更新为已检查
          supervisorChecked: item.supervisorChecked,
          bomSpecialistChecked: item.bomSpecialistChecked,
          parentCode: item.parentCode,
          isBomParent: item.isBomParent,
          bomGroup: item.bomGroup,
          documentDate: item.documentDate,
          customer: item.customer,
          customerName: item.customerName,
          createdAt: item.createdAt,
          staffId: item.staffId,
          priority: item.priority, // 添加 priority 字段
        );

        // 检查是否已存在相同项目，避免重复添加
        final existingIndex = _pendingSupervisorCheck.indexWhere((existing) =>
            existing.documentNo == documentNo && existing.line == line);

        if (existingIndex == -1) {
          _pendingSupervisorCheck.add(updatedItem);
        } else {
          // 更新现有项目
          _pendingSupervisorCheck[existingIndex] = updatedItem;
        }
        break;
      }
    }
  }

  // 更新BOM专员检查状态
  void _updateBomCheckLocally(String documentNo, int line) {
    CheckItem? checkedItem;

    // 从待BOM检查列表中移除
    for (int i = 0; i < _pendingBomCheck.length; i++) {
      if (_pendingBomCheck[i].documentNo == documentNo &&
          _pendingBomCheck[i].line == line) {
        final item = _pendingBomCheck[i];
        _pendingBomCheck.removeAt(i);

        // 创建已检查的项目并添加到待主管确认列表
        checkedItem = CheckItem(
          documentNo: item.documentNo,
          line: item.line,
          stock: item.stock,
          description: item.description,
          quantity: item.quantity,
          uom: item.uom,
          binShelfNo: item.binShelfNo,
          haveStock: item.haveStock,
          staffChecked: item.staffChecked,
          supervisorChecked: item.supervisorChecked,
          bomSpecialistChecked: true, // 更新为已检查
          parentCode: item.parentCode,
          isBomParent: item.isBomParent,
          bomGroup: item.bomGroup,
          documentDate: item.documentDate,
          customer: item.customer,
          customerName: item.customerName,
          createdAt: item.createdAt,
          staffId: item.staffId,
          priority: item.priority,
        );

        _pendingSupervisorCheck.add(checkedItem);
        break;
      }
    }

    // 如果找到了被检查的子项，更新相关的BOM父项状态
    if (checkedItem != null && checkedItem.parentCode != null) {
      _updateBomParentStatusLocally(checkedItem);
    }
  }

  // 更新BOM父项的状态（当子项状态改变时）
  void _updateBomParentStatusLocally(CheckItem childItem) {
    final parentCode = childItem.parentCode;
    final bomGroup =
        childItem.bomGroup ?? childItem.documentNo.split('/').first;

    // 在waitingList中查找对应的BOM父项
    for (int i = 0; i < _waitingList.length; i++) {
      final parentItem = _waitingList[i];

      // 检查是否是对应的BOM父项
      if (parentItem.isBomParent &&
          parentItem.stock == parentCode &&
          (parentItem.bomGroup == bomGroup ||
              parentItem.documentNo.split('/').first == bomGroup)) {
        // 收集该父项的所有子项状态
        final allChildren = <CheckItem>[];

        // 从pendingBomCheck中查找子项
        allChildren.addAll(_pendingBomCheck.where((item) =>
            item.parentCode == parentCode &&
            (item.bomGroup == bomGroup ||
                item.documentNo.split('/').first == bomGroup)));

        // 从pendingSupervisorCheck中查找子项
        allChildren.addAll(_pendingSupervisorCheck.where((item) =>
            item.parentCode == parentCode &&
            (item.bomGroup == bomGroup ||
                item.documentNo.split('/').first == bomGroup)));

        // 计算新的状态
        final totalChildren = allChildren.length;
        final staffCheckedChildren =
            allChildren.where((c) => c.staffChecked).length;
        final bomCheckedChildren =
            allChildren.where((c) => c.bomSpecialistChecked).length;
        final allChildrenStaffChecked =
            allChildren.every((c) => c.staffChecked);
        final allChildrenBomChecked =
            allChildren.every((c) => c.bomSpecialistChecked);
        final canBomCheck =
            allChildren.any((c) => c.staffChecked && !c.bomSpecialistChecked);
        final canSupervisorCheck =
            allChildren.every((c) => c.bomSpecialistChecked);

        // 创建更新后的父项
        final updatedParent = CheckItem(
          documentNo: parentItem.documentNo,
          line: parentItem.line,
          stock: parentItem.stock,
          description: parentItem.description,
          quantity: parentItem.quantity,
          uom: parentItem.uom,
          binShelfNo: parentItem.binShelfNo,
          haveStock: parentItem.haveStock,
          staffChecked: parentItem.staffChecked,
          supervisorChecked: parentItem.supervisorChecked,
          bomSpecialistChecked: parentItem.bomSpecialistChecked,
          parentCode: parentItem.parentCode,
          isBomParent: parentItem.isBomParent,
          bomGroup: parentItem.bomGroup,
          documentDate: parentItem.documentDate,
          customer: parentItem.customer,
          customerName: parentItem.customerName,
          createdAt: parentItem.createdAt,
          staffId: parentItem.staffId,
          priority: parentItem.priority,
          // 更新BOM父项状态字段
          totalChildren: totalChildren,
          staffCheckedChildren: staffCheckedChildren,
          bomCheckedChildren: bomCheckedChildren,
          allChildrenStaffChecked: allChildrenStaffChecked,
          allChildrenBomChecked: allChildrenBomChecked,
          canBomCheck: canBomCheck,
          canSupervisorCheck: canSupervisorCheck,
        );

        // 替换waitingList中的父项
        _waitingList[i] = updatedParent;
        break;
      }
    }
  }

  // 更新主管确认状态
  void _updateSupervisorCheckLocally(String documentNo, int line) {
    // 在整合模式下，需要从waitingList中移除项目
    // 首先尝试从waitingList中移除
    bool removedFromWaiting = false;
    for (int i = 0; i < _waitingList.length; i++) {
      if (_waitingList[i].documentNo == documentNo &&
          _waitingList[i].line == line) {
        final item = _waitingList[i];
        _waitingList.removeAt(i);
        removedFromWaiting = true;

        // 添加到已检查列表
        final updatedItem = CheckItem(
          documentNo: item.documentNo,
          line: item.line,
          stock: item.stock,
          description: item.description,
          quantity: item.quantity,
          uom: item.uom,
          binShelfNo: item.binShelfNo,
          haveStock: item.haveStock,
          staffChecked: item.staffChecked,
          supervisorChecked: true, // 更新为已确认
          bomSpecialistChecked: item.bomSpecialistChecked,
          parentCode: item.parentCode,
          isBomParent: item.isBomParent,
          bomGroup: item.bomGroup,
          documentDate: item.documentDate,
          customer: item.customer,
          customerName: item.customerName,
          createdAt: item.createdAt,
          staffId: item.staffId,
          priority: item.priority, // 添加 priority 字段
        );

        _checkedItems.add(updatedItem);
        break;
      }
    }

    // 如果没有从waitingList中找到，则从pendingSupervisorCheck中移除（向后兼容）
    if (!removedFromWaiting) {
      for (int i = 0; i < _pendingSupervisorCheck.length; i++) {
        if (_pendingSupervisorCheck[i].documentNo == documentNo &&
            _pendingSupervisorCheck[i].line == line) {
          final item = _pendingSupervisorCheck[i];

          // 从待确认列表中移除
          _pendingSupervisorCheck.removeAt(i);

          // 添加到已检查列表
          final updatedItem = CheckItem(
            documentNo: item.documentNo,
            line: item.line,
            stock: item.stock,
            description: item.description,
            quantity: item.quantity,
            uom: item.uom,
            binShelfNo: item.binShelfNo,
            haveStock: item.haveStock,
            staffChecked: item.staffChecked,
            supervisorChecked: true, // 更新为已确认
            bomSpecialistChecked: item.bomSpecialistChecked,
            parentCode: item.parentCode,
            isBomParent: item.isBomParent,
            bomGroup: item.bomGroup,
            documentDate: item.documentDate,
            customer: item.customer,
            customerName: item.customerName,
            createdAt: item.createdAt,
            staffId: item.staffId,
            priority: item.priority, // 添加 priority 字段
          );

          _checkedItems.add(updatedItem);
          break;
        }
      }
    }
  }

  // 后台异步刷新数据
  void _refreshDataInBackground(int staffId, {int delayMs = 500}) {
    Future.delayed(Duration(milliseconds: delayMs), () async {
      try {
        // 使用保存的currentStaff信息进行权限过滤
        await loadCheckList(staffId,
            currentStaff: _currentStaff, forceRefresh: true);
      } catch (e) {
        // 后台刷新失败不影响用户操作，静默处理
        // 可以考虑使用日志框架记录错误
      }
    });
  }

  // 检查缓存是否有效
  bool _isCacheValid() {
    if (_lastCacheTime == null) return false;
    return DateTime.now().difference(_lastCacheTime!) < _cacheValidDuration;
  }

  // 从缓存加载数据，但仍需要应用用户特定的过滤逻辑
  void _loadFromCache(int staffId, Staff? currentStaff) {
    if (_cachedData == null) return;

    _isLoading = false;
    _error = null;

    // 处理缓存数据并应用过滤逻辑
    _processAndFilterData(_cachedData!, staffId, currentStaff);

    notifyListeners();
  }

  // 处理数据并应用过滤逻辑
  void _processAndFilterData(
      Map<String, List<CheckItem>> result, int staffId, Staff? currentStaff) {
    // 原始数据
    List<CheckItem> allRegularCheckItems = result['pendingRegularCheck'] ?? [];
    List<CheckItem> allBomCheckItems = result['pendingBomCheck'] ?? [];
    List<CheckItem> allSupervisorCheckItems =
        result['pendingSupervisorCheck'] ?? [];
    List<CheckItem> allCheckedItems = result['checkedItems'] ?? [];
    List<CheckItem> allWaitingItems = result['waitingList'] ?? [];

    // 主管/管理员/BOM专员/司机可以看到所有物品，不需要过滤
    if (currentStaff != null &&
        (currentStaff.level == 'supervisor' ||
            currentStaff.level == 'admin' ||
            currentStaff.level == 'staff_bom' ||
            currentStaff.level == 'driver')) {
      _pendingRegularCheck = allRegularCheckItems;
      _pendingBomCheck = allBomCheckItems;
      _pendingSupervisorCheck = allSupervisorCheckItems;
      _waitingList = allWaitingItems;

      // BOM专员特别处理
      if (currentStaff.level == 'staff_bom') {
        // 包含正常流程的BOM子项和特殊条件的BOM子项
        _pendingBomCheck = allBomCheckItems.where((item) {
          // 必须是BOM子项（有parentCode）且未被BOM专员检查
          bool isBomChild = item.parentCode != null &&
              item.parentCode!.isNotEmpty &&
              !item.isBomParent;
          bool notCheckedByBomSpecialist = item.bomSpecialistChecked != true;

          // 检查是否为特殊条件的BOM子项（无货架位置或无库存）
          bool isSpecialBomItem =
              item.binShelfNo.trim().isEmpty || !item.haveStock;

          // 对于特殊条件的BOM子项，BOM专员可以直接检查，不需要等待staff检查
          // 对于正常流程的BOM子项，需要先被普通员工检查
          bool canBeCheckedByBomSpecialist =
              isSpecialBomItem || item.staffChecked == true;

          // 返回最终判断结果
          return isBomChild &&
              notCheckedByBomSpecialist &&
              canBeCheckedByBomSpecialist;
        }).toList();

        // BOM专员特别关注已检查的项目
        _checkedItems = allCheckedItems
            .where((item) =>
                item.staffChecked && (item.isBom || item.parentCode != null))
            .toList();
      } else {
        _checkedItems = allCheckedItems;
      }
    }
    // 如果是普通员工，需要按照楼层过滤数据
    else if (currentStaff != null && currentStaff.level == 'regular') {
      String floorPrefix = currentStaff.floor.replaceAll('F', '');

      // 只显示该员工负责楼层的待检查普通库存项目
      _pendingRegularCheck = allRegularCheckItems.where((item) {
        // 假设binShelfNo的格式为"2-1-G001-1"，第一个数字代表楼层
        if (item.binShelfNo.isNotEmpty) {
          String itemFloor = item.binShelfNo.split('-').first;
          return itemFloor == floorPrefix;
        }
        return false;
      }).toList();

      // 只显示该员工负责楼层的待检查BOM子项
      _pendingBomCheck = allBomCheckItems.where((item) {
        // 假设binShelfNo的格式为"2-1-G001-1"，第一个数字代表楼层
        if (item.binShelfNo.isNotEmpty) {
          String itemFloor = item.binShelfNo.split('-').first;
          return itemFloor == floorPrefix;
        }
        return false;
      }).toList();

      // 普通员工只能看到自己检查过的、待主管确认的项目
      _pendingSupervisorCheck = allSupervisorCheckItems.where((item) {
        if (!item.staffChecked) return false; // 未被员工检查的不显示

        // 已被员工检查，且是当前员工检查的
        if (item.staffId == staffId) {
          // 检查是否是该员工负责的楼层
          if (item.binShelfNo.isNotEmpty) {
            String itemFloor = item.binShelfNo.split('-').first;
            return itemFloor == floorPrefix;
          }
        }
        return false;
      }).toList();

      // 普通员工不需要查看所有已检查的BOM项目
      _checkedItems = [];
      _waitingList = [];
    } else {
      // 无效的员工信息，使用默认处理方式
      _pendingRegularCheck = allRegularCheckItems;
      _pendingBomCheck = allBomCheckItems;
      _pendingSupervisorCheck = allSupervisorCheckItems;
      _checkedItems = [];
      _waitingList = [];
    }
  }

  // 清除缓存
  void clearCache() {
    _cachedData = null;
    _lastCacheTime = null;
  }

  // 清除所有数据和状态（用于登出时）
  void clearAllData() {
    _pendingRegularCheck.clear();
    _pendingBomCheck.clear();
    _pendingSupervisorCheck.clear();
    _checkedItems.clear();
    _waitingList.clear();
    _processingItems.clear();
    _currentStaff = null;
    _searchQuery = ''; // 清除搜索查询
    clearCache();
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  // 检查特定项目是否正在处理中
  bool isItemProcessing(String documentNo, int line) {
    final itemKey = '${documentNo}_$line';
    return _processingItems.contains(itemKey);
  }

  // 获取所有正在处理的项目
  Set<String> get processingItems => Set.from(_processingItems);

  // BOM专员检查方法 - 优化版本，减少全局影响
  Future<void> bomCheck(
      String documentNo, int line, int bomSpecialistId) async {
    // 防重复提交检查
    final itemKey = '${documentNo}_$line';
    if (_processingItems.contains(itemKey)) {
      return; // 如果正在处理，直接返回
    }

    try {
      _processingItems.add(itemKey); // 标记为处理中

      // 只在必要时设置全局loading状态
      // 对于BOM子项检查，不需要全局loading，由UI层处理loading状态
      // _isLoading = true;
      // notifyListeners();

      await _checkService.bomCheck(documentNo, line, bomSpecialistId);

      // 操作成功，使用局部更新而不是完全重新加载
      _error = null;
      _updateItemStatusLocally(documentNo, line, 'bom_specialist_checked');

      // 清除缓存，确保下次刷新获取最新数据
      clearCache();

      // 完全移除notifyListeners()，由UI层控制更新，避免影响其他页面
      // notifyListeners();

      // 减少后台刷新的频率和影响
      // 对于BOM子项检查，可以延长刷新时间或者完全由UI层控制
      // _refreshDataInBackground(bomSpecialistId, delayMs: 2000);
    } catch (e) {
      // 设置详细的错误信息
      _error = e.toString();
      // 移除notifyListeners()，错误处理由UI层负责，避免影响其他页面
      // notifyListeners();

      // 重新抛出错误，以便在UI层处理
      rethrow;
    } finally {
      _processingItems.remove(itemKey); // 移除处理标记
      // _isLoading = false;
      // notifyListeners();
    }
  }

  // 拒绝员工检查方法 - 适用于主管、司机和BOM专员
  Future<void> rejectStaffCheck(
      String documentNo, int line, int staffId, String reason) async {
    try {
      _isLoading = true;
      notifyListeners();

      // 检查是否是"修改/取消"选项
      bool isModifyCancel = reason == '修改/取消 (Modify/Cancel)' ||
          reason == 'Modify/Cancel' ||
          reason == '修改/取消' ||
          reason == 'Ubah/Batal';

      if (isModifyCancel) {
        // 1. 首先拒绝检查
        await _checkService.rejectStaffCheck(documentNo, line, staffId, reason);

        // 注释掉从SQL Server同步最新数据的逻辑 - 2025-06-18
        /*
        // 2. 从SQL Server同步最新数据
        try {
          final syncResult = await _checkService.syncDetail(documentNo, line);

          // 3. 检查是否需要库存归位
          if (syncResult['needStockReturn'] == true) {
            try {
              await _checkService.returnStock(documentNo, line, staffId);
            } catch (returnError) {
              // 库存归位失败不影响整体流程
            }
          }
        } catch (syncError) {
          // 同步失败不影响整体流程，继续执行
        }
        */
      } else {
        // 普通拒绝流程
        await _checkService.rejectStaffCheck(documentNo, line, staffId, reason);
      }

      // 操作成功
      _error = null;
      await loadCheckList(staffId,
          currentStaff: _currentStaff, forceRefresh: true); // 强制重新加载列表
    } catch (e) {
      // 如果错误是error_reject_check，说明是后端返回的错误代码
      // 这种情况下，我们不应该将其视为错误，而是应该将其视为成功
      if (e.toString() == 'error_reject_check') {
        // 操作成功
        _error = null;
        await loadCheckList(staffId,
            currentStaff: _currentStaff, forceRefresh: true); // 强制重新加载列表

        // 不抛出错误
        return;
      }

      // 设置详细的错误信息
      _error = e.toString();
      notifyListeners();

      // 重新抛出错误，以便在UI层处理
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 获取BOM子项状态
  Future<List<CheckItem>> getBomChildrenStatus(String bomGroup,
      [String? parentStock, int? bomParentId]) async {
    try {
      _isLoading = true;
      notifyListeners();

      // 优先使用bomParentId，如果没有则使用parentStock
      final result = await _checkService.getBomChildrenStatus(
          bomGroup, parentStock, bomParentId);

      _error = null;
      return result;
    } catch (e) {
      _error = e.toString();
      return [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // 获取Waiting页面的账单数量（只计算main table，不计算detail数量）
  int getWaitingDocumentCount() {
    if (_waitingList.isEmpty) {
      return 0;
    }

    // 按document_no分组，只计算唯一的账单数量
    final documentGroups = <String, List<CheckItem>>{};
    for (final item in _waitingList) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentGroups.containsKey(groupKey)) {
        documentGroups[groupKey] = [];
      }
      documentGroups[groupKey]!.add(item);
    }

    return documentGroups.length;
  }

  // 获取待检查普通项目的账单数量（只计算main table，不计算detail数量）
  int getPendingRegularCheckDocumentCount() {
    if (_pendingRegularCheck.isEmpty) {
      return 0;
    }

    // 按document_no分组，只计算唯一的账单数量
    final documentGroups = <String, List<CheckItem>>{};
    for (final item in _pendingRegularCheck) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentGroups.containsKey(groupKey)) {
        documentGroups[groupKey] = [];
      }
      documentGroups[groupKey]!.add(item);
    }

    return documentGroups.length;
  }

  // 获取待检查BOM项目的账单数量（只计算main table，不计算detail数量）
  int getPendingBomCheckDocumentCount() {
    if (_pendingBomCheck.isEmpty) {
      return 0;
    }

    // 按document_no分组，只计算唯一的账单数量
    final documentGroups = <String, List<CheckItem>>{};
    for (final item in _pendingBomCheck) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentGroups.containsKey(groupKey)) {
        documentGroups[groupKey] = [];
      }
      documentGroups[groupKey]!.add(item);
    }

    return documentGroups.length;
  }

  // 获取过滤后的待确认项目数量（排除没有子项被检查过的BOM父项）
  int getFilteredToConfirmItemsCount() {
    // 如果不是主管或BOM专员，直接返回原始数量
    if (_pendingSupervisorCheck.isEmpty) {
      return 0;
    }

    // 收集所有BOM子项，按bomGroup分组
    final bomChildrenByGroup = <String, List<CheckItem>>{};
    final hasBomParents = <String>{}; // 用于跟踪哪些bomGroup有父项

    // 找出所有BOM子项和父项
    for (var item in _pendingSupervisorCheck) {
      if (!item.isBomParent &&
          item.parentCode != null &&
          item.parentCode!.isNotEmpty) {
        // 按bomGroup分组
        String bomGroup = item.bomGroup ?? '';
        if (bomGroup.isEmpty && item.documentNo.contains('/')) {
          // 如果bomGroup为空，尝试从documentNo提取主文档编号作为bomGroup
          bomGroup = item.documentNo.split('/').first;
        }

        if (bomGroup.isNotEmpty) {
          if (!bomChildrenByGroup.containsKey(bomGroup)) {
            bomChildrenByGroup[bomGroup] = [];
          }
          bomChildrenByGroup[bomGroup]!.add(item);
        }
      } else if (item.isBomParent && item.bomGroup != null) {
        // 记录哪些bomGroup有父项
        hasBomParents.add(item.bomGroup!);
      }
    }

    // 计算符合条件的项目数量
    int count = 0;

    for (var item in _pendingSupervisorCheck) {
      // 如果是BOM父项，检查是否有子项被检查过
      if (item.isBomParent) {
        bool hasCheckedChildren = false;

        // 获取父项的bomGroup
        String bomGroup = item.bomGroup ?? item.documentNo;

        // 检查通过bomGroup关联的子项
        if (bomChildrenByGroup.containsKey(bomGroup)) {
          // 修改逻辑：只要还有任何子项没有被supervisor检查完，就应该显示父项
          hasCheckedChildren = bomChildrenByGroup[bomGroup]!.any((child) {
            // 检查是否为跳过条件的BOM子项
            bool shouldSkipChecks =
                child.binShelfNo.trim().isEmpty || !child.haveStock;

            if (shouldSkipChecks) {
              // 跳过条件的子项：只要还没被supervisor检查，就需要显示父项
              return !child.supervisorChecked;
            } else {
              // 正常流程的子项：只要还没被supervisor检查，就需要显示父项
              // 不管staff和BOM专员是否检查过，supervisor都应该能看到
              return !child.supervisorChecked;
            }
          });
        }

        // 如果有子项被检查过，计数加1
        if (hasCheckedChildren) {
          count++;
        }
      }
      // 如果是BOM子项
      else if (item.parentCode != null && item.parentCode!.isNotEmpty) {
        String bomGroup = item.bomGroup ?? '';
        if (bomGroup.isEmpty && item.documentNo.contains('/')) {
          bomGroup = item.documentNo.split('/').first;
        }

        // 如果没有对应的父项，则计数加1
        if (!hasBomParents.contains(bomGroup)) {
          count++;
        }
      } else if (item.parentCode == null || item.parentCode!.isEmpty) {
        // 普通项目，计数加1
        count++;
      }
    }

    return count;
  }

  // 重新同步整个订单
  Future<Map<String, dynamic>> refreshOrder(
      String documentNo, int supervisorId, String reason) async {
    try {
      _isLoading = true;
      notifyListeners();

      final result =
          await _checkService.refreshOrder(documentNo, supervisorId, reason);

      // 操作成功，重新加载数据
      _error = null;
      await loadCheckList(supervisorId,
          currentStaff: _currentStaff, forceRefresh: true);

      return result;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
