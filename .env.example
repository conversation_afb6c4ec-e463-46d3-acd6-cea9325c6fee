# ===========================================
# 环境变量配置模板 (.env.example)
# 复制此文件为 .env 并填入实际值
# 注意：此文件主要用于 npm start 启动时
# PM2部署时会使用 ecosystem.*.config.js 中的配置
# ===========================================

# SQL Server Configuration
SQL_SERVER_HOST=your-sql-server-host
SQL_SERVER_PORT=1433
SQL_SERVER_USERNAME=your-sql-username
SQL_SERVER_PASSWORD=your-sql-password
SQL_SERVER_DATABASE=your-sql-database

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DATABASE=your-postgres-database

# Application Configuration
NODE_ENV=development

# PDF Configuration
PDF_OUTPUT_DIR=your-project-path\PDF_Output

# Email Configuration
EMAIL_HOST=your-email-host
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>