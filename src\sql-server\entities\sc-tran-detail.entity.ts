import { <PERSON>ti<PERSON>, Column, PrimaryC<PERSON>umn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('SC_Tran_Detail')
export class SqlServerScTranDetail {
  @ApiProperty({ description: '文档编号', example: 'D00001/1' })
  @Column({ name: 'Document No', primary: true })
  DocumentNo: string;

  @ApiProperty({ description: '行号', example: 1 })
  @Column({ name: 'Line', primary: true })
  Line: number;

  @ApiProperty({ description: '库存代码', example: 'ITEM001' })
  @Column({ name: 'Stock Code' })
  StockCode: string;

  @ApiProperty({ description: '数量', example: 10 })
  @Column({ name: 'Quantity' })
  Quantity: number;

  @ApiProperty({ description: 'BOM的库存代码', example: 'BOM001', nullable: true })
  @Column({ name: 'Parent Code', nullable: true })
  ParentCode: string;
} 