class CheckItem {
  final int? id; // 添加id字段
  final String documentNo;
  final int line;
  final String stock;
  final String description;
  final String? partNo; // 添加零件号字段
  final int quantity;
  final String uom;
  final String binShelfNo;
  final bool staffChecked;
  final DateTime? staffCheckedAt;
  final int? staffId;
  final bool supervisorChecked;
  final DateTime? supervisorCheckedAt;
  final int? supervisorId;
  final bool bomSpecialistChecked;
  final DateTime? bomSpecialistCheckedAt;
  final int? bomSpecialistId;
  final DateTime documentDate;
  final DateTime createdAt;
  final String customer;
  final String customerName;
  final String? remark;
  final String? parentCode;
  final bool haveStock;
  final String? brandCode;
  final double? unitPrice;
  final double? totalAmount;
  // 新增字段
  final bool isBomParent;
  final int? bomParentId;
  final String? bomGroup;
  final int priority;

  // 新增BOM父项状态字段
  final int? totalChildren;
  final int? staffCheckedChildren;
  final int? bomCheckedChildren;
  final bool? allChildrenStaffChecked;
  final bool? allChildrenBomChecked;
  final bool? canBomCheck;
  final bool? canSupervisorCheck;
  final bool? bomChildrenCompleted;

  CheckItem({
    this.id, // 添加id参数
    required this.documentNo,
    required this.line,
    required this.stock,
    required this.description,
    this.partNo, // 添加零件号参数
    required this.quantity,
    required this.uom,
    required this.binShelfNo,
    required this.staffChecked,
    this.staffCheckedAt,
    this.staffId,
    required this.supervisorChecked,
    this.supervisorCheckedAt,
    this.supervisorId,
    required this.bomSpecialistChecked,
    this.bomSpecialistCheckedAt,
    this.bomSpecialistId,
    required this.documentDate,
    required this.createdAt,
    required this.customer,
    required this.customerName,
    this.remark,
    this.parentCode,
    this.haveStock = true,
    this.brandCode,
    this.unitPrice,
    this.totalAmount,
    this.isBomParent = false,
    this.bomParentId,
    this.bomGroup,
    this.priority = 0,
    this.totalChildren,
    this.staffCheckedChildren,
    this.bomCheckedChildren,
    this.allChildrenStaffChecked,
    this.allChildrenBomChecked,
    this.canBomCheck,
    this.canSupervisorCheck,
    this.bomChildrenCompleted,
  });

  // 判断是否为BOM项（包括父项和子项）
  bool get isBom =>
      isBomParent || (parentCode != null && parentCode!.isNotEmpty);

  // 判断是否为BOM子项
  bool get isBomChild =>
      !isBomParent && parentCode != null && parentCode!.isNotEmpty;

  // 创建副本并更新指定字段
  CheckItem copyWith({
    int? id,
    String? documentNo,
    int? line,
    String? stock,
    String? description,
    String? partNo,
    int? quantity,
    String? uom,
    String? binShelfNo,
    bool? staffChecked,
    DateTime? staffCheckedAt,
    int? staffId,
    bool? supervisorChecked,
    DateTime? supervisorCheckedAt,
    int? supervisorId,
    bool? bomSpecialistChecked,
    DateTime? bomSpecialistCheckedAt,
    int? bomSpecialistId,
    DateTime? documentDate,
    DateTime? createdAt,
    String? customer,
    String? customerName,
    String? remark,
    String? parentCode,
    bool? haveStock,
    String? brandCode,
    double? unitPrice,
    double? totalAmount,
    bool? isBomParent,
    int? bomParentId,
    String? bomGroup,
    int? priority,
    int? totalChildren,
    int? staffCheckedChildren,
    int? bomCheckedChildren,
    bool? allChildrenStaffChecked,
    bool? allChildrenBomChecked,
    bool? canBomCheck,
    bool? canSupervisorCheck,
    bool? bomChildrenCompleted,
  }) {
    return CheckItem(
      id: id ?? this.id,
      documentNo: documentNo ?? this.documentNo,
      line: line ?? this.line,
      stock: stock ?? this.stock,
      description: description ?? this.description,
      partNo: partNo ?? this.partNo,
      quantity: quantity ?? this.quantity,
      uom: uom ?? this.uom,
      binShelfNo: binShelfNo ?? this.binShelfNo,
      staffChecked: staffChecked ?? this.staffChecked,
      staffCheckedAt: staffCheckedAt ?? this.staffCheckedAt,
      staffId: staffId ?? this.staffId,
      supervisorChecked: supervisorChecked ?? this.supervisorChecked,
      supervisorCheckedAt: supervisorCheckedAt ?? this.supervisorCheckedAt,
      supervisorId: supervisorId ?? this.supervisorId,
      bomSpecialistChecked: bomSpecialistChecked ?? this.bomSpecialistChecked,
      bomSpecialistCheckedAt:
          bomSpecialistCheckedAt ?? this.bomSpecialistCheckedAt,
      bomSpecialistId: bomSpecialistId ?? this.bomSpecialistId,
      documentDate: documentDate ?? this.documentDate,
      createdAt: createdAt ?? this.createdAt,
      customer: customer ?? this.customer,
      customerName: customerName ?? this.customerName,
      remark: remark ?? this.remark,
      parentCode: parentCode ?? this.parentCode,
      haveStock: haveStock ?? this.haveStock,
      brandCode: brandCode ?? this.brandCode,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
      isBomParent: isBomParent ?? this.isBomParent,
      bomParentId: bomParentId ?? this.bomParentId,
      bomGroup: bomGroup ?? this.bomGroup,
      priority: priority ?? this.priority,
      totalChildren: totalChildren ?? this.totalChildren,
      staffCheckedChildren: staffCheckedChildren ?? this.staffCheckedChildren,
      bomCheckedChildren: bomCheckedChildren ?? this.bomCheckedChildren,
      allChildrenStaffChecked:
          allChildrenStaffChecked ?? this.allChildrenStaffChecked,
      allChildrenBomChecked:
          allChildrenBomChecked ?? this.allChildrenBomChecked,
      canBomCheck: canBomCheck ?? this.canBomCheck,
      canSupervisorCheck: canSupervisorCheck ?? this.canSupervisorCheck,
      bomChildrenCompleted: bomChildrenCompleted ?? this.bomChildrenCompleted,
    );
  }

  factory CheckItem.fromJson(Map<String, dynamic> json) {
    try {
      // 打印整个json，帮助调试
      //print('CheckItem原始数据: $json');

      // 安全地解析数字类型字段
      int parseLine() {
        final line = json['line'];
        if (line is int) return line;
        if (line is String) return int.parse(line);
        return 0;
      }

      int parseQuantity() {
        final quantity = json['quantity'];
        if (quantity is int) return quantity;
        if (quantity is double) return quantity.round();
        if (quantity is String) {
          try {
            // 尝试先解析为双精度浮点数，然后四舍五入转为整数
            return double.parse(quantity).round();
          } catch (e) {
            print('无法解析quantity值: $quantity, 错误: $e');
            return 0;
          }
        }
        return 0;
      }

      int? parseStaffId() {
        final staffId = json['staff_id'];
        if (staffId == null) return null;
        if (staffId is int) return staffId;
        if (staffId is String) {
          if (staffId.isEmpty) return null;
          return int.parse(staffId);
        }
        return null;
      }

      int? parseSupervisorId() {
        final supervisorId = json['supervisor_id'];
        if (supervisorId == null) return null;
        if (supervisorId is int) return supervisorId;
        if (supervisorId is String) {
          if (supervisorId.isEmpty) return null;
          return int.parse(supervisorId);
        }
        return null;
      }

      int? parseBomSpecialistId() {
        final bomSpecialistId = json['bom_specialist_id'];
        if (bomSpecialistId == null) return null;
        if (bomSpecialistId is int) return bomSpecialistId;
        if (bomSpecialistId is String) {
          if (bomSpecialistId.isEmpty) return null;
          return int.parse(bomSpecialistId);
        }
        return null;
      }

      bool parseBoolean(String key) {
        final value = json[key];
        if (value is bool) return value;
        if (value is String) return value.toLowerCase() == 'true';
        if (value is int) return value != 0;
        return false;
      }

      DateTime parseDate(String key) {
        final value = json[key];
        if (value == null) return DateTime.now();
        try {
          return DateTime.parse(value.toString());
        } catch (e) {
          print('无法解析日期: $value, 错误: $e');
          return DateTime.now();
        }
      }

      // 解析新增字段
      double? parseUnitPrice() {
        final price = json['unit_price'];
        if (price == null) return null;
        if (price is double) return price;
        if (price is int) return price.toDouble();
        if (price is String && price.isNotEmpty) {
          try {
            return double.parse(price);
          } catch (e) {
            print('无法解析unit_price值: $price, 错误: $e');
            return null;
          }
        }
        return null;
      }

      double? parseTotalAmount() {
        final amount = json['total_amount'];
        if (amount == null) return null;
        if (amount is double) return amount;
        if (amount is int) return amount.toDouble();
        if (amount is String && amount.isNotEmpty) {
          try {
            return double.parse(amount);
          } catch (e) {
            print('无法解析total_amount值: $amount, 错误: $e');
            return null;
          }
        }
        return null;
      }

      // 解析新增字段
      int? parseBomParentId() {
        final bomParentId = json['bom_parent_id'];
        if (bomParentId == null) return null;
        if (bomParentId is int) return bomParentId;
        if (bomParentId is String) {
          if (bomParentId.isEmpty) return null;
          try {
            return int.parse(bomParentId);
          } catch (e) {
            print('无法解析bom_parent_id值: $bomParentId, 错误: $e');
            return null;
          }
        }
        return null;
      }

      return CheckItem(
        id: json['id'] is int ? json['id'] : null, // 解析id字段
        documentNo: json['document_no']?.toString() ?? '',
        line: parseLine(),
        stock: json['stock']?.toString() ?? '',
        description: json['description']?.toString() ?? '',
        partNo: json['part_no']?.toString(), // 添加零件号字段解析
        quantity: parseQuantity(),
        uom: json['uom']?.toString() ?? '',
        // 确保bin_shelf_no为null或空字符串时都被处理为空字符串
        binShelfNo: (json['bin_shelf_no'] == null ||
                json['bin_shelf_no'].toString().trim() == '')
            ? ''
            : json['bin_shelf_no'].toString(),
        staffChecked: parseBoolean('staff_checked'),
        staffCheckedAt: json['staff_checked_at'] != null
            ? DateTime.parse(json['staff_checked_at'].toString())
            : null,
        staffId: parseStaffId(),
        supervisorChecked: parseBoolean('supervisor_checked'),
        supervisorCheckedAt: json['supervisor_checked_at'] != null
            ? DateTime.parse(json['supervisor_checked_at'].toString())
            : null,
        supervisorId: parseSupervisorId(),
        bomSpecialistChecked: parseBoolean('bom_specialist_checked'),
        bomSpecialistCheckedAt: json['bom_specialist_checked_at'] != null
            ? DateTime.parse(json['bom_specialist_checked_at'].toString())
            : null,
        bomSpecialistId: parseBomSpecialistId(),
        documentDate: parseDate('document_date'),
        createdAt: parseDate('created_at'),
        customer: json['customer']?.toString() ?? '',
        customerName: json['customer_name']?.toString() ?? '',
        remark: json['remark']?.toString(),
        parentCode: json['parent_code']?.toString(),
        haveStock: parseBoolean('have_stock'),
        brandCode: json['brand_code']?.toString(),
        unitPrice: parseUnitPrice(),
        totalAmount: parseTotalAmount(),
        // 新增字段
        isBomParent: parseBoolean('is_bom_parent'),
        bomParentId: parseBomParentId(),
        bomGroup: json['bom_group']?.toString(),
        priority: json['priority'] is int ? json['priority'] : 0,
        // BOM父项状态字段
        totalChildren:
            json['total_children'] is int ? json['total_children'] : null,
        staffCheckedChildren: json['staff_checked_children'] is int
            ? json['staff_checked_children']
            : null,
        bomCheckedChildren: json['bom_checked_children'] is int
            ? json['bom_checked_children']
            : null,
        allChildrenStaffChecked: json['all_children_staff_checked'] is bool
            ? json['all_children_staff_checked']
            : null,
        allChildrenBomChecked: json['all_children_bom_checked'] is bool
            ? json['all_children_bom_checked']
            : null,
        canBomCheck:
            json['can_bom_check'] is bool ? json['can_bom_check'] : null,
        canSupervisorCheck: json['can_supervisor_check'] is bool
            ? json['can_supervisor_check']
            : null,
        bomChildrenCompleted: json['bom_children_completed'] is bool
            ? json['bom_children_completed']
            : null,
      );
    } catch (e) {
      print('解析CheckItem时出错: $e');
      print('出错的JSON数据: $json');
      rethrow;
    }
  }
}
