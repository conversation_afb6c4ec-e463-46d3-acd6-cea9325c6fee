import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Main } from '../postgres/entities/main.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { FirebaseService } from '../firebase/firebase.service';
import * as PDFDocument from 'pdfkit';
import * as fs from 'fs';
import * as path from 'path';
import * as fsExtra from 'fs-extra';
import { Buffer } from 'buffer';

@Injectable()
export class PdfService {
  constructor(
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    private readonly firebaseService: FirebaseService,
  ) { }

  /**
   * 生成订单PDF并保存到指定目录
   * @param documentNo 订单编号
   * @returns 生成的PDF文件路径
   */
  async generateOrderPdf(documentNo: string): Promise<string> {
    // 获取订单主表数据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!main) {
      throw new Error(`Document ${documentNo} not found`);
    }

    // 获取订单明细数据
    let details = await this.detailRepository.find({
      where: { main_id: main.id },
    });

    // 按行号排序明细数据
    details = details.sort((a, b) => a.line - b.line);

    // 创建输出目录 - 使用订单相关日期而不是当前日期
    let dateStr: string;
    if (main.shipped_at) {
      // 优先使用出货日期
      const shippedDate = new Date(main.shipped_at);
      dateStr = `${shippedDate.getFullYear()}-${String(shippedDate.getMonth() + 1).padStart(2, '0')}-${String(shippedDate.getDate()).padStart(2, '0')}`;
      console.log(`[PDF] 使用出货日期: ${dateStr}`);
    } else if (main.document_date) {
      // 其次使用文档日期
      const docDate = new Date(main.document_date);
      dateStr = `${docDate.getFullYear()}-${String(docDate.getMonth() + 1).padStart(2, '0')}-${String(docDate.getDate()).padStart(2, '0')}`;
      console.log(`[PDF] 使用文档日期: ${dateStr}`);
    } else {
      // 最后使用当前日期
      const today = new Date();
      dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      console.log(`[PDF] 使用当前日期: ${dateStr}`);
    }

    const baseDir = process.env.PDF_OUTPUT_DIR || path.join(__dirname, '..', '..', 'PDF_Output');
    const outputDir = path.join(baseDir, dateStr);

    console.log(`[PDF Service] 工作目录: ${process.cwd()}`);
    console.log(`[PDF Service] PDF基础目录: ${baseDir}`);
    console.log(`[PDF Service] 输出目录: ${outputDir}`);

    // 确保目录存在
    await fsExtra.ensureDir(outputDir);

    // 创建PDF文件
    const pdfPath = path.join(outputDir, `${documentNo}.pdf`);
    const doc = new PDFDocument();
    const stream = fs.createWriteStream(pdfPath);

    doc.pipe(stream);

    // 添加公司标题
    doc.fontSize(16).text('POLYSEALS SDN BHD', { align: 'center' });
    doc.fontSize(8).text('NO. 28, JALAN PERUSAHAAN AMARI, AMARI BUSINESS PARK, KAWASAN INDUSTRI BATU CAVES, 68100 BATU CAVES, SELANGOR', { align: 'center' });
    doc.text('TEL: 03-6187 8511   FAX: 03-6187 8646   Email: <EMAIL> (ACC)', { align: 'center' });
    doc.moveDown();

    // 添加订单标题
    doc.fontSize(14).text('DELIVERY ORDER', { align: 'center' });
    doc.moveDown();

    // 添加订单信息 - 左侧
    doc.fontSize(10);
    const leftColumnX = 50;
    const rightColumnX = 350;

    doc.text('A/C NO.', leftColumnX, doc.y);
    doc.text(': ' + (main.customer || ''), leftColumnX + 80, doc.y - 12);
    doc.moveDown();

    doc.text('DELIVER TO', leftColumnX, doc.y);
    doc.text(': ' + (main.customer_name || ''), leftColumnX + 80, doc.y - 12);
    doc.moveDown();

    // 添加地址行（示例，实际应从数据库获取）
    const address = main.remarks ? main.remarks.split('\n') : [];
    if (address.length > 0) {
      for (const line of address) {
        doc.text(line, leftColumnX + 80, doc.y);
        doc.moveDown();
      }
    } else {
      doc.moveDown(2);
    }

    // 添加联系方式
    doc.text('TEL', leftColumnX, doc.y);
    doc.text(': ', leftColumnX + 80, doc.y - 12);
    doc.moveDown();

    doc.text('FAX', leftColumnX, doc.y);
    doc.text(': ', leftColumnX + 80, doc.y - 12);
    doc.moveDown(2);

    // 添加订单信息 - 右侧
    const initialY = 140; // 调整此值以匹配左侧起始位置

    doc.text('TRAN. NO.', rightColumnX, initialY);
    doc.text(': ' + main.document_no, rightColumnX + 80, initialY);

    doc.text('DATE', rightColumnX, initialY + 20);
    doc.text(': ' + main.document_date.toISOString().split('T')[0], rightColumnX + 80, initialY + 20);

    doc.text('SALESMAN', rightColumnX, initialY + 40);
    doc.text(': ' + (main.salesman_code || ''), rightColumnX + 80, initialY + 40);

    doc.text('TERM', rightColumnX, initialY + 60);
    doc.text(': ' + (main.term || 'CASH'), rightColumnX + 80, initialY + 60);

    doc.text('P/O NO.', rightColumnX, initialY + 80);
    doc.text(': ', rightColumnX + 80, initialY + 80);

    doc.text('PAGE NO.', rightColumnX, initialY + 100);
    doc.text(': 1/1', rightColumnX + 80, initialY + 100);

    // 重置Y位置到表格开始处
    doc.y = Math.max(doc.y, initialY + 130);

    // 添加表格标题
    const tableTop = doc.y;
    const tableWidth = 500;
    const colWidths = {
      no: 30,
      stockCode: 80,
      description: 150,
      bin: 80,
      brand: 50,
      qty: 40,
      unitPrice: 40,
      amount: 50
    };

    // 绘制表头上边框
    doc.moveTo(50, tableTop).lineTo(50 + tableWidth, tableTop).stroke();
    // 绘制表头下边框
    doc.moveTo(50, tableTop + 20).lineTo(50 + tableWidth, tableTop + 20).stroke();
    doc.fontSize(8);

    let currentX = 50;
    doc.text('NO', currentX + 5, tableTop + 5, { width: colWidths.no });
    currentX += colWidths.no;

    doc.text('STOCK CODE', currentX + 5, tableTop + 5, { width: colWidths.stockCode });
    currentX += colWidths.stockCode;

    doc.text('DESCRIPTION', currentX + 5, tableTop + 5, { width: colWidths.description });
    currentX += colWidths.description;

    doc.text('BIN', currentX + 5, tableTop + 5, { width: colWidths.bin });
    currentX += colWidths.bin;

    doc.text('BRAND', currentX + 5, tableTop + 5, { width: colWidths.brand });
    currentX += colWidths.brand;

    doc.text('QTY', currentX + 5, tableTop + 5, { width: colWidths.qty });
    currentX += colWidths.qty;

    doc.text('U/PRICE', currentX + 5, tableTop + 5, { width: colWidths.unitPrice });
    currentX += colWidths.unitPrice;

    doc.text('AMOUNT', currentX + 5, tableTop + 5, { width: colWidths.amount });

    // 添加明细行
    let rowY = tableTop + 20;
    let totalAmount = 0;

    for (const detail of details) {
      const rowHeight = 20;
      // 移除行边框，不再绘制矩形

      // 设置明细内容的字体大小
      doc.fontSize(8);
      currentX = 50;
      doc.text(detail.line.toString(), currentX + 5, rowY + 5, { width: colWidths.no });
      currentX += colWidths.no;

      doc.text(detail.stock, currentX + 5, rowY + 5, { width: colWidths.stockCode });
      currentX += colWidths.stockCode;

      doc.text(detail.description, currentX + 5, rowY + 5, { width: colWidths.description });
      currentX += colWidths.description;

      doc.text(detail.bin_shelf_no, currentX + 5, rowY + 5, { width: colWidths.bin });
      currentX += colWidths.bin;

      // 品牌字段（示例，实际应从数据库获取）
      const brand = ''; // 如果有品牌字段，应从detail中获取
      doc.text(brand, currentX + 5, rowY + 5, { width: colWidths.brand });
      currentX += colWidths.brand;

      doc.text(`${Math.floor(detail.quantity)} ${detail.uom || 'pcs'}`, currentX + 5, rowY + 5, { width: colWidths.qty });
      currentX += colWidths.qty;

      // 单价和金额（示例，实际应从数据库获取）
      const unitPrice = 0; // 如果有单价字段，应从detail中获取
      const amount = detail.quantity * unitPrice;
      totalAmount += amount;

      doc.text(unitPrice.toFixed(2), currentX + 5, rowY + 5, { width: colWidths.unitPrice });
      currentX += colWidths.unitPrice;

      doc.text(amount.toFixed(2), currentX + 5, rowY + 5, { width: colWidths.amount });

      rowY += rowHeight;
    }

    // 恢复字体大小
    doc.fontSize(8);

    // 添加备注
    if (main.remarks) {
      doc.fontSize(8);
      doc.text('REMARKS :', 50, rowY + 10);
      doc.text(main.remarks, 120, rowY + 10, { width: 430 });
      rowY += 30; // 为备注留出空间
    } else {
      rowY += 10;
    }

    // 添加底部总计和免责声明，只保留上边框
    doc.moveTo(50, rowY).lineTo(50 + tableWidth, rowY).stroke();

    // 添加底部免责声明（左侧）
    doc.fontSize(7);
    doc.text('No claim or goods return will be accepted after 7 days from date of delivery.', 50, rowY + 5, { width: 330 });
    doc.text('Responsibility of seller ceased with deliver of merchandise in good order & condition', 50, rowY + 15, { width: 330 });
    doc.text('to public carrier or consignee.', 50, rowY + 25, { width: 330 });

    // 添加底部总计（右侧）
    doc.fontSize(8);
    doc.text('TOTAL : RM', 400, rowY + 15);
    doc.text('0.00', 480, rowY + 15); // 实际应显示totalAmount

    // 更新rowY位置
    rowY += 30;

    // 添加签名区域
    rowY += 40;
    doc.fontSize(8);
    doc.text('CHECKED BY', 80, rowY, { align: 'center' });
    doc.text('DELIVER BY', 250, rowY, { align: 'center' });
    doc.text('RECEIVED BY', 420, rowY, { align: 'center' });

    // 添加签名线
    doc.moveTo(50, rowY - 20).lineTo(150, rowY - 20).stroke();
    doc.moveTo(200, rowY - 20).lineTo(300, rowY - 20).stroke();
    // RECEIVED BY上方添加一条线
    doc.moveTo(350, rowY - 40).lineTo(450, rowY - 40).stroke();
    // RECEIVED BY下方的线
    doc.moveTo(350, rowY - 20).lineTo(450, rowY - 20).stroke();

    // 添加发行人信息
    doc.text('ISSUED BY : ' + (main.issue_by || ''), 450, rowY + 20);

    // 添加生成时间（可选，不在示例图中）
    doc.moveDown(2);
    doc.fontSize(6).text(`Generated on: ${new Date().toLocaleString()}`, { align: 'right' });

    // 完成PDF
    doc.end();

    // 等待文件写入完成
    return new Promise((resolve, reject) => {
      stream.on('finish', async () => {
        try {
          // PDF生成成功后，尝试上传到Firebase Storage
          await this.uploadPdfToFirebase(pdfPath, documentNo);
          resolve(pdfPath);
        } catch (error) {
          // Firebase上传失败不应该影响PDF生成的主流程
          console.error(`[PDF] Firebase上传失败，但PDF生成成功: ${error.message}`);
          resolve(pdfPath);
        }
      });
      stream.on('error', reject);
    });
  }

  /**
   * 将签名添加到现有的PDF文件中
   * @param documentNo 订单编号
   * @param signatureBase64 签名的Base64编码
   * @param date 可选的日期参数，默认为当天
   * @returns 更新后的PDF文件路径
   */
  async addSignatureToPdf(documentNo: string, signatureBase64: string, date?: string): Promise<string> {
    // 获取订单主表数据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo },
    });

    if (!main) {
      throw new Error(`Document ${documentNo} not found`);
    }

    // 确定使用的日期 - 优先使用出货日期，其次使用提供的日期，最后使用当前日期
    let dateStr = date;
    if (!dateStr) {
      if (main.shipped_at) {
        const shippedDate = new Date(main.shipped_at);
        dateStr = `${shippedDate.getFullYear()}-${String(shippedDate.getMonth() + 1).padStart(2, '0')}-${String(shippedDate.getDate()).padStart(2, '0')}`;
        console.log(`[PDF] 使用出货日期: ${dateStr}`);
      } else {
        const today = new Date();
        dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
        console.log(`[PDF] 使用当前日期: ${dateStr}`);
      }
    }



    // 保存签名数据到数据库
    console.log(`[PDF] 保存签名数据到数据库`);
    await this.mainRepository.update(
      { document_no: documentNo },
      {
        customer_signature: signatureBase64,
        signature_date: dateStr,
        signed_at: new Date(),
      }
    );
    console.log(`[PDF] 签名数据已保存到数据库`);

    // 智能确定PDF文件路径
    const pdfPath = await this.findPdfPath(documentNo, main, dateStr);
    console.log(`[PDF] 智能确定的PDF文件路径: ${pdfPath}`);

    // 检查文件是否存在，如果不存在则生成
    if (!fs.existsSync(pdfPath)) {
      console.log(`[PDF] PDF文件不存在，需要生成: ${pdfPath}`);

      try {
        await this.generateOrderPdf(documentNo);
        console.log(`[PDF] PDF文件生成成功`);
      } catch (genError) {
        console.error(`[PDF] 生成PDF文件失败: ${genError.message}`);
        throw new Error(`Failed to generate PDF file for document ${documentNo}: ${genError.message}`);
      }
    } else {
      console.log(`[PDF] 找到现有PDF文件: ${pdfPath}`);
    }

    // 获取PDF目录
    const pdfDir = path.dirname(pdfPath);

    // 创建临时文件路径
    const tempPdfPath = path.join(pdfDir, `${documentNo}_signed_temp.pdf`);

    // 处理签名数据
    let signatureImagePath: string | null = null;
    const hasSignature = signatureBase64 && signatureBase64.trim() !== '';

    if (hasSignature) {
      // 解码Base64签名
      const signatureBuffer = Buffer.from(signatureBase64, 'base64');

      // 创建临时签名图片文件
      signatureImagePath = path.join(pdfDir, `${documentNo}_signature_temp.png`);
      fs.writeFileSync(signatureImagePath, signatureBuffer);
    }

    // 获取订单明细数据
    let details = await this.detailRepository.find({
      where: { main_id: main.id },
    });

    // 按行号排序明细数据
    details = details.sort((a, b) => a.line - b.line);

    // 创建新的PDF文档
    const doc = new PDFDocument();
    const writeStream = fs.createWriteStream(tempPdfPath);
    doc.pipe(writeStream);

    // 添加公司标题
    doc.fontSize(16).text('POLYSEALS SDN BHD', { align: 'center' });
    doc.fontSize(8).text('NO. 28, JALAN PERUSAHAAN AMARI, AMARI BUSINESS PARK, KAWASAN INDUSTRI BATU CAVES, 68100 BATU CAVES, SELANGOR', { align: 'center' });
    doc.text('TEL: 03-6187 8511   FAX: 03-6187 8646   Email: <EMAIL> (ACC)', { align: 'center' });
    doc.moveDown();

    // 添加订单标题
    doc.fontSize(14).text('DELIVERY ORDER', { align: 'center' });
    doc.moveDown();

    // 添加订单信息 - 左侧
    doc.fontSize(10);
    const leftColumnX = 50;
    const rightColumnX = 350;

    doc.text('A/C NO.', leftColumnX, doc.y);
    doc.text(': ' + (main.customer || ''), leftColumnX + 80, doc.y - 12);
    doc.moveDown();

    doc.text('DELIVER TO', leftColumnX, doc.y);
    doc.text(': ' + (main.customer_name || ''), leftColumnX + 80, doc.y - 12);
    doc.moveDown();

    // 添加客户送货地址
    if (main.customer_deliver_address) {
      doc.text(main.customer_deliver_address, leftColumnX + 80, doc.y);
      doc.moveDown();
    } else {
      doc.moveDown(2);
    }

    // 添加联系方式
    doc.text('TEL', leftColumnX, doc.y);
    doc.text(': ', leftColumnX + 80, doc.y - 12);
    doc.moveDown();

    doc.text('FAX', leftColumnX, doc.y);
    doc.text(': ', leftColumnX + 80, doc.y - 12);
    doc.moveDown(2);

    // 添加订单信息 - 右侧
    const initialY = 140; // 调整此值以匹配左侧起始位置

    doc.text('TRAN. NO.', rightColumnX, initialY);
    doc.text(': ' + main.document_no, rightColumnX + 80, initialY);

    doc.text('DATE', rightColumnX, initialY + 20);
    doc.text(': ' + main.document_date.toISOString().split('T')[0], rightColumnX + 80, initialY + 20);

    doc.text('SALESMAN', rightColumnX, initialY + 40);
    doc.text(': ' + (main.salesman_code || ''), rightColumnX + 80, initialY + 40);

    doc.text('TERM', rightColumnX, initialY + 60);
    doc.text(': ' + (main.term || 'CASH'), rightColumnX + 80, initialY + 60);

    doc.text('P/O NO.', rightColumnX, initialY + 80);
    doc.text(': ', rightColumnX + 80, initialY + 80);

    doc.text('PAGE NO.', rightColumnX, initialY + 100);
    doc.text(': 1/1', rightColumnX + 80, initialY + 100);

    // 重置Y位置到表格开始处
    doc.y = Math.max(doc.y, initialY + 130);

    // 添加表格标题
    const tableTop = doc.y;
    const tableWidth = 500;
    const colWidths = {
      no: 30,
      stockCode: 80,
      description: 150,
      bin: 80,
      brand: 50,
      qty: 40,
      unitPrice: 40,
      amount: 50
    };

    // 绘制表头上边框
    doc.moveTo(50, tableTop).lineTo(50 + tableWidth, tableTop).stroke();
    // 绘制表头下边框
    doc.moveTo(50, tableTop + 20).lineTo(50 + tableWidth, tableTop + 20).stroke();
    doc.fontSize(8);

    let currentX = 50;
    doc.text('NO', currentX + 5, tableTop + 5, { width: colWidths.no });
    currentX += colWidths.no;

    doc.text('STOCK CODE', currentX + 5, tableTop + 5, { width: colWidths.stockCode });
    currentX += colWidths.stockCode;

    doc.text('DESCRIPTION', currentX + 5, tableTop + 5, { width: colWidths.description });
    currentX += colWidths.description;

    doc.text('BIN', currentX + 5, tableTop + 5, { width: colWidths.bin });
    currentX += colWidths.bin;

    doc.text('BRAND', currentX + 5, tableTop + 5, { width: colWidths.brand });
    currentX += colWidths.brand;

    doc.text('QTY', currentX + 5, tableTop + 5, { width: colWidths.qty });
    currentX += colWidths.qty;

    doc.text('U/PRICE', currentX + 5, tableTop + 5, { width: colWidths.unitPrice });
    currentX += colWidths.unitPrice;

    doc.text('AMOUNT', currentX + 5, tableTop + 5, { width: colWidths.amount });

    // 添加明细行
    let rowY = tableTop + 20;
    let totalAmount = 0;

    for (const detail of details) {
      const rowHeight = 20;
      // 移除行边框，不再绘制矩形

      // 设置明细内容的字体大小
      doc.fontSize(8);
      currentX = 50;
      doc.text(detail.line.toString(), currentX + 5, rowY + 5, { width: colWidths.no });
      currentX += colWidths.no;

      doc.text(detail.stock, currentX + 5, rowY + 5, { width: colWidths.stockCode });
      currentX += colWidths.stockCode;

      doc.text(detail.description, currentX + 5, rowY + 5, { width: colWidths.description });
      currentX += colWidths.description;

      doc.text(detail.bin_shelf_no, currentX + 5, rowY + 5, { width: colWidths.bin });
      currentX += colWidths.bin;

      // 品牌字段
      const brand = detail.brand_code || ''; // 使用brand_code字段
      doc.text(brand, currentX + 5, rowY + 5, { width: colWidths.brand });
      currentX += colWidths.brand;

      doc.text(`${Math.floor(detail.quantity)} ${detail.uom || 'pcs'}`, currentX + 5, rowY + 5, { width: colWidths.qty });
      currentX += colWidths.qty;

      // 单价和金额
      const unitPrice = detail.unit_price || 0; // 使用unit_price字段
      const amount = detail.total_amount || (detail.quantity * unitPrice); // 使用total_amount字段或计算
      totalAmount += amount;

      doc.text(unitPrice.toFixed(2), currentX + 5, rowY + 5, { width: colWidths.unitPrice });
      currentX += colWidths.unitPrice;

      doc.text(amount.toFixed(2), currentX + 5, rowY + 5, { width: colWidths.amount });

      rowY += rowHeight;
    }

    // 恢复字体大小
    doc.fontSize(8);

    // 添加备注
    if (main.remarks) {
      doc.fontSize(8);
      doc.text('REMARKS :', 50, rowY + 10);
      doc.text(main.remarks, 120, rowY + 10, { width: 430 });
      rowY += 30; // 为备注留出空间
    } else {
      rowY += 10;
    }

    // 添加底部总计和免责声明，只保留上边框
    doc.moveTo(50, rowY).lineTo(50 + tableWidth, rowY).stroke();

    // 添加底部免责声明（左侧）
    doc.fontSize(7);
    doc.text('No claim or goods return will be accepted after 7 days from date of delivery.', 50, rowY + 5, { width: 330 });
    doc.text('Responsibility of seller ceased with deliver of merchandise in good order & condition', 50, rowY + 15, { width: 330 });
    doc.text('to public carrier or consignee.', 50, rowY + 25, { width: 330 });

    // 添加底部总计（右侧）
    doc.fontSize(8);
    doc.text('TOTAL : RM', 400, rowY + 15);
    doc.text(totalAmount.toFixed(2), 480, rowY + 15); // 显示计算的总金额

    // 更新rowY位置
    rowY += 30;

    // 添加签名区域
    rowY += 40;
    doc.fontSize(8);
    doc.text('CHECKED BY', 80, rowY, { align: 'center' });
    doc.text('DELIVER BY', 250, rowY, { align: 'center' });
    doc.text('RECEIVED BY', 420, rowY, { align: 'center' });

    // 添加CHECKED BY和DELIVER BY的签名线
    doc.moveTo(50, rowY - 20).lineTo(150, rowY - 20).stroke();
    doc.moveTo(200, rowY - 20).lineTo(300, rowY - 20).stroke();

    // 添加客户签名到"RECEIVED BY"位置
    if (signatureImagePath && hasSignature) {
      try {
        // 先添加签名图像
        doc.image(signatureImagePath, 350, rowY - 60, { width: 100 });

        // 在签名图像下方添加签名线（RECEIVED BY上方）
        doc.moveTo(350, rowY - 20).lineTo(450, rowY - 20).stroke();
      } catch (error) {
        console.error('Error adding signature image:', error);
        // 如果图片添加失败，添加两条签名线
        doc.moveTo(350, rowY - 40).lineTo(450, rowY - 40).stroke(); // 上方线
        doc.moveTo(350, rowY - 20).lineTo(450, rowY - 20).stroke(); // 下方线
        // 添加一个占位符
        doc.text('(Signature image could not be loaded)', 350, rowY - 40);
      }
    } else {
      // 没有签名时，只添加签名线
      doc.moveTo(350, rowY - 40).lineTo(450, rowY - 40).stroke(); // 上方线
      doc.moveTo(350, rowY - 20).lineTo(450, rowY - 20).stroke(); // 下方线
    }

    // 添加发行人信息
    doc.text('ISSUED BY : ' + (main.issue_by || ''), 450, rowY + 20);

    // 添加生成和签名时间
    doc.moveDown(2);
    doc.fontSize(6).text(`Generated on: ${new Date().toLocaleString()}`, { align: 'right' });
    doc.fontSize(6).text(`Signed on: ${new Date().toLocaleString()}`, { align: 'right' });

    // 完成PDF
    doc.end();

    // 等待文件写入完成
    return new Promise((resolve, reject) => {
      writeStream.on('finish', async () => {
        try {
          // 删除原始PDF文件
          fs.unlinkSync(pdfPath);
          // 重命名临时文件为原始文件名
          fs.renameSync(tempPdfPath, pdfPath);
          // 删除临时签名图片（如果存在）
          if (signatureImagePath && fs.existsSync(signatureImagePath)) {
            fs.unlinkSync(signatureImagePath);
          }

          // 签名PDF生成成功后，尝试上传到Firebase Storage
          await this.uploadPdfToFirebase(pdfPath, documentNo);

          resolve(pdfPath);
        } catch (error) {
          // Firebase上传失败不应该影响PDF生成的主流程
          if (error.message && error.message.includes('Firebase')) {
            console.error(`[PDF] Firebase上传失败，但签名PDF生成成功: ${error.message}`);
            resolve(pdfPath);
          } else {
            reject(error);
          }
        }
      });
      writeStream.on('error', (error) => {
        // 清理临时文件
        if (fs.existsSync(tempPdfPath)) {
          fs.unlinkSync(tempPdfPath);
        }
        if (signatureImagePath && fs.existsSync(signatureImagePath)) {
          fs.unlinkSync(signatureImagePath);
        }
        reject(error);
      });
    });
  }

  /**
   * 智能确定PDF文件路径
   * 根据订单信息确定正确的PDF文件路径
   */
  private async findPdfPath(documentNo: string, main: any, fallbackDate: string): Promise<string> {
    const baseDir = process.env.PDF_OUTPUT_DIR || path.join(__dirname, '..', '..', 'PDF_Output');

    // 尝试多个可能的日期路径
    const possibleDates: Date[] = [];

    // 优先使用出货日期
    if (main.shipped_at) {
      possibleDates.push(new Date(main.shipped_at));
    }

    // 其次使用文档日期
    if (main.document_date) {
      possibleDates.push(new Date(main.document_date));
    }

    // 最后使用创建日期
    if (main.created_at) {
      possibleDates.push(new Date(main.created_at));
    }

    // 检查每个可能的日期路径
    for (const date of possibleDates) {
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const pdfPath = path.join(baseDir, dateStr, `${documentNo}.pdf`);

      console.log(`[PDF] 检查路径: ${pdfPath}`);
      if (fs.existsSync(pdfPath)) {
        console.log(`[PDF] 找到PDF文件: ${pdfPath}`);
        return pdfPath;
      }
    }

    // 如果都没找到，使用fallback日期
    const pdfPath = path.join(baseDir, fallbackDate, `${documentNo}.pdf`);
    console.log(`[PDF] 使用fallback日期路径: ${pdfPath}`);
    return pdfPath;
  }

  /**
   * 上传PDF文件到Firebase Storage
   * @param pdfPath 本地PDF文件路径
   * @param documentNo 订单编号
   */
  private async uploadPdfToFirebase(pdfPath: string, documentNo: string): Promise<void> {
    try {
      console.log(`[PDF] 开始上传PDF到Firebase: ${documentNo}`);

      // 检查Firebase服务状态
      const firebaseStatus = this.firebaseService.getStatus();
      if (!firebaseStatus.enabled) {
        console.log(`[PDF] Firebase上传已禁用，跳过上传: ${documentNo}`);
        return;
      }

      if (!firebaseStatus.initialized) {
        console.warn(`[PDF] Firebase服务未初始化，跳过上传: ${documentNo}`);
        return;
      }

      // 执行上传
      const uploadResult = await this.firebaseService.uploadPdf(pdfPath, documentNo);

      if (uploadResult.success) {
        console.log(`[PDF] Firebase上传成功: ${documentNo}`);
        console.log(`[PDF] Firebase下载URL: ${uploadResult.downloadUrl}`);
        console.log(`[PDF] Firebase路径: ${uploadResult.firebasePath}`);
      } else {
        console.error(`[PDF] Firebase上传失败: ${documentNo} -> ${uploadResult.error}`);
      }
    } catch (error) {
      // Firebase上传失败不应该影响PDF生成的主流程
      console.error(`[PDF] Firebase上传异常: ${documentNo} -> ${error.message}`);
    }
  }
}
