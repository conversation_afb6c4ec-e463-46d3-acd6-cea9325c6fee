import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Detail } from './detail.entity';

@Entity('delivery_order_main')
export class Main {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '文档编号', example: 'DO123456' })
  @Column({ type: 'varchar', length: 50, unique: true })
  document_no: string;

  @ApiProperty({ description: '文档日期', example: '2025-04-04' })
  @Column()
  document_date: Date;

  @ApiProperty({ description: '客户代码', example: 'CUST001' })
  @Column()
  customer: string;

  @ApiProperty({ description: '客户名称', example: '测试客户' })
  @Column({ nullable: true })
  customer_name: string;

  @ApiProperty({ description: '客户邮箱', example: '<EMAIL>' })
  @Column({ nullable: true })
  customer_email: string;

  @ApiProperty({ description: '备注', example: '紧急订单' })
  @Column()
  remarks: string;

  @ApiProperty({ description: '创建时间', example: '2025-04-04T12:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @ApiProperty({ description: '是否已出货', example: false })
  @Column({ default: false })
  is_shipped: boolean;

  @ApiProperty({ description: '出货时间', example: '2025-04-05T12:00:00Z' })
  @Column({ nullable: true })
  shipped_at: Date;

  @ApiProperty({ description: '出货人ID', example: 2 })
  @Column({ nullable: true })
  shipped_by: number;

  @ApiProperty({ description: '是否已送达', example: false })
  @Column({ default: false })
  delivered: boolean;

  @ApiProperty({ description: '送达时间', example: '2025-04-06T12:00:00Z' })
  @Column({ nullable: true })
  delivered_at: Date;

  @ApiProperty({ description: '送达确认人ID（司机）', example: 3 })
  @Column({ nullable: true })
  delivered_by: number;

  @ApiProperty({ description: '送达备注', example: '已送达客户仓库，验收完毕' })
  @Column({ nullable: true, length: 255 })
  delivery_notes: string;

  @ApiProperty({ description: '指派司机ID', example: 3 })
  @Column({ nullable: true })
  driver_id: number;

  @ApiProperty({ description: '拒绝原因', example: '车辆故障无法送达' })
  @Column({ nullable: true, length: 255 })
  rejection_reason: string;

  @ApiProperty({ description: '拒绝人ID', example: 3 })
  @Column({ nullable: true })
  rejected_by: number;

  @ApiProperty({ description: '拒绝时间', example: '2025-04-06T12:00:00Z' })
  @Column({ nullable: true })
  rejected_at: Date;

  @ApiProperty({ description: '运输商代码', example: 'TRANS001' })
  @Column({ nullable: true, length: 50 })
  transporter_code: string;

  @ApiProperty({ description: '运输商名称', example: '某物流公司' })
  @Column({ nullable: true, length: 100 })
  transporter_name: string;

  @ApiProperty({ description: '客户送货地址', example: '123 Main St' })
  @Column({ nullable: true, length: 255 })
  customer_deliver_address: string;

  @ApiProperty({ description: '客户电话', example: '************' })
  @Column({ nullable: true, length: 255 })
  customer_telephone: string;

  @ApiProperty({ description: '客户传真', example: '************' })
  @Column({ nullable: true, length: 50 })
  customer_fax: string;

  @ApiProperty({ description: '销售员代码', example: 'S001' })
  @Column({ nullable: true, length: 10 })
  salesman_code: string;

  @ApiProperty({ description: '发行人', example: 'John Doe' })
  @Column({ nullable: true, length: 20 })
  issue_by: string;

  @ApiProperty({ description: '付款条件', example: 'CASH' })
  @Column({ nullable: true, length: 50 })
  term: string;

  @ApiProperty({ description: 'PO号码', example: 'P1234' })
  @Column({ nullable: true, length: 20 })
  po_no: string;

  @ApiProperty({ description: '外币代码', example: 'USD' })
  @Column({ nullable: true, length: 10 })
  forex_code: string;

  @ApiProperty({ description: '优先级', example: 0 })
  @Column({ default: 0 })
  priority: number;

  @ApiProperty({ description: '客户签名Base64数据', example: 'iVBORw0KGgoAAAANSUhEUgAA...' })
  @Column({ nullable: true, type: 'text' })
  customer_signature: string;

  @ApiProperty({ description: '签名日期', example: '2025-01-15' })
  @Column({ nullable: true, length: 10 })
  signature_date: string;

  @ApiProperty({ description: '签名时间', example: '2025-01-15T14:30:00Z' })
  @Column({ nullable: true })
  signed_at: Date;

  @ApiProperty({ description: 'Firebase Storage下载链接', example: 'https://storage.googleapis.com/...' })
  @Column({ nullable: true, type: 'text' })
  firebase_download_url: string;

  @ApiProperty({ description: 'Firebase Storage文件路径', example: 'pdfs/2025-06-19/DOC001.pdf' })
  @Column({ nullable: true })
  firebase_file_path: string;

  @ApiProperty({ description: 'Firebase上传时间', example: '2025-06-19T14:30:00Z' })
  @Column({ nullable: true })
  firebase_uploaded_at: Date;

  @OneToMany(() => Detail, detail => detail.main)
  details: Detail[];
}