# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)

# NestJS项目忽略规则

# 忽略node_modules目录和依赖相关文件
**/node_modules
**/package-lock.json
**/yarn.lock
**/pnpm-lock.yaml

# 忽略编译输出目录
**/dist
**/build
**/.nest

# 忽略环境配置文件（包含敏感信息）
**/.env
**/.env.*
!**/.env.example

# 忽略日志文件
**/logs
**/*.log
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# 忽略测试覆盖率报告
**/coverage

# 忽略IDE和编辑器配置文件
**/.idea
**/.vscode
**/*.sublime-*
**/*.swp
**/*.swo

# 忽略操作系统文件
**/.DS_Store
**/Thumbs.db

# 忽略临时文件和缓存
**/.tmp
**/.temp
**/.cache

# 忽略上传文件目录
**/uploads

# NestJS特定忽略
**/.nestcli.json
**/.eslintrc.js
**/.prettierrc
**/tsconfig.json
**/tsconfig.*.json
