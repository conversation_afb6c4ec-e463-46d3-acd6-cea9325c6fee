import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as dotenv from 'dotenv';
import * as path from 'path';
import * as hbs from 'hbs';

// 加载环境变量
dotenv.config();

// 调试SQL Server连接信息
console.log('SQL Server连接信息:');
console.log('- 主机:', process.env.SQL_SERVER_HOST);
console.log('- 端口:', process.env.SQL_SERVER_PORT);
console.log('- 用户名:', process.env.SQL_SERVER_USERNAME);
console.log('- 数据库:', process.env.SQL_SERVER_DATABASE);
console.log('- 密码长度:', process.env.SQL_SERVER_PASSWORD ? process.env.SQL_SERVER_PASSWORD.length : 0);
console.log('- 原始密码前4字符:', process.env.SQL_SERVER_PASSWORD ? process.env.SQL_SERVER_PASSWORD.substring(0, 4) : '');

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // 配置视图引擎
  app.setBaseViewsDir(path.join(__dirname, '..', 'src/status/views'));
  app.setViewEngine('hbs');

  // 配置静态文件服务
  app.useStaticAssets(path.join(__dirname, '..', 'public'));

  // 启用CORS
  app.enableCors();

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('SQL Sync API')
    .setDescription('SQL Server到PostgreSQL数据同步API')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);

  // 获取服务器IP地址
  const networkInterfaces = require('os').networkInterfaces();
  const addresses = [];
  for (const name of Object.keys(networkInterfaces)) {
    for (const net of networkInterfaces[name]) {
      // 跳过内部IP和非IPv4地址
      if (!net.internal && net.family === 'IPv4') {
        addresses.push(net.address);
      }
    }
  }

  // 打印服务器地址
  const port = 3000;
  console.log(`服务器启动成功，可通过以下地址访问:`);
  console.log(`本地访问: http://localhost:${port}`);
  addresses.forEach(ip => {
    console.log(`网络访问: http://${ip}:${port}`);
    console.log(`状态页面: http://${ip}:${port}/status`);
  });

  await app.listen(port);
}
bootstrap();