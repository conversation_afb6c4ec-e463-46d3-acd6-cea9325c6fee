import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not, Like, Between } from 'typeorm';
import { Detail } from '../postgres/entities/detail.entity';
import { Main } from '../postgres/entities/main.entity';
import { Staff } from '../postgres/entities/staff.entity';

@Injectable()
export class StatusService {
  constructor(
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
  ) { }

  // 获取系统整体状态
  async getSystemStatus() {
    // 获取各阶段的检查状态
    const [
      pendingStaffCheck,
      pendingBomCheck,
      pendingSupervisorCheck,
      readyToShip,
      inDelivery,
      delivered
    ] = await Promise.all([
      // 待员工检查的货物
      this.detailRepository.count({
        where: { staff_checked: false }
      }),
      // 待BOM专员检查的货物
      this.detailRepository.count({
        where: {
          staff_checked: true,
          bom_specialist_checked: false,
          parent_code: Not(IsNull()) // 非空
        }
      }),
      // 待主管检查的货物
      this.detailRepository.count({
        where: {
          staff_checked: true,
          supervisor_checked: false,
          // 对于BOM物品，还需要已被BOM专员检查
          bom_specialist_checked: true
        }
      }),
      // 准备出货的订单
      this.mainRepository.count({
        where: {
          is_shipped: false,
          // 所有明细都已被主管检查
        }
      }),
      // 正在配送的订单
      this.mainRepository.count({
        where: {
          is_shipped: true,
          delivered: false
        }
      }),
      // 已送达的订单
      this.mainRepository.count({
        where: { delivered: true }
      })
    ]);

    // 获取今日统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [
      todayChecked,
      todayShipped,
      todayDelivered
    ] = await Promise.all([
      // 今日检查的货物
      this.detailRepository.count({
        where: {
          staff_checked: true,
          staff_checked_at: Between(today, tomorrow)
        }
      }),
      // 今日出货的订单
      this.mainRepository.count({
        where: {
          is_shipped: true,
          shipped_at: Between(today, tomorrow)
        }
      }),
      // 今日送达的订单
      this.mainRepository.count({
        where: {
          delivered: true,
          delivered_at: Between(today, tomorrow)
        }
      })
    ]);

    // 获取最近检查的5个货物
    const recentChecks = await this.detailRepository.find({
      where: { staff_checked: true },
      order: { staff_checked_at: 'DESC' },
      take: 5,
      relations: ['main']
    });

    // 获取最近出货的5个订单
    const recentShipments = await this.mainRepository.find({
      where: { is_shipped: true },
      order: { shipped_at: 'DESC' },
      take: 5
    });

    return {
      counts: {
        pendingStaffCheck,
        pendingBomCheck,
        pendingSupervisorCheck,
        readyToShip,
        inDelivery,
        delivered
      },
      today: {
        checked: todayChecked,
        shipped: todayShipped,
        delivered: todayDelivered
      },
      recent: {
        checks: recentChecks.map(detail => ({
          documentNo: detail.document_no,
          stockCode: detail.stock,
          description: detail.description,
          checkedAt: detail.staff_checked_at,
          checkedBy: detail.staff_id,
          customer: detail.main?.customer_name || 'Unknown'
        })),
        shipments: recentShipments.map(main => ({
          documentNo: main.document_no,
          customer: main.customer_name,
          shippedAt: main.shipped_at,
          shippedBy: main.shipped_by
        }))
      }
    };
  }

  // 获取特定楼层的状态
  async getFloorStatus(floor: string) {
    // 获取该楼层的检查状态
    const [
      pendingStaffCheck,
      pendingBomCheck,
      pendingSupervisorCheck
    ] = await Promise.all([
      // 待员工检查的货物
      this.detailRepository.count({
        where: {
          staff_checked: false,
          bin_shelf_no: floor ? Like(`${floor}-%`) : undefined
        }
      }),
      // 待BOM专员检查的货物
      this.detailRepository.count({
        where: {
          staff_checked: true,
          bom_specialist_checked: false,
          parent_code: Not(IsNull()), // 非空
          bin_shelf_no: floor ? Like(`${floor}-%`) : undefined
        }
      }),
      // 待主管检查的货物
      this.detailRepository.count({
        where: {
          staff_checked: true,
          supervisor_checked: false,
          bin_shelf_no: floor ? Like(`${floor}-%`) : undefined
        }
      })
    ]);

    return {
      floor,
      counts: {
        pendingStaffCheck,
        pendingBomCheck,
        pendingSupervisorCheck,
        total: pendingStaffCheck + pendingBomCheck + pendingSupervisorCheck
      }
    };
  }

  // 获取待处理和待出货的订单
  async getOrderQueue() {
    // 获取待处理订单 (未完成检查的订单)
    // 使用QueryBuilder获取唯一的document_no，并去除后缀
    const pendingOrdersQuery = this.detailRepository.createQueryBuilder('detail')
      .select('CASE WHEN POSITION(\'/\' IN detail.document_no) > 0 THEN SUBSTRING(detail.document_no, 1, POSITION(\'/\' IN detail.document_no) - 1) ELSE detail.document_no END', 'base_document_no')
      .addSelect('MIN(detail.created_at)', 'created_at')
      .leftJoin('detail.main', 'main')
      .addSelect('main.customer_name', 'customer_name')
      .where('detail.staff_checked = :checked', { checked: false })
      .andWhere('(detail.bom_specialist_checked = :bomChecked OR detail.parent_code IS NULL)', { bomChecked: false })
      .groupBy('CASE WHEN POSITION(\'/\' IN detail.document_no) > 0 THEN SUBSTRING(detail.document_no, 1, POSITION(\'/\' IN detail.document_no) - 1) ELSE detail.document_no END, main.customer_name')
      .orderBy('MIN(detail.created_at)', 'ASC')
      .limit(30);

    const pendingOrdersRaw = await pendingOrdersQuery.getRawMany();

    // 转换为与原来相似的格式
    const pendingOrders = pendingOrdersRaw.map(raw => ({
      document_no: raw.base_document_no, // 使用去除后缀的订单号
      created_at: raw.created_at,
      main: {
        customer_name: raw.customer_name
      }
    }));

    // 获取待出货订单 (已完成检查但未出货的订单)
    // 使用QueryBuilder获取唯一的document_no，并去除后缀
    const toShipOrdersQuery = this.detailRepository.createQueryBuilder('detail')
      .select('CASE WHEN POSITION(\'/\' IN detail.document_no) > 0 THEN SUBSTRING(detail.document_no, 1, POSITION(\'/\' IN detail.document_no) - 1) ELSE detail.document_no END', 'base_document_no')
      .addSelect('MIN(detail.created_at)', 'created_at')
      .leftJoin('detail.main', 'main')
      .addSelect('main.customer_name', 'customer_name')
      .where('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: true })
      .andWhere('detail.staff_checked = :staffChecked', { staffChecked: true })
      .andWhere('(detail.bom_specialist_checked = :bomChecked OR detail.parent_code IS NULL)', { bomChecked: true })
      .groupBy('CASE WHEN POSITION(\'/\' IN detail.document_no) > 0 THEN SUBSTRING(detail.document_no, 1, POSITION(\'/\' IN detail.document_no) - 1) ELSE detail.document_no END, main.customer_name')
      .orderBy('MIN(detail.created_at)', 'ASC')
      .limit(30);

    const toShipOrders = await toShipOrdersQuery.getRawMany();

    // 确保待出货订单不包含已在待处理列表中的订单号
    const pendingDocumentNos = new Set(pendingOrders.map(detail => detail.document_no));
    const filteredToShipOrders = toShipOrders.filter(order => !pendingDocumentNos.has(order.base_document_no));

    // 计算是否有更多未显示的订单
    const hasPendingMore = pendingOrders.length > 15;
    const hasToShipMore = filteredToShipOrders.length > 15;

    // 如果超过15个，只取前15个
    const pendingToDisplay = hasPendingMore ? pendingOrders.slice(0, 15) : pendingOrders;
    const toShipToDisplay = hasToShipMore ? filteredToShipOrders.slice(0, 15) : filteredToShipOrders;

    return {
      pending: pendingToDisplay.map(detail => ({
        documentNo: detail.document_no,
        customer: detail.main?.customer_name || '',
        createdAt: detail.created_at
      })),
      toShip: toShipToDisplay.map(order => ({
        documentNo: order.base_document_no, // 使用去除后缀的订单号
        customer: order.customer_name,
        createdAt: order.created_at
      })),
      hasPendingMore,
      hasToShipMore,
      pendingCount: pendingOrders.length > 15 ? '15+' : pendingOrders.length.toString(),
      toShipCount: filteredToShipOrders.length > 15 ? '15+' : filteredToShipOrders.length.toString()
    };
  }
}
