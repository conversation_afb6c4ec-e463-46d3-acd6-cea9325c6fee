---
description:
globs:
alwaysApply: false
---
# 业务流程说明

## 核心业务流程

### 1. 员工登录流程
1. 员工输入用户名和密码
2. 系统验证凭据
3. 成功登录后，保存用户信息至本地存储
4. 根据员工角色(level)显示对应的功能界面
5. 登录时记录最后登录时间

### 2. 货物检查流程

#### 非BOM物品检查流程
1. 普通员工查看待检查列表，选择非BOM物品
2. 员工检查物品，标记为"员工已检查"(staff_checked = true)
3. 主管查看待确认列表，选择已被员工检查的非BOM物品
4. 主管确认检查结果，标记为"主管已确认"(supervisor_checked = true)

#### BOM物品检查流程
1. 普通员工查看待检查列表，选择BOM物品
2. 员工检查物品，标记为"员工已检查"(staff_checked = true)
3. BOM管理员查看待检查列表，选择已被员工检查的BOM物品
4. BOM管理员检查物品，标记为"BOM专员已检查"(bom_specialist_checked = true)
5. 主管查看待确认列表，选择已被BOM专员检查的BOM物品
6. 主管确认检查结果，标记为"主管已确认"(supervisor_checked = true)

#### 检查流程权限控制
- 普通员工只能查看和检查自己负责楼层的货物
- BOM管理员可以查看和检查所有楼层的BOM物品
- 主管可以查看和确认所有楼层的物品
- 司机拥有与主管相同的楼层权限

### 3. 订单出货流程

#### 主管出货流程
1. 主管查看可出货订单列表(所有物品都已被主管确认)
2. 主管选择出货订单，可选择是否指派司机
3. 若不指派司机，订单直接标记为"已出货"(shipped = true)并完成
4. 若指派司机，订单标记为"已出货"并等待司机确认送达

#### 司机送货流程
1. 司机查看待送达订单列表
2. 司机送货完成后，选择订单标记为"已送达"(delivered = true)
3. 司机可选择添加送货备注信息(delivery_notes)

### 4. 通知系统流程

#### 拒绝检查通知流程
1. 主管或司机拒绝员工检查时提供拒绝原因
2. 系统自动生成通知发送给相关员工
3. 员工登录后查看未读通知
4. 员工通过通知了解被拒绝的原因，重新检查货物
5. 员工标记通知为已读

#### 订单分配通知流程
1. 主管指派司机负责订单送货时
2. 系统自动生成通知发送给被指派的司机
3. 司机登录后查看未读通知，获知被分配的订单信息

## 楼层权限控制

### 楼层权限规则
- 楼层格式限定为: `1F`, `2F`, `3F`
- 主管、司机和BOM管理员可以使用`ALL`值表示有权操作所有楼层
- 系统根据货架位置编码第一个数字识别楼层
   - 例如: `2-1-G001-1` 表示物品在二楼

### 权限判断逻辑
- 如果用户的floor为"2F"，则只能操作bin_shelf_no以"2-"开头的货物
- 如果用户的floor为"ALL"，则可以操作所有楼层的货物

## 特殊字段与格式处理

### 1. DocumentNo格式解析
- 普通格式: `D00001` - 标准订单号
- BOM格式: `D00001/1` - 表示订单D00001的BOM子项1

### 2. BOM关系识别
- 通过parent_code字段识别BOM关系
- parent_code为NULL表示非BOM物品
- parent_code有值表示该物品是某个BOM的子项

### 3. 检查状态管理
- staff_checked: 普通员工检查状态
- bom_specialist_checked: BOM专员检查状态(仅对BOM物品有效)
- supervisor_checked: 主管确认状态

## 缓存与离线处理

### 1. 用户凭证缓存
- 登录成功后，用户凭证存储在SharedPreferences中
- 应用启动时尝试从缓存加载凭证实现自动登录

### 2. 检查列表缓存
- 首次加载检查列表后缓存在内存中
- 提供手动刷新功能以更新列表
- 执行检查操作后自动刷新列表

### 3. 网络连接管理
- 使用connectivity_plus包监控网络连接状态
- 在无网络时显示离线提示
- 网络恢复后自动重新连接和同步数据
