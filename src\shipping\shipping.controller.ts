import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ShippingService } from './shipping.service';

@ApiTags('订单出货')
@Controller('shipping')
export class ShippingController {
  constructor(
    private readonly shippingService: ShippingService
  ) { }

  @ApiOperation({ summary: '获取司机待送达订单列表' })
  @ApiQuery({ name: 'driverId', description: '司机ID' })
  @ApiResponse({
    status: 200,
    description: '返回该司机待送达的订单列表',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          document_no: { type: 'string', example: 'DO-2024-001' },
          document_date: { type: 'string', format: 'date-time' },
          customer: { type: 'string', example: 'CUST001' },
          customer_name: { type: 'string', example: '测试客户' },
          is_shipped: { type: 'boolean', example: true },
          shipped_at: { type: 'string', format: 'date-time' },
          shipped_by: { type: 'number', example: 3 },
          delivered: { type: 'boolean', example: false }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '司机不存在' })
  @Get('delivery-list')
  async getDeliveryList(@Query('driverId') driverId: number) {
    return this.shippingService.getDeliveryList(driverId);
  }

  @ApiOperation({ summary: '获取主管指派的订单列表' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiQuery({ name: 'page', description: '页码', required: false })
  @ApiQuery({ name: 'limit', description: '每页数量', required: false })
  @ApiResponse({
    status: 200,
    description: '返回主管出货并指派给司机的订单列表及其送达状态',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              document_no: { type: 'string', example: 'D00001' },
              document_date: { type: 'string', format: 'date-time' },
              customer: { type: 'string', example: 'A0001' },
              customer_name: { type: 'string', example: 'Atas Com' },
              is_shipped: { type: 'boolean', example: true },
              shipped_at: { type: 'string', format: 'date-time' },
              shipped_by: { type: 'number', example: 3 },
              driver_username: { type: 'string', example: 'tom.driver' },
              delivered: { type: 'boolean', example: true },
              delivered_at: { type: 'string', format: 'date-time' },
              delivered_by: { type: 'number', example: 3 },
              delivery_notes: { type: 'string', example: '已送达客户仓库，验收完毕' }
            }
          }
        },
        total: { type: 'number', example: 50 },
        hasMore: { type: 'boolean', example: true }
      }
    }
  })
  @ApiResponse({ status: 404, description: '主管不存在' })
  @ApiResponse({ status: 403, description: '无权访问此列表' })
  @Get('supervisor-shipments')
  async getSupervisorShipments(
    @Query('supervisorId') supervisorId: number,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') searchQuery?: string
  ) {
    return this.shippingService.getSupervisorShipments(supervisorId, page, limit, searchQuery);
  }

  @ApiOperation({ summary: '获取所有司机列表' })
  @ApiResponse({
    status: 200,
    description: '返回所有司机列表',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 3 },
          username: { type: 'string', example: 'driver.lee' },
          full_name: { type: 'string', example: '李司机' },
          level: { type: 'string', example: 'driver' },
          status: { type: 'string', example: 'active' },
          created_at: { type: 'string', format: 'date-time' },
          last_login: { type: 'string', format: 'date-time' }
        }
      }
    }
  })
  @Get('drivers')
  async getAllDrivers() {
    return this.shippingService.getAllDrivers();
  }

  @ApiOperation({ summary: '确认订单送达' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（如"D00001"）' })
  @ApiQuery({ name: 'driverId', description: '司机ID' })
  @ApiQuery({ name: 'notes', description: '送达备注', required: false })
  @ApiResponse({
    status: 200,
    description: '订单已确认送达',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '订单 D00001 已被司机确认送达' },
        document: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            delivered: { type: 'boolean', example: true },
            delivered_at: { type: 'string', format: 'date-time' },
            delivered_by: { type: 'number', example: 3 }
          }
        }
      }
    }
  })
  @Post('confirm-delivery')
  async confirmDelivery(
    @Query('documentNo') documentNo: string,
    @Query('driverId') driverId: number,
    @Query('notes') notes?: string
  ) {
    return this.shippingService.confirmDelivery(documentNo, driverId, notes || '');
  }

  @ApiOperation({ summary: '司机拒绝分配的订单' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（如"D00001"）' })
  @ApiQuery({ name: 'driverId', description: '司机ID' })
  @ApiQuery({ name: 'reason', description: '拒绝原因', required: false })
  @ApiResponse({
    status: 200,
    description: '订单已被拒绝并恢复为未出货状态',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Document D00001 has been rejected by driver and reset to unshipped state' },
        document: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            is_shipped: { type: 'boolean', example: false },
            rejected_by: { type: 'number', example: 3 },
            rejection_reason: { type: 'string', example: '车辆维修无法送达' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '无权拒绝此订单（非指定司机）'
  })
  @ApiResponse({
    status: 404,
    description: '订单不存在或司机不存在'
  })
  @Post('reject-delivery')
  async rejectDelivery(
    @Query('documentNo') documentNo: string,
    @Query('driverId') driverId: number,
    @Query('reason') reason?: string
  ) {
    return this.shippingService.rejectDelivery(documentNo, driverId, reason || '');
  }

  @ApiOperation({ summary: '主管拒绝待出货订单' })
  @ApiQuery({ name: 'documentNo', description: '单据编号（如"D00001"）' })
  @ApiQuery({ name: 'supervisorId', description: '主管ID' })
  @ApiQuery({ name: 'reason', description: '拒绝原因', required: false })
  @ApiResponse({
    status: 200,
    description: '订单已被拒绝，所有检查状态已重置',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Document D00001 has been rejected by supervisor. All checks have been reset.' },
        document: {
          type: 'object',
          properties: {
            document_no: { type: 'string', example: 'D00001' },
            rejected_by: { type: 'number', example: 2 },
            rejection_reason: { type: 'string', example: '需要重新检查' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '无权拒绝此订单（非主管）'
  })
  @ApiResponse({
    status: 404,
    description: '订单不存在或主管不存在'
  })
  @Post('reject-shipment')
  async rejectShipment(
    @Query('documentNo') documentNo: string,
    @Query('supervisorId') supervisorId: number,
    @Query('reason') reason?: string
  ) {
    return this.shippingService.rejectShipment(documentNo, supervisorId, reason || '');
  }
}