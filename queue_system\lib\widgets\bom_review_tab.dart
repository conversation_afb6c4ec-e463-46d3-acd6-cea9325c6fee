// BomReviewTab 已注释，使用Queue页面替代
// 保留代码以备将来使用

/*
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';
import 'package:queue_system/utils/search_utils.dart';
import 'package:queue_system/widgets/info_chip.dart';

class BomReviewTab extends StatefulWidget {
  final List<CheckItem> items;
  final int staffId;
  final bool isSenior;

  // 确认操作回调函数
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;

  const BomReviewTab({
    super.key,
    required this.items,
    required this.staffId,
    required this.isSenior,
    required this.onConfirmAction,
  });

  @override
  State<BomReviewTab> createState() => _BomReviewTabState();
}

class _BomReviewTabState extends State<BomReviewTab> {
  // 用于保持滚动位置
  final ScrollController _scrollController = ScrollController();

  // 用于保存当前展开的文档组
  final Set<String> _expandedGroups = <String>{};

  // 搜索相关状态
  String _searchQuery = '';
  List<CheckItem> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _updateFilteredItems();
  }

  @override
  void didUpdateWidget(BomReviewTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      _updateFilteredItems();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 更新过滤后的项目列表
  void _updateFilteredItems() {
    setState(() {
      _filteredItems = SearchUtils.searchCheckItems(widget.items, _searchQuery);
    });
  }

  // 处理搜索查询变化
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _updateFilteredItems();
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    setState(() {
      _searchQuery = '';
      _updateFilteredItems();
    });
  }

  // 刷新数据方法
  Future<void> _refreshData() async {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    await checkListProvider.loadCheckList(
      widget.staffId,
      currentStaff: authProvider.currentStaff,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 首先应用搜索过滤
    final searchFilteredItems =
        _filteredItems.isNotEmpty || _searchQuery.isNotEmpty
            ? _filteredItems
            : widget.items;

    // 然后应用BOM过滤条件：只显示正常流程的BOM子项，与后端SQL查询条件完全匹配：
    // 只包含已被staff检查、有货架位置、有库存的BOM子项（排除特殊条件的BOM子项）
    final filteredItems = searchFilteredItems.where((item) {
      // 必须是BOM子项（有parentCode）且未被BOM专员检查
      bool isBomChild = item.parentCode != null &&
          item.parentCode!.isNotEmpty &&
          !item.isBomParent;
      bool notCheckedByBomSpecialist = item.bomSpecialistChecked != true;

      // 必须已被普通员工检查，且有货架位置和库存（正常流程）
      bool isCheckedByStaff = item.staffChecked == true;
      bool hasValidBinShelf = item.binShelfNo.trim() != '';
      bool hasStock = item.haveStock == true;

      // 返回最终判断结果，与后端SQL查询条件完全匹配
      return isBomChild &&
          notCheckedByBomSpecialist &&
          isCheckedByStaff &&
          hasValidBinShelf &&
          hasStock;
    }).toList();

    if (widget.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.inventory_2_outlined,
                size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(context.t('no_bom_items')),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshData,
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 搜索栏
        SearchBarWidget(
          hintText: context.t('search_documents'),
          onSearchChanged: _onSearchChanged,
          onClear: _onSearchCleared,
          initialValue: _searchQuery,
        ),
        // 显示搜索结果统计
        if (_searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '${context.t('search_results')}: ${filteredItems.length} / ${widget.items.length}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        // 项目列表
        Expanded(child: _buildItemsList(filteredItems)),
      ],
    );
  }

  Widget _buildItemsList(List<CheckItem> filteredItems) {
    // 如果搜索后没有结果，显示无结果提示
    if (_searchQuery.isNotEmpty && filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              context.t('no_search_results'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              '${context.t('search_keyword')}: "$_searchQuery"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.inventory_2_outlined,
                size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(context.t('no_bom_items')),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshData,
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    // 首先按优先级降序排序，然后按创建时间升序排序（紧急订单优先，然后越早的排越前面）
    final sortedItems = List<CheckItem>.from(filteredItems)
      ..sort((a, b) {
        // 先按优先级降序排序（数值越大优先级越高）
        final priorityComparison = b.priority.compareTo(a.priority);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        // 优先级相同时，按创建时间升序排序
        return a.createdAt.compareTo(b.createdAt);
      });

    // 按document_no和customer分组，而不是按created_at分组
    final documentCustomerGroups = <String, List<CheckItem>>{};
    for (final item in sortedItems) {
      // 提取主文档编号（不包括行号）
      String mainDocumentNo = item.documentNo;
      if (item.documentNo.contains('/')) {
        mainDocumentNo = item.documentNo.split('/').first;
      }

      // 使用document_no和customer作为分组键
      final groupKey = '${mainDocumentNo}_${item.customer}';

      if (!documentCustomerGroups.containsKey(groupKey)) {
        documentCustomerGroups[groupKey] = [];
      }
      documentCustomerGroups[groupKey]!.add(item);
    }

    // 将分组转换为列表并按优先级和创建时间排序（紧急订单优先，然后较早的在前）
    final sortedGroups = documentCustomerGroups.entries.toList()
      ..sort((a, b) {
        // 使用每组中第一个项目的优先级和创建时间进行排序
        final aPriority = a.value.first.priority;
        final bPriority = b.value.first.priority;
        final aTime = a.value.first.createdAt;
        final bTime = b.value.first.createdAt;

        // 先按优先级降序排序
        final priorityComparison = bPriority.compareTo(aPriority);
        if (priorityComparison != 0) {
          return priorityComparison;
        }
        // 优先级相同时，按创建时间升序排序
        return aTime.compareTo(bTime);
      });

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: sortedGroups.length,
        itemBuilder: (context, index) {
          final groupEntry = sortedGroups[index];
          final groupItems = groupEntry.value;

          // 获取该组的第一个项目的创建时间，用于显示
          final createdAt =
              _formatDate(groupItems.first.createdAt, format: 'yyyy-MM-dd HH:mm');

          // 直接构建文档组，不再需要中间的客户分组
          return _buildDocumentGroup(context, groupItems, createdAt);
        },
      ),
    );
  }

  // 构建文档组
  Widget _buildDocumentGroup(
    BuildContext context,
    List<CheckItem> items,
    String createdAtStr,
  ) {
    // 获取文档信息
    final firstItem = items.first;
    final documentNo = firstItem.documentNo.contains('/')
        ? firstItem.documentNo.split('/').first
        : firstItem.documentNo;
    final documentDate = firstItem.documentDate;
    final customerCode = firstItem.customer;
    final customerName = firstItem.customerName;

    // 创建唯一的组键
    final groupKey = '${documentNo}_$customerCode';

    // 检查是否为紧急订单
    final isUrgent = items.any((item) => item.priority > 0);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isUrgent ? Colors.red.shade50 : null,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                border: Border(left: BorderSide(color: Colors.red, width: 4)),
                borderRadius: BorderRadius.circular(4),
              )
            : null,
        child: ExpansionTile(
          key: ValueKey(groupKey),
          title: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$customerCode - $customerName',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${context.t('document_no')}: $documentNo',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100, // 与其他标签页保持一致，使用蓝色
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Created: $createdAtStr',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade800, // 与其他标签页保持一致，使用蓝色
                  ),
                ),
              ),
            ],
          ),
          subtitle: Text(
              'Document Date: ${_formatDate(documentDate, format: 'dd/MM/yyyy')} | ${items.length} ${items.length == 1 ? 'item' : context.t('items')}'),
          // 使用保存的展开状态
          initiallyExpanded: _expandedGroups.contains(groupKey),
          onExpansionChanged: (isExpanded) {
            setState(() {
              if (isExpanded) {
                _expandedGroups.add(groupKey);
              } else {
                _expandedGroups.remove(groupKey);
              }
            });
          },
          children: items.map((item) {
            // 使用自定义卡片，而不是直接使用CheckItemCard
            return _buildCustomItemCard(context, item);
          }).toList(),
        ),
      ),
    );
  }

  // 构建自定义项目卡片，与其他标签页保持一致的样式
  Widget _buildCustomItemCard(BuildContext context, CheckItem item) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Text(
                              '${context.t('line_no')}: ${item.documentNo}/${item.line} - ${item.stock}',
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        item.description,
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    // 使用操作按钮
                    _buildActionButton(context, item),
                  ],
                ),
              ],
            ),
            const Divider(),
            // 货物详情
            Wrap(
              spacing: 16,
              runSpacing: 4,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Wrap(
                      spacing: 16,
                      children: [
                        // QTY
                        InfoChip(
                          icon: Icons.shopping_cart,
                          label: context.t('quantity_short'),
                          value: '${item.quantity} ${item.uom}',
                          color: Colors.blue,
                        ),
                        // Shelf
                        InfoChip(
                          icon: Icons.location_on,
                          label: context.t('shelf_location'),
                          value: item.binShelfNo,
                          color: Colors.green,
                          isEmpty: item.binShelfNo.isEmpty, // 检查bin是否为空
                        ),
                        // BOM (如果有)
                        if (item.parentCode != null &&
                            item.parentCode!.isNotEmpty)
                          InfoChip(
                            icon: Icons.account_tree,
                            label: context.t('parent_code'),
                            value: item.parentCode!,
                            color: Colors.orange,
                          ),
                      ],
                    ),
                    // 如果没有库存，显示No Stock标签
                    if (!item.haveStock)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'No Stock',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
                if (item.staffChecked)
                  InfoChip(
                    icon: Icons.check_circle,
                    label: context.t('checked_status'),
                    value: _formatDateTime(context, item.staffCheckedAt),
                    color: Colors.green,
                  ),
                if (item.remark != null && item.remark!.isNotEmpty)
                  InfoChip(
                    icon: Icons.note,
                    label: context.t('note'),
                    value: item.remark!,
                    color: Colors.blue,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 格式化日期时间为指定格式
  String _formatDateTime(BuildContext context, DateTime? dateTime,
      {String format = 'MM-dd HH:mm'}) {
    if (dateTime == null) return context.t('unknown_time');
    return DateFormat(format).format(_toMalaysiaTime(dateTime));
  }

  // 辅助方法：将UTC时间转换为马来西亚时区（UTC+8）
  DateTime _toMalaysiaTime(DateTime? utcTime) {
    if (utcTime == null) return DateTime.now();
    // 转换为马来西亚时区（UTC+8）
    return utcTime.add(const Duration(hours: 8));
  }

  // 构建操作按钮
  Widget _buildActionButton(BuildContext context, CheckItem item) {
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // 检查是否为BOM专员
    final isBomSpecialist = authProvider.currentStaff?.level == 'staff_bom';

    // BOM专员模式
    if (isBomSpecialist) {
      return Row(
        children: [
          // Reject按钮
          ElevatedButton.icon(
            onPressed: () {
              _showSimpleRejectDialog(context, item, checkListProvider);
            },
            icon: const Icon(Icons.cancel),
            label: Text(context.t('reject')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8), // 添加间距
          // 确认按钮
          ElevatedButton.icon(
            onPressed: () {
              _handleBomConfirm(context, item, checkListProvider);
            },
            icon: const Icon(Icons.biotech),
            label: Text(context.t('bom_check')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      );
    }

    // 如果不是BOM专员，显示状态文本
    if (item.staffChecked) {
      return Text(
        context.t('already_checked'),
        style: const TextStyle(
          color: Colors.green,
          fontWeight: FontWeight.bold,
        ),
      );
    } else {
      return Text(
        context.t('not_checked'),
        style: const TextStyle(
          color: Colors.orange,
          fontWeight: FontWeight.bold,
        ),
      );
    }
  }

  // 处理 BOM 确认操作，保持当前视图位置
  void _handleBomConfirm(BuildContext context, CheckItem item,
      CheckListProvider checkListProvider) {
    // 保存当前滚动位置和操作项目信息
    final currentScrollOffset =
        _scrollController.hasClients ? _scrollController.offset : 0.0;
    final itemKey = '${item.documentNo}_${item.line}';

    widget.onConfirmAction(
      context,
      context.t('confirm_bom_check'),
      context.t('confirm_bom_check_message'),
      () async {
        await checkListProvider.bomCheck(
            item.documentNo, item.line, widget.staffId);

        // 使用更强大的滚动位置恢复机制
        _restoreScrollPositionWithRetry(currentScrollOffset, itemKey);
      },
    );
  }

  // 显示简化的拒绝对话框，与 driver 页面保持一致
  Future<void> _showSimpleRejectDialog(BuildContext context, CheckItem item,
      CheckListProvider checkListProvider) async {
    final TextEditingController reasonController = TextEditingController();

    // 预先获取本地化文本，避免异步使用 context
    final noReasonProvided = context.t('no_reason_provided');

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('reject_staff_check')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${item.documentNo}'),
            Text('${context.t('line')}: ${item.line}'),
            const SizedBox(height: 16),
            Text(context.t('rejection_reason')),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                border: const OutlineInputBorder(),
                hintText: context.t('enter_reject_reason'),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              context.t('confirm'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final reason = reasonController.text.isEmpty
          ? noReasonProvided
          : reasonController.text;

      // 直接调用拒绝操作，避免跨异步间隙使用 context
      _performBomReject(item, checkListProvider, reason);
    }
  }

  // 执行 BOM 拒绝操作，避免跨异步间隙使用 context
  void _performBomReject(CheckItem item, CheckListProvider checkListProvider,
      String rejectReason) {
    // 保存当前滚动位置和操作项目信息
    final currentScrollOffset =
        _scrollController.hasClients ? _scrollController.offset : 0.0;
    final itemKey = '${item.documentNo}_${item.line}';

    // 直接执行拒绝操作，不需要确认对话框
    checkListProvider
        .rejectStaffCheck(
      item.documentNo,
      item.line,
      widget.staffId,
      rejectReason,
    )
        .then((_) {
      // 使用更强大的滚动位置恢复机制
      _restoreScrollPositionWithRetry(currentScrollOffset, itemKey);
    }).catchError((error) {
      // 处理错误，但仍然尝试恢复滚动位置
      _restoreScrollPositionWithRetry(currentScrollOffset, itemKey);
    });
  }

  // 带重试机制的滚动位置恢复
  void _restoreScrollPositionWithRetry(double targetOffset, String itemKey) {
    _attemptScrollRestore(targetOffset, itemKey, 0);
  }

  // 尝试恢复滚动位置（带重试机制）
  void _attemptScrollRestore(
      double targetOffset, String itemKey, int attemptCount) {
    const maxAttempts = 5;

    if (attemptCount >= maxAttempts || !mounted) {
      return;
    }

    final delay = 100 + (attemptCount * 100);

    Future.delayed(Duration(milliseconds: delay), () {
      if (!mounted || !_scrollController.hasClients) {
        _attemptScrollRestore(targetOffset, itemKey, attemptCount + 1);
        return;
      }

      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final clampedOffset = targetOffset.clamp(0.0, maxScrollExtent);

      _scrollController
          .animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      )
          .then((_) {
        // 滚动恢复成功
      }).catchError((error) {
        // 如果动画失败，尝试直接跳转
        if (mounted && _scrollController.hasClients) {
          try {
            _scrollController.jumpTo(clampedOffset);
          } catch (e) {
            // 静默处理跳转失败
          }
        }
      });
    });
  }

  /*
  // 【已废弃】处理 BOM 拒绝操作，保持当前视图位置 - 旧的双选项版本
  // 保留此代码以备后续需要恢复双选项功能
  void _handleBomReject(
      BuildContext context,
      CheckItem item,
      CheckListProvider checkListProvider,
      bool useCustomReason,
      String rejectReason) {
    // 保存当前滚动位置
    final currentScrollOffset =
        _scrollController.hasClients ? _scrollController.offset : 0.0;

    widget.onConfirmAction(
      context,
      context.t('confirm_reject'),
      context.t('confirm_reject_message'),
      () async {
        // 如果是修改/取消选项，使用特殊处理
        if (!useCustomReason) {
          await checkListProvider.rejectStaffCheck(
            item.documentNo,
            item.line,
            widget.staffId,
            context.t('modify_cancel'),
          );
        } else {
          await checkListProvider.rejectStaffCheck(
            item.documentNo,
            item.line,
            widget.staffId,
            rejectReason,
          );
        }

        // 在下一帧恢复滚动位置
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients && mounted) {
            _scrollController.animateTo(
              currentScrollOffset,
              duration: const Duration(milliseconds: 100),
              curve: Curves.easeInOut,
            );
          }
        });
      },
    );
  }
  */

  // 格式化日期为指定格式
  String _formatDate(DateTime dateTime, {String format = 'yyyy-MM-dd'}) {
    if (format == 'yyyy-MM-dd') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    } else if (format == 'yyyy-MM-dd HH:mm') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (format == 'yyyy-MM-dd HH:mm:ss') {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
    } else if (format == 'dd/MM/yyyy') {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } else if (format == 'HH:mm dd/MM/yyyy') {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')} ${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } else {
      return dateTime.toString();
    }
  }
}
*/
