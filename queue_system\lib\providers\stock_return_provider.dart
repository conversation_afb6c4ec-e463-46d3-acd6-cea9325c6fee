import 'package:flutter/foundation.dart';
import 'package:queue_system/models/stock_return_model.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/services/check_service.dart';

class StockReturnProvider with ChangeNotifier {
  final CheckService _checkService;
  final AuthProvider _authProvider;

  List<StockReturnModel> _pendingReturns = [];
  bool _isLoading = false;
  String? _error;

  StockReturnProvider({
    required CheckService checkService,
    required AuthProvider authProvider,
  })  : _checkService = checkService,
        _authProvider = authProvider;

  List<StockReturnModel> get pendingReturns => _pendingReturns;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get pendingCount => _pendingReturns.length;

  // 加载待退回库存列表
  Future<void> loadPendingReturns() async {
    if (_authProvider.currentStaff == null) {
      _error = 'User not logged in';
      notifyListeners();
      return;
    }

    // 只有普通员工可以查看待退回库存
    if (_authProvider.currentStaff!.level != 'regular') {
      _pendingReturns = [];
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _pendingReturns = await _checkService.getPendingReturnStocks(
        _authProvider.currentStaff!.id,
      );
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // 确认库存已退回
  Future<bool> confirmStockReturn(int detailId) async {
    if (_authProvider.currentStaff == null) {
      _error = 'User not logged in';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _checkService.confirmStockReturn(
        detailId,
        _authProvider.currentStaff!.id,
      );

      // 从列表中移除已确认的项目
      _pendingReturns.removeWhere((item) => item.id == detailId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
