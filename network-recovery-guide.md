# 🔄 网络中断恢复机制使用指南

## 功能概述

当网络中断导致PDF生成时Firebase上传失败后，可以使用以下API端点手动重新上传PDF文件。

## 🔍 API端点

### 1. 检查上传状态

**GET** `/firebase-test/check-upload-status`

检查PDF文件的上传状态，识别哪些文件需要重新上传。

**参数：**
- `documentNo` (可选) - 指定订单编号，不提供则检查最近50个订单

**响应示例：**
```json
{
  "success": true,
  "summary": {
    "total": 10,
    "uploaded": 7,
    "needsReupload": 2,
    "localOnly": 2,
    "noPdf": 1
  },
  "results": [
    {
      "documentNo": "DO396400",
      "localPdfExists": true,
      "localPdfPath": "D:\\Project\\QueueSystem\\backend_nestjs\\PDF_Output\\2025-06-19\\DO396400.pdf",
      "firebaseUploaded": false,
      "firebaseUrl": null,
      "uploadedAt": null,
      "needsReupload": true,
      "status": "NEEDS_REUPLOAD"
    }
  ]
}
```

**状态说明：**
- `UPLOADED` - 已成功上传到Firebase
- `NEEDS_REUPLOAD` - 本地存在PDF但Firebase未上传，需要重新上传
- `LOCAL_ONLY` - 仅本地存在PDF
- `NO_PDF` - 本地和Firebase都没有PDF

### 2. 重新上传单个PDF

**POST** `/firebase-test/retry-upload?documentNo=DO396400`

重新上传指定订单的PDF文件到Firebase Storage。

**参数：**
- `documentNo` (必需) - 订单编号
- `force` (可选) - 设置为 `true` 强制重新上传已存在的文件

**响应示例：**
```json
{
  "success": true,
  "documentNo": "DO396400",
  "localPath": "D:\\Project\\QueueSystem\\backend_nestjs\\PDF_Output\\2025-06-19\\DO396400.pdf",
  "firebase": {
    "success": true,
    "downloadUrl": "https://storage.googleapis.com/bucket-name/pdfs/2025-06-19/DO396400.pdf",
    "firebasePath": "pdfs/2025-06-19/DO396400.pdf"
  },
  "message": "PDF重新上传成功: DO396400",
  "isRetry": true,
  "forced": false
}
```

### 3. 批量重新上传

**POST** `/firebase-test/batch-retry-upload`

批量重新上传失败的PDF文件。

**参数：**
- `limit` (可选) - 最大处理数量，默认10个

**响应示例：**
```json
{
  "success": true,
  "summary": {
    "totalChecked": 15,
    "totalProcessed": 5,
    "successful": 4,
    "failed": 1,
    "skipped": 10
  },
  "results": [
    {
      "documentNo": "DO396400",
      "success": true,
      "localPath": "D:\\...\\DO396400.pdf",
      "firebase": { ... }
    }
  ],
  "message": "批量重新上传完成，处理了5个PDF文件"
}
```

## 🛠️ 使用场景

### 场景1：网络中断后恢复

1. **检查状态**：
   ```
   GET /firebase-test/check-upload-status
   ```

2. **重新上传失败的文件**：
   ```
   POST /firebase-test/retry-upload?documentNo=DO396400
   ```

### 场景2：批量处理多个失败上传

1. **批量重新上传**：
   ```
   POST /firebase-test/batch-retry-upload?limit=20
   ```

### 场景3：强制重新上传已存在的文件

1. **强制重新上传**：
   ```
   POST /firebase-test/retry-upload?documentNo=DO396400&force=true
   ```

## 📁 文件查找逻辑

系统会在以下位置查找本地PDF文件：

1. **按日期文件夹查找**（最近7天）：
   - `PDF_Output/2025-06-19/DO396400.pdf`
   - `PDF_Output/2025-06-18/DO396400.pdf`
   - ...

2. **根目录查找**：
   - `PDF_Output/DO396400.pdf`

## ⚠️ 注意事项

1. **安全性**：重新上传不会覆盖已存在的Firebase文件，除非使用 `force=true`
2. **性能**：批量上传会在每个文件之间添加100ms延迟，避免过快请求
3. **限制**：批量上传默认最多处理10个文件，可通过 `limit` 参数调整
4. **日志**：所有上传操作都会记录详细日志，便于问题排查

## 🔧 故障排除

### 问题1：找不到本地PDF文件
- **原因**：PDF文件可能在不同的日期文件夹中
- **解决**：检查 `searchedPaths` 字段，确认文件位置

### 问题2：上传失败
- **原因**：网络问题或Firebase配置问题
- **解决**：检查网络连接和Firebase配置，查看详细错误信息

### 问题3：已存在文件无法重新上传
- **原因**：默认不覆盖已存在的文件
- **解决**：使用 `force=true` 参数强制重新上传

## 📊 监控建议

定期运行状态检查，监控上传失败的文件：

```bash
# 每日检查
curl "http://localhost:3000/firebase-test/check-upload-status"

# 批量处理失败的上传
curl -X POST "http://localhost:3000/firebase-test/batch-retry-upload?limit=50"
```
