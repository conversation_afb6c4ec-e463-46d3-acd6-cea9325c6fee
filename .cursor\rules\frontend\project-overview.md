---
title: "Flutter 移动应用概述"
description: "队列系统 Flutter 移动应用的总体介绍"
version: "1.0.0"
last_updated: "2025-05-19"
---

# Flutter 移动应用概述

## 项目简介

这是一个基于 Flutter 框架构建的移动应用，作为队列系统的前端界面，主要用于仓库员工检查、主管确认、BOM 专员审核和司机送货等操作。应用通过 REST API 与 NestJS 后端进行通信，支持多语言界面（中文、英文和马来语）。

## 项目结构

- `lib/` - 源代码目录
  - `main.dart` - 应用程序入口点
  - `models/` - 数据模型定义
  - `providers/` - 状态管理和业务逻辑
  - `screens/` - 用户界面屏幕
  - `services/` - API 服务和网络请求
  - `utils/` - 工具类和辅助函数
  - `widgets/` - 可复用 UI 组件
  - `l10n/` - 本地化和翻译

## 主要功能

### 1. 用户认证
- 员工登录界面
- 权限控制（普通员工、BOM专员、主管、司机）
- 会话管理和自动登录

### 2. 员工操作
- 查看待检查货物列表
- 扫描货物条码进行检查
- 查看检查统计和进度

### 3. BOM 专员操作
- 查看待检查 BOM 物品列表
- 执行 BOM 物品二次检查
- 查看 BOM 检查统计

### 4. 主管操作
- 确认员工和 BOM 专员的检查结果
- 拒绝检查并提供原因
- 标记订单为已出货
- 指派司机送货

### 5. 司机操作
- 查看待送达订单列表
- 确认订单送达
- 获取客户签名
- 生成和发送 PDF 订单

### 6. PDF 和签名功能
- 在应用内查看 PDF 订单
- 获取客户签名
- 将签名添加到 PDF
- 通过邮件发送 PDF

### 7. 通知系统
- 接收检查被拒绝通知
- 接收新订单分配通知
- 查看通知历史

## 技术栈

- **Flutter**: UI 框架
- **Provider**: 状态管理
- **Dio**: 网络请求
- **flutter_pdfview**: PDF 查看
- **signature**: 签名功能
- **shared_preferences**: 本地存储
- **flutter_barcode_scanner**: 条码扫描

## 用户界面规范

- 使用 Material Design 风格
- 主题颜色：主色调 #2196F3，强调色 #FF9800
- 支持浅色和深色模式
- 响应式布局，适配不同屏幕尺寸
- 标准化的错误提示和加载状态

## 性能优化

- 使用缓存减少网络请求
- 懒加载和分页加载大型列表
- 图片和资源优化
- 最小化应用启动时间

## 安全性

- 使用 HTTPS 进行 API 通信
- 存储敏感信息时使用加密
- 实现会话超时和自动登出
- 防止未授权访问受保护功能
