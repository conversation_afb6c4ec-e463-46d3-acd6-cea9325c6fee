import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SqlServerMain } from '../sql-server/entities/main.entity';
import { SqlServerDetail } from '../sql-server/entities/detail.entity';
import { SqlServerScTranDetail } from '../sql-server/entities/sc-tran-detail.entity';
import { SqlServerStock } from '../stock/entities/stock.entity';
import { SqlServerCustomer } from '../sql-server/entities/customer.entity';
import { SqlServerCnMain } from '../sql-server/entities/cn-main.entity';
import { SqlServerCnDetail } from '../sql-server/entities/cn-detail.entity';
import { Main } from '../postgres/entities/main.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { CnMain } from '../postgres/entities/cn-main.entity';
import { CnDetail } from '../postgres/entities/cn-detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { SyncService } from './sync.service';
import { SyncController } from './sync.controller';
import { NotificationModule } from '../notification/notification.module';
import { StockModule } from '../stock/stock.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SqlServerMain,
      SqlServerDetail,
      SqlServerScTranDetail,
      SqlServerStock,
      SqlServerCustomer,
      SqlServerCnMain,
      SqlServerCnDetail
    ], 'sqlServerConnection'),
    TypeOrmModule.forFeature([
      Main,
      Detail,
      CnMain,
      CnDetail,
      Staff
    ], 'postgresConnection'),
    NotificationModule,
    StockModule,
  ],
  controllers: [SyncController],
  providers: [SyncService],
  exports: [SyncService],
})
export class SyncModule { }