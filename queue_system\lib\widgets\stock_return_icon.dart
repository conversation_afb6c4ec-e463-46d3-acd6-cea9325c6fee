import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/stock_return_provider.dart';
import 'package:queue_system/providers/stock_return_tracking_provider.dart';
import 'package:queue_system/screens/stock_return_screen.dart';

class StockReturnIcon extends StatelessWidget {
  const StockReturnIcon({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer2<StockReturnProvider, StockReturnTrackingProvider>(
      builder: (context, stockReturnProvider, trackingProvider, child) {
        // 合并两个 Provider 的计数
        final cnReturnCount = stockReturnProvider.pendingCount;
        final trackingCount = trackingProvider.pendingCount;
        final totalPendingCount = cnReturnCount + trackingCount;

        return Stack(
          alignment: Alignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.assignment_return),
              tooltip: context.t('stock_return'),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StockReturnScreen(),
                  ),
                );
              },
            ),
            if (totalPendingCount > 0)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    totalPendingCount > 99 ? '99+' : '$totalPendingCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
