import { Entity, Column, PrimaryColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('AR_DO_Main_tbl')
export class SqlServerMain {
  @ApiProperty({ description: '文档编号', example: 'DO123456' })
  @PrimaryColumn({ name: 'Document No' })
  DocumentNo: string;

  @ApiProperty({ description: '文档日期', example: '2025-04-04' })
  @Column({ name: 'Document Date' })
  DocumentDate: Date;

  @ApiProperty({ description: '客户', example: 'CUST001' })
  @Column()
  Customer: string;

  @ApiProperty({ description: '客户名称', example: '测试客户' })
  @Column({ name: 'Customer Name', nullable: true })
  CustomerName: string;

  @ApiProperty({ description: '备注', example: '紧急订单' })
  @Column()
  Remarks: string;

  @ApiProperty({ description: '运输商代码', example: 'TRANS001' })
  @Column({ name: 'Transporter Code', nullable: true })
  TransporterCode: string;

  @ApiProperty({ description: '运输商名称', example: '某物流公司' })
  @Column({ name: 'Transporter Name', nullable: true })
  TransporterName: string;

  @ApiProperty({ description: '发行人', example: 'John Doe' })
  @Column({ name: 'Issue By', nullable: true })
  IssueBy: string;

  @ApiProperty({ description: 'PO号码', example: 'P1234' })
  @Column({ name: 'PO No', nullable: true })
  PONo: string;

  @OneToMany('SqlServerDetail', 'main')
  details: any[];
}