import { Injectable, NotFoundException, ForbiddenException, BadRequestException, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In, IsNull, Not } from 'typeorm';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { Main } from '../postgres/entities/main.entity';
import { NotificationService } from '../notification/notification.service';
import { PdfService } from '../pdf/pdf.service';
import { HtmlPdfService } from '../pdf/html-pdf.service';

@Injectable()
export class CheckService {
  private readonly logger = new Logger(CheckService.name);

  constructor(
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    @InjectRepository(Main, 'postgresConnection')
    private readonly mainRepository: Repository<Main>,
    private readonly notificationService: NotificationService,
    private readonly pdfService: PdfService,
    private readonly htmlPdfService: HtmlPdfService
  ) { }

  async getPendingChecks(staffId: number) {
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    // 如果是普通员工，返回三种不同类型的数据
    if (staff.level === 'regular') {
      // 从 staff.floor (例如 "2F") 中提取楼层数字
      const floor = staff.floor.replace('F', '');

      // 查询该楼层的待检查数据
      const [pendingRegularCheckDetails, pendingBomCheckDetails, pendingSupervisorCheckDetails] = await Promise.all([
        // 1. 待检查的普通库存项目 - 非BOM类型
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('detail.staff_checked = :staffChecked', { staffChecked: false })
          .andWhere('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .andWhere('detail.parent_code IS NULL')
          .andWhere('detail.is_bom_parent = :isNotBomParent', { isNotBomParent: false })
          // 过滤掉bin为空或者have_stock为false的记录
          .andWhere('detail.bin_shelf_no != :emptyBin', { emptyBin: '' })
          .andWhere('detail.have_stock = :haveStock', { haveStock: true })
          .getMany()
          .then(items =>
            // 过滤出该楼层的货物
            items.filter(item => {
              const itemFloor = item.bin_shelf_no.split('-')[0];
              return itemFloor === floor;
            })
          ),

        // 2. 待检查的BOM子项（普通员工只需检查有货架位置的BOM子项）
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('detail.staff_checked = :staffChecked', { staffChecked: false })
          .andWhere('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .andWhere('detail.parent_code IS NOT NULL')
          .andWhere('detail.is_bom_parent = :isNotBomParent', { isNotBomParent: false })
          // 过滤掉bin为空或者have_stock为false的记录
          // 注意：没有货架位置的BOM子项由BOM专员直接处理，不需要普通员工检查
          .andWhere('detail.bin_shelf_no != :emptyBin', { emptyBin: '' })
          .andWhere('detail.have_stock = :haveStock', { haveStock: true })
          .getMany()
          .then(items =>
            // 过滤出该楼层的货物
            items.filter(item => {
              const itemFloor = item.bin_shelf_no.split('-')[0];
              return itemFloor === floor;
            })
          ),

        // 3. 待主管确认的货物 - 保持不变
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('detail.staff_checked = :staffChecked', { staffChecked: true })
          .andWhere('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .getMany()
          .then(items =>
            // 过滤出该楼层的货物
            items.filter(item => {
              const itemFloor = item.bin_shelf_no.split('-')[0];
              return itemFloor === floor;
            })
          )
      ]);

      // 转换结果结构，添加所需字段
      const pendingRegularCheck = pendingRegularCheckDetails.map(detail => {
        return {
          ...detail,
          document_date: detail.main.document_date,
          customer: detail.main.customer,
          customer_name: detail.main.customer_name,
          created_at: detail.created_at,
          priority: detail.main.priority || 0
        };
      });

      const pendingBomCheck = pendingBomCheckDetails.map(detail => {
        return {
          ...detail,
          document_date: detail.main.document_date,
          customer: detail.main.customer,
          customer_name: detail.main.customer_name,
          created_at: detail.created_at,
          priority: detail.main.priority || 0
        };
      });

      const pendingSupervisorCheck = pendingSupervisorCheckDetails.map(detail => {
        return {
          ...detail,
          document_date: detail.main.document_date,
          customer: detail.main.customer,
          customer_name: detail.main.customer_name,
          created_at: detail.created_at,
          priority: detail.main.priority || 0
        };
      });

      return {
        pendingRegularCheck,  // 新名称：普通库存项目
        pendingBomCheck,      // 新增：BOM子项
        pendingSupervisorCheck,
        total: pendingRegularCheck.length + pendingBomCheck.length + pendingSupervisorCheck.length
      };
    } else if (staff.level === 'staff_bom') {
      this.logger.log(`Processing data for BOM specialist with ID: ${staffId}`);

      // BOM专员需要四种不同的列表
      // 首先查询所有包含BOM项的送货单ID（至少有一个货品是BOM类型）
      const bomMainIds = await this.detailRepository
        .createQueryBuilder('detail')
        .select('DISTINCT detail.main_id')
        .where('(detail.is_bom_parent = :isBomParent OR detail.bom_parent_id IS NOT NULL OR detail.parent_code IS NOT NULL)')
        .setParameter('isBomParent', true)
        .getRawMany()
        .then(results => results.map(item => item.main_id));

      // 如果没有找到包含BOM项的送货单，记录警告
      if (bomMainIds.length === 0) {
        this.logger.warn('No delivery orders containing BOM items were found for BOM specialist view');
      }

      this.logger.log(`Found ${bomMainIds.length} delivery orders containing BOM items: [${bomMainIds.join(', ')}]`);

      const [waitingListDetails, pendingStaffCheckDetails, pendingBomCheckDetails, pendingSupervisorCheckDetails] = await Promise.all([
        // 1. waitingList: 对于BOM专员，显示BOM父项和非BOM项目，排除BOM子项
        // 按优先级降序，然后按创建时间升序排序，限制数量避免性能问题
        bomMainIds.length > 0
          ? this.detailRepository.createQueryBuilder('detail')
            .leftJoinAndSelect('detail.main', 'main')
            .where('main.is_shipped = :isShipped', { isShipped: false })
            .andWhere('detail.main_id IN (:...bomMainIds)', { bomMainIds })
            // 包含BOM父项和非BOM项目，排除BOM子项
            .andWhere('(detail.is_bom_parent = true OR (detail.parent_code IS NULL OR detail.parent_code = \'\'))')
            .orderBy('main.priority', 'DESC')
            .addOrderBy('main.created_at', 'ASC')
            // .take(100) // 限制最多返回100条记录 - 暂时注释掉
            .getMany()
          : Promise.resolve([]), // 如果没有包含BOM项的送货单，返回空数组

        // 2. pendingStaffCheck: 只显示包含BOM项的送货单中待检查的普通库存项目（保留原有字段，用于向后兼容）
        bomMainIds.length > 0
          ? this.detailRepository.createQueryBuilder('detail')
            .leftJoinAndSelect('detail.main', 'main')
            .where('detail.staff_checked = :staffChecked', { staffChecked: false })
            .andWhere('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
            .andWhere('detail.parent_code IS NULL')
            .andWhere('detail.is_bom_parent = :isNotBomParent', { isNotBomParent: false })
            // 过滤掉bin为空或者have_stock为false的记录
            .andWhere('detail.bin_shelf_no != :emptyBin', { emptyBin: '' })
            .andWhere('detail.have_stock = :haveStock', { haveStock: true })
            // 只包含有BOM项的送货单
            .andWhere('detail.main_id IN (:...bomMainIds)', { bomMainIds })
            .orderBy('main.priority', 'DESC')
            .addOrderBy('main.created_at', 'ASC')
            .getMany()
          : Promise.resolve([]), // 如果没有包含BOM项的送货单，返回空数组

        // 3. pendingBomCheck: BOM专员待检查的BOM物品，包括两种情况：
        // a. 普通员工已检查但BOM专员未检查的BOM物品（有货架位置且有库存）
        // b. 跳过staff检查的BOM子项不应出现在BOM专员列表中，直接进入supervisor确认

        // 使用原始SQL查询，确保能够找到所有符合条件的记录
        // 包含正常流程的BOM子项和特殊条件的BOM子项（无货架位置或无库存）
        bomMainIds.length > 0
          ? this.detailRepository.query(`
              SELECT d.*, m.document_date, m.customer, m.customer_name, m.created_at
              FROM delivery_order_detail d
              JOIN delivery_order_main m ON d.main_id = m.id
              WHERE d.bom_specialist_checked = false
              AND d.supervisor_checked = false
              AND d.parent_code IS NOT NULL
              AND d.is_bom_parent = false
              AND d.main_id IN (${bomMainIds.join(',')})
              AND (
                (d.bin_shelf_no != '' AND d.have_stock = true AND d.staff_checked = true) OR
                (d.bin_shelf_no = '' OR d.have_stock = false)
              )
            `)
          : Promise.resolve([])
            .then(items => {
              // 添加调试日志，检查是否有bin_shelf_no为空的记录
              const emptyBinItems = items.filter((item: any) => item.bin_shelf_no === '');
              this.logger.log(`Found ${emptyBinItems.length} BOM items with empty bin_shelf_no in pendingBomCheck`);

              if (emptyBinItems.length > 0) {
                // 打印前5个空bin_shelf_no的记录
                const sampleItems = emptyBinItems.slice(0, 5);
                sampleItems.forEach((item: any, index: number) => {
                  this.logger.log(`Empty bin item #${index + 1} in pendingBomCheck: document_no=${item.document_no}, parent_code=${item.parent_code}, staff_checked=${item.staff_checked}, have_stock=${item.have_stock}`);
                });
              }

              // 检查是否有符合条件但bin_shelf_no不为空的记录
              const nonEmptyBinItems = items.filter((item: any) => item.bin_shelf_no !== '');
              this.logger.log(`Found ${nonEmptyBinItems.length} BOM items with non-empty bin_shelf_no in pendingBomCheck`);



              // 直接查询数据库，检查是否有bin_shelf_no为空的记录
              this.detailRepository.query(`
              SELECT * FROM delivery_order_detail
              WHERE bin_shelf_no = ''
              AND parent_code IS NOT NULL
              AND is_bom_parent = false
              AND have_stock = true
              AND bom_specialist_checked = false
              AND supervisor_checked = false
              LIMIT 5
            `).then((rawResults: any[]) => {
                this.logger.log(`Raw SQL query found ${rawResults.length} BOM items with empty bin_shelf_no`);

                if (rawResults.length > 0) {
                  // 打印前5个空bin_shelf_no的记录
                  rawResults.forEach((item, index) => {
                    this.logger.log(`Raw SQL empty bin item #${index + 1}: document_no=${item.document_no}, parent_code=${item.parent_code}, staff_checked=${item.staff_checked}, have_stock=${item.have_stock}`);
                  });
                }
              });

              return items;
            })
            .then(items => {
              // 添加调试日志，检查是否有bin_shelf_no为空的记录
              const emptyBinItems = items.filter((item: any) => item.bin_shelf_no === '');
              this.logger.log(`Found ${emptyBinItems.length} BOM items with empty bin_shelf_no`);

              if (emptyBinItems.length > 0) {
                // 打印前5个空bin_shelf_no的记录
                const sampleItems = emptyBinItems.slice(0, 5);
                sampleItems.forEach((item: any, index: number) => {
                  this.logger.log(`Empty bin item #${index + 1}: document_no=${item.document_no}, parent_code=${item.parent_code}, staff_checked=${item.staff_checked}`);
                });
              }

              return items;
            }),

        // 4. pendingSupervisorCheck: 只包含已被BOM专员检查但未被主管检查的货物
        // 特殊BOM子项（bin为空或have_stock=false）不在这里显示，直接由Supervisor在主页面处理
        bomMainIds.length > 0
          ? this.detailRepository.createQueryBuilder('detail')
            .leftJoinAndSelect('detail.main', 'main')
            .where('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
            .andWhere('detail.bom_specialist_checked = :bomChecked', { bomChecked: true })
            .setParameter('bomChecked', true)
            .setParameter('supervisorChecked', false)
            // 只包含有BOM项的送货单
            .andWhere('detail.main_id IN (:...bomMainIds)', { bomMainIds })
            .orderBy('main.priority', 'DESC')
            .addOrderBy('main.created_at', 'ASC')
            .getMany()
          : Promise.resolve([])
      ]);

      // 转换结果结构，添加所需字段
      // 分离BOM父项和非BOM项目
      const bomParentsOnly = waitingListDetails.filter(detail => detail.is_bom_parent);
      const nonBomItemsOnly = waitingListDetails.filter(detail => !detail.is_bom_parent);

      // 过滤掉所有子项都被BOM专员检查完毕的BOM父项
      // 使用新的 bom_children_completed 字段，简化逻辑
      const activeBomParents = bomParentsOnly.filter(parent => {
        const shouldKeep = !parent.bom_children_completed;
        this.logger.log(`BOM parent ${parent.document_no} (${parent.stock}): bom_children_completed=${parent.bom_children_completed}, shouldKeep=${shouldKeep}`);
        return shouldKeep;
      });

      // 为活跃的BOM父项添加子项状态信息（用于前端显示）
      const enhancedBomParents = await this.enhanceBomParentsWithChildStatus(activeBomParents);

      // 转换非BOM项目数据
      const transformedNonBomItems = nonBomItemsOnly.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.created_at,
        priority: detail.main.priority || 0
      }));

      // 合并数据
      const waitingList = [...enhancedBomParents, ...transformedNonBomItems];

      const pendingStaffCheck = pendingStaffCheckDetails.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.created_at,
        priority: detail.main.priority || 0
      }));

      const pendingBomCheck = pendingBomCheckDetails.map((detail: any) => ({
        ...detail,
        document_date: detail.document_date || (detail.main ? detail.main.document_date : null),
        customer: detail.customer || (detail.main ? detail.main.customer : null),
        customer_name: detail.customer_name || (detail.main ? detail.main.customer_name : null),
        created_at: detail.created_at,
        priority: (detail.main ? detail.main.priority : 0) || 0
      }));

      const pendingSupervisorCheck = pendingSupervisorCheckDetails.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.created_at,
        priority: detail.main.priority || 0
      }));

      this.logger.log(`BOM specialist data: waitingList=${waitingList.length}, pendingStaffCheck=${pendingStaffCheck.length}, pendingBomCheck=${pendingBomCheck.length}, pendingSupervisorCheck=${pendingSupervisorCheck.length}`);

      return {
        waitingList,
        pendingStaffCheck,
        pendingBomCheck,  // 新增字段，专门用于BOM专员
        pendingSupervisorCheck,
        total: waitingList.length + pendingStaffCheck.length + pendingBomCheck.length + pendingSupervisorCheck.length
      };
    } else {
      // 如果是主管，返回所有未被主管检查过的数据
      // 获取所有包含BOM项的Document的ID，以及至少有一个detail被staff检查过的Document的ID
      const [documentsWithStaffCheckedDetails, documentsWithBomItems] = await Promise.all([
        this.detailRepository
          .createQueryBuilder('detail')
          .select('DISTINCT detail.main_id')
          .where('detail.staff_checked = :staffChecked', { staffChecked: true })
          .getRawMany(),
        this.detailRepository
          .createQueryBuilder('detail')
          .select('DISTINCT detail.main_id')
          .where('(detail.is_bom_parent = :isBomParent OR detail.parent_code IS NOT NULL)')
          .setParameter('isBomParent', true)
          .getRawMany()
      ]);

      const mainIdsWithStaffChecked = documentsWithStaffCheckedDetails.map(item => item.main_id);
      const mainIdsWithBomItems = documentsWithBomItems.map(item => item.main_id);

      // 合并两个列表，确保包含所有相关的文档
      const allRelevantMainIds = [...new Set([...mainIdsWithStaffChecked, ...mainIdsWithBomItems])];



      // 如果没有任何相关Document，Supervisor仍然可以看到未出货的订单
      // 包括所有BOM记录（bin为空和have_stock=false的也要显示）
      if (allRelevantMainIds.length === 0) {
        // 获取所有未出货的订单，包括bin为空和have_stock=false的BOM记录
        const allUnshippedDetails = await this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('main.is_shipped = :isShipped', { isShipped: false })
          .andWhere('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .orderBy('main.priority', 'DESC')
          .addOrderBy('main.created_at', 'ASC')
          .take(200) // 限制最多返回200条记录
          .getMany();

        const allDetails = allUnshippedDetails.map(detail => ({
          ...detail,
          document_date: detail.main.document_date,
          customer: detail.main.customer,
          customer_name: detail.main.customer_name,
          created_at: detail.created_at,
          priority: detail.main.priority || 0
        }));

        return {
          waitingList: allDetails, // 整合版本：包含所有未被主管确认的订单（原waiting + to_confirm功能）
          pendingSupervisorCheck: [], // 清空，功能已整合到waitingList中
          total: allDetails.length
        };
      }

      const [waitingListDetails, _, __, allDetailsForDocuments] = await Promise.all([
        // 主管可以看到BOM父项和非BOM项目，排除BOM子项
        // 按优先级降序，然后按创建时间升序排序，限制数量避免性能问题
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('main.is_shipped = :isShipped', { isShipped: false })
          .andWhere('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          // 包含BOM父项和非BOM项目，排除BOM子项
          .andWhere('(detail.is_bom_parent = true OR (detail.parent_code IS NULL OR detail.parent_code = \'\'))')
          .orderBy('main.priority', 'DESC')
          .addOrderBy('main.created_at', 'ASC')
          // .take(100) // 限制最多返回100条记录 - 暂时注释掉
          .getMany(),
        // 主管可以看到所有已被员工检查但未被主管检查的货物，包括bin为空和have_stock=false的BOM子项
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .andWhere(qb => {
            // 五种情况：
            // 1. 普通物品（非BOM）已被员工检查
            qb.where('(detail.parent_code IS NULL AND detail.is_bom_parent = :isNotBomParent AND detail.staff_checked = :staffChecked)')
              // 2. BOM子项已被员工和BOM专员检查
              .orWhere('(detail.parent_code IS NOT NULL AND detail.staff_checked = :staffChecked AND detail.bom_specialist_checked = :bomChecked)')
              // 3. BOM父项（直接显示，不需要检查）- 包括错误标记的BOM父项
              .orWhere('(detail.is_bom_parent = :isBomParent OR (detail.parent_code IS NULL AND detail.description IS NOT NULL AND detail.description != \'\'))')
              // 4. bin为空或have_stock=false的BOM子项（可以直接由Supervisor检查）
              .orWhere('(detail.parent_code IS NOT NULL AND detail.is_bom_parent = :isNotBomParent AND (detail.bin_shelf_no = \'\' OR detail.have_stock = :noStock))')
              // 5. 已被员工检查但未被BOM专员检查的BOM子项（需要先由BOM专员检查）
              .orWhere('(detail.parent_code IS NOT NULL AND detail.staff_checked = :staffChecked AND detail.bom_specialist_checked = :bomNotChecked)');
          })
          .setParameter('staffChecked', true)
          .setParameter('supervisorChecked', false)
          .setParameter('bomChecked', true)
          .setParameter('bomNotChecked', false)
          .setParameter('isBomParent', true)
          .setParameter('isNotBomParent', false)
          .setParameter('noStock', false)
          .orderBy('main.priority', 'DESC')
          .addOrderBy('main.created_at', 'ASC')
          .getMany(),
        // 查询bin为空或者have_stock为false的记录，这些记录直接显示在pendingSupervisorCheck中
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .andWhere('(detail.bin_shelf_no = :emptyBin OR detail.have_stock = :noStock)')
          .andWhere('detail.main_id IN (:...mainIds)', { mainIds: allRelevantMainIds })
          .setParameter('emptyBin', '')
          .setParameter('noStock', false)
          .orderBy('main.priority', 'DESC')
          .addOrderBy('main.created_at', 'ASC')
          .getMany(),
        // 获取所有相关Document的所有detail，用于前端显示，限制数量避免性能问题
        this.detailRepository.createQueryBuilder('detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('detail.supervisor_checked = :supervisorChecked', { supervisorChecked: false })
          .andWhere('detail.main_id IN (:...mainIds)', { mainIds: allRelevantMainIds })
          .orderBy('main.priority', 'DESC')
          .addOrderBy('main.created_at', 'ASC')
          // .take(200) // 限制最多返回200条记录 - 暂时注释掉
          .getMany()
      ]);

      // 转换结果结构，添加所需字段
      const waitingList = waitingListDetails.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.created_at,
        priority: detail.main.priority || 0
      }));

      // 不再需要单独处理emptyBinOrNoStockItems，因为它们已经包含在allDetailsForDocuments中

      // 合并waitingList和allDetails，确保所有相关记录都被包含
      const combinedDetails = [...waitingListDetails, ...allDetailsForDocuments];

      // 去重（基于id）
      const uniqueDetails = combinedDetails.filter((detail, index, self) =>
        index === self.findIndex(d => d.id === detail.id)
      );

      const finalDetails = uniqueDetails.map(detail => ({
        ...detail,
        document_date: detail.main?.document_date || (detail as any).document_date,
        customer: detail.main?.customer || (detail as any).customer,
        customer_name: detail.main?.customer_name || (detail as any).customer_name,
        created_at: detail.created_at,
        priority: detail.main?.priority || (detail as any).priority || 0
      }));

      this.logger.log(`Supervisor data: waitingList=${waitingList.length}, finalDetails=${finalDetails.length}, uniqueDetails=${uniqueDetails.length}`);

      return {
        waitingList: finalDetails, // 整合版本：包含所有未被主管确认的订单（原waiting + to_confirm功能）
        pendingSupervisorCheck: [], // 清空，功能已整合到waitingList中
        total: finalDetails.length
      };
    }
  }

  /**
   * 为BOM父项添加子项状态信息
   */
  async enhanceBomParentsWithChildStatus(bomParents: Detail[]): Promise<any[]> {
    const enhancedParents = await Promise.all(
      bomParents.map(async (parent) => {
        // 获取该父项的所有子项 - 使用 bom_parent_id 而不是 parent_code
        const children = await this.detailRepository.find({
          where: {
            bom_parent_id: parent.id,
            is_bom_parent: false
          }
        });

        // 计算子项状态
        const totalChildren = children.length;
        const staffCheckedChildren = children.filter(c => c.staff_checked).length;
        const bomCheckedChildren = children.filter(c => c.bom_specialist_checked).length;
        const allChildrenStaffChecked = children.every(c => c.staff_checked);
        const allChildrenBomChecked = children.every(c => c.bom_specialist_checked);
        const canBomCheck = children.some(c => c.staff_checked && !c.bom_specialist_checked);
        const canSupervisorCheck = children.every(c => c.bom_specialist_checked);

        return {
          ...parent,
          // 添加子项状态信息
          total_children: totalChildren,
          staff_checked_children: staffCheckedChildren,
          bom_checked_children: bomCheckedChildren,
          all_children_staff_checked: allChildrenStaffChecked,
          all_children_bom_checked: allChildrenBomChecked,
          can_bom_check: canBomCheck,
          can_supervisor_check: canSupervisorCheck,
          // 保持原有字段
          document_date: parent.main?.document_date,
          customer: parent.main?.customer,
          customer_name: parent.main?.customer_name,
          created_at: parent.created_at,
          priority: parent.main?.priority || 0
        };
      })
    );

    return enhancedParents;
  }

  /**
   * 修复错误标记的BOM父项数据
   * 将没有parent_code但有description的记录标记为BOM父项
   */
  async fixBomParentData() {
    this.logger.log('Starting BOM parent data fix...');

    // 查找可能是BOM父项但标记错误的记录
    const potentialBomParents = await this.detailRepository
      .createQueryBuilder('detail')
      .where('detail.parent_code IS NULL')
      .andWhere('detail.description IS NOT NULL')
      .andWhere('detail.description != :empty', { empty: '' })
      .andWhere('detail.is_bom_parent = :notBomParent', { notBomParent: false })
      .getMany();

    this.logger.log(`Found ${potentialBomParents.length} potential BOM parent items with incorrect marking`);

    let fixedCount = 0;
    for (const detail of potentialBomParents) {
      // 检查是否有对应的BOM子项 - 使用 bom_parent_id 而不是 parent_code
      const childrenCount = await this.detailRepository
        .createQueryBuilder('detail')
        .where('detail.bom_parent_id = :bomParentId', { bomParentId: detail.id })
        .getCount();

      if (childrenCount > 0) {
        // 有子项，确实是BOM父项，修复标记
        detail.is_bom_parent = true;
        await this.detailRepository.save(detail);
        fixedCount++;
        this.logger.log(`Fixed BOM parent: ${detail.document_no}, line ${detail.line}, stock ${detail.stock}`);
      }
    }

    this.logger.log(`BOM parent data fix completed. Fixed ${fixedCount} records.`);
    return { fixed: fixedCount, total: potentialBomParents.length };
  }

  // 辅助函数：从带有后缀的document_no (如D00001/1)提取基本document_no (D00001)
  private extractBaseDocNo(documentNo: string): string {
    if (documentNo && documentNo.includes('/')) {
      return documentNo.split('/')[0];
    }
    return documentNo;
  }

  /**
   * 获取指定BOM组的所有子项状态
   * @param bomGroup BOM组标识
   * @param parentStock 可选的BOM父项库存代码，用于精确过滤子项
   * @param bomParentId 可选的BOM父项ID，用于更精确的过滤
   * @returns 子项列表及其状态
   */
  async getBomChildrenStatus(bomGroup: string, parentStock?: string, bomParentId?: number) {
    this.logger.log(`Getting BOM children status for group: ${bomGroup}${parentStock ? `, parentStock: ${parentStock}` : ''}${bomParentId ? `, bomParentId: ${bomParentId}` : ''}`);

    // 构建查询条件
    const whereCondition: any = {
      bom_group: bomGroup,
      is_bom_parent: false
    };

    // 优先使用bomParentId进行过滤，这样更精确
    if (bomParentId) {
      whereCondition.bom_parent_id = bomParentId;
      this.logger.log(`Using bomParentId filter: ${bomParentId}`);
    } else if (parentStock) {
      // 如果没有bomParentId，则使用parentStock作为备选
      whereCondition.parent_code = parentStock;
      this.logger.log(`Using parentStock filter: ${parentStock}`);
    }

    this.logger.log(`Final whereCondition: ${JSON.stringify(whereCondition)}`);

    // 删除这部分代码，避免变量重复声明

    // 直接使用TypeORM查询，它正确应用了所有过滤条件包括bomParentId

    // 使用TypeORM查询获取BOM子项
    const children = await this.detailRepository.find({
      where: whereCondition,
      relations: ['main']
    });

    // 直接使用TypeORM查询的结果，因为它正确应用了所有过滤条件
    const result = children.map(detail => ({
      ...detail,
      document_date: detail.main.document_date,
      customer: detail.main.customer,
      customer_name: detail.main.customer_name,
      created_at: detail.created_at,
      priority: detail.main.priority || 0
    }));

    this.logger.log(`Found ${result.length} BOM children for group: ${bomGroup}${bomParentId ? ` with bomParentId: ${bomParentId}` : parentStock ? ` with parentStock: ${parentStock}` : ''}`);
    return result;
  }

  /**
   * 获取BOM子项详细信息，用于supervisor的逐个检查模式
   * @param bomGroup BOM组标识
   * @param parentStock BOM父项库存代码
   * @param supervisorId 主管ID
   * @returns 子项详细列表，包含跳过检查标识
   */
  async getBomChildrenDetails(bomGroup: string, parentStock: string, supervisorId: number) {
    this.logger.log(`Getting BOM children details for supervisor check: bomGroup=${bomGroup}, parentStock=${parentStock}, supervisorId=${supervisorId}`);

    // 验证主管权限
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    if (!supervisor) {
      throw new NotFoundException('Supervisor not found');
    }
    if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin') {
      throw new ForbiddenException('Only supervisors can access BOM children details');
    }

    // 获取指定父项的所有子项
    const children = await this.detailRepository.find({
      where: {
        bom_group: bomGroup,
        parent_code: parentStock,
        is_bom_parent: false
      },
      relations: ['main'],
      order: {
        line: 'ASC'
      }
    });

    // 转换结果，添加跳过检查标识
    const result = children.map(detail => {
      const shouldSkipChecks = detail.bin_shelf_no === '' || !detail.have_stock;

      return {
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.created_at,
        priority: detail.main.priority || 0,
        skip_checks: shouldSkipChecks, // 标识是否跳过staff/staff_bom检查
        ready_for_supervisor: shouldSkipChecks || detail.bom_specialist_checked // 是否可以由supervisor检查
      };
    });

    this.logger.log(`Found ${result.length} BOM children details, ${result.filter(r => r.skip_checks).length} with skip conditions`);
    return result;
  }

  async checkByStaff(staffId: number, documentNo: string, line: number) {
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    // 明确禁止主管和管理员执行员工检查操作
    if (staff.level === 'supervisor' || staff.level === 'admin') {
      throw new ForbiddenException('Supervisors cannot perform staff checks. Please use supervisor check instead.');
    }

    // 先获取明细记录 - 使用LIKE查询匹配document_no，它可能带有后缀
    const details = await this.detailRepository
      .createQueryBuilder('detail')
      .where('(detail.document_no = :exactDocNo OR detail.document_no LIKE :docNoPattern)', {
        exactDocNo: documentNo,
        docNoPattern: `${documentNo}/%`
      })
      .andWhere('detail.line = :line', { line })
      .getMany();

    if (!details || details.length === 0) {
      throw new NotFoundException('Detail record not found');
    }

    // 使用第一条匹配的记录
    const detail = details[0];

    // 获取货物所在楼层
    const itemFloor = detail.bin_shelf_no.split('-')[0];

    // 验证权限 - 普通员工只能检查自己负责的楼层，主管(ALL)可以检查所有楼层
    // BOM管理员可以检查所有楼层
    if (staff.level !== 'staff_bom' && staff.floor !== 'ALL' && staff.floor !== `${itemFloor}F`) {
      throw new ForbiddenException('You don\'t have permission to check items on this floor');
    }

    // 处理BOM专员检查的情况
    if (staff.level === 'staff_bom') {
      // BOM专员只能检查有parent_code的BOM子项，不能检查BOM父项
      // 额外检查：如果没有parent_code，很可能是BOM父项（即使is_bom_parent标记错误）
      if (!detail.parent_code) {
        throw new ForbiddenException('BOM specialists can only check BOM child items (items with parent_code). This appears to be a BOM parent item.');
      }

      // 如果明确标记为BOM父项，也不能检查
      if (detail.is_bom_parent) {
        throw new ForbiddenException('BOM specialists cannot check BOM parent items');
      }

      // 检查物品是否已被普通员工检查过，但对于bin为空或have_stock=false的BOM子项可以跳过
      const shouldSkipStaffCheck = detail.bin_shelf_no === '' || !detail.have_stock;
      if (!detail.staff_checked && !shouldSkipStaffCheck) {
        throw new ForbiddenException('This BOM item must be checked by regular staff first');
      }

      // 检查是否已被BOM专员检查过
      if (detail.bom_specialist_checked) {
        throw new ForbiddenException('This item has already been checked by a BOM specialist');
      }

      // 标记为BOM专员已检查
      detail.bom_specialist_checked = true;
      detail.bom_specialist_checked_at = new Date();
      detail.bom_specialist_id = staffId;

      // 保存当前子项的更新
      await this.detailRepository.save(detail);

      // 检查并更新BOM父项的完成状态
      // 直接使用BOM父项ID进行更新，避免查找错误的父项
      if (detail.bom_parent_id) {
        await this.updateBomParentCompletionStatusById(detail.bom_parent_id);
      }
    } else {
      // 处理普通员工检查的情况
      // 检查此货物是否已被普通员工检查
      if (detail.staff_checked) {
        throw new ForbiddenException('This item has already been checked by staff');
      }

      // 检查是否有库存
      if (!detail.have_stock) {
        throw new ForbiddenException('This item has no stock and cannot be checked');
      }

      // 检查是否有货架位置
      if (detail.bin_shelf_no === '') {
        // 如果是BOM子项且没有货架位置，提示由Supervisor处理
        if (detail.parent_code) {
          throw new ForbiddenException('This BOM item has no bin location and should be handled directly by supervisor');
        } else {
          throw new ForbiddenException('This item has no bin location and cannot be checked');
        }
      }

      // 获取同一个DocumentNo下的所有detail记录
      // 提取基本文档编号，处理可能的后缀情况
      const baseDocNo = this.extractBaseDocNo(detail.document_no);

      // 查询同一个DocumentNo下已经被检查过的记录
      const checkedDetails = await this.detailRepository
        .createQueryBuilder('detail')
        .where('(detail.document_no = :baseDocNo OR detail.document_no LIKE :docNoPattern)', {
          baseDocNo: baseDocNo,
          docNoPattern: `${baseDocNo}/%`
        })
        .andWhere('detail.staff_checked = :staffChecked', { staffChecked: true })
        .andWhere('detail.staff_id IS NOT NULL')
        .getMany();

      // 如果有已检查的记录，检查是否是同一个staff
      if (checkedDetails.length > 0) {
        // 获取第一个已检查记录的staff_id
        const firstCheckerId = checkedDetails[0].staff_id;

        // 确保两个ID都是数字类型进行比较
        const firstCheckerIdNum = Number(firstCheckerId);
        const staffIdNum = Number(staffId);

        // 如果当前staff不是第一个检查者，且是同一楼层的检查
        if (firstCheckerIdNum !== staffIdNum) {
          // 获取第一个检查者的楼层
          const firstChecker = await this.staffRepository.findOne({ where: { id: firstCheckerIdNum } });

          // 如果找不到第一个检查者，允许当前staff继续检查
          if (!firstChecker) {
            // 允许当前staff继续检查
            return;
          }

          // 如果是同一楼层的检查，则限制只有第一个检查者可以继续检查
          if (firstChecker.floor === staff.floor) {
            const firstCheckerName = firstChecker.username;
            throw new ForbiddenException(`This document is being checked by ${firstCheckerName}. Only the same staff can check all items in a document.`);
          }
        }
      }

      // 普通员工检查
      detail.staff_checked = true;
      detail.staff_checked_at = new Date();
      detail.staff_id = staffId;
    }

    // 保存更新后的明细记录
    await this.detailRepository.save(detail);

    // 获取关联的主表数据（仅用于返回）
    const main = await this.mainRepository.findOne({
      where: { id: detail.main_id }
    });

    // 构建包含主表信息的返回对象
    const result = {
      ...detail,
      document_date: main.document_date,
      customer: main.customer,
      customer_name: main.customer_name,
      priority: main.priority || 0
    };

    return {
      success: true,
      message: `Item checked by ${staff.level === 'staff_bom' ? 'BOM specialist' : 'staff'} ${staff.username}${detail.parent_code && staff.level !== 'staff_bom' ? ' (requires BOM specialist check)' : ''}`,
      detail: result
    };
  }



  /**
   * 通过BOM父项ID更新完成状态
   * 避免通过bom_group和stock查找时可能出现的重复匹配问题
   */
  private async updateBomParentCompletionStatusById(bomParentId: number) {
    if (!bomParentId) {
      return;
    }

    this.logger.log(`Checking BOM parent completion status for ID: ${bomParentId}`);

    // 直接通过ID查找BOM父项
    const bomParent = await this.detailRepository.findOne({
      where: {
        id: bomParentId,
        is_bom_parent: true
      }
    });

    if (!bomParent) {
      this.logger.warn(`BOM parent not found for ID: ${bomParentId}`);
      return;
    }

    // 查找所有子项 - 使用 bom_parent_id
    const children = await this.detailRepository.find({
      where: {
        bom_parent_id: bomParentId,
        is_bom_parent: false
      }
    });

    if (children.length === 0) {
      this.logger.log(`No children found for BOM parent ID: ${bomParentId}`);
      return;
    }

    // 检查所有子项是否都完成了检查
    const allChildrenComplete = children.every(child => {
      const isSpecialBomItem = child.bin_shelf_no === '' || !child.have_stock;

      if (isSpecialBomItem) {
        // 特殊条件的子项：只需要BOM专员检查
        return child.bom_specialist_checked;
      } else {
        // 正常流程的子项：需要staff和BOM专员都检查
        return child.staff_checked && child.bom_specialist_checked;
      }
    });

    this.logger.log(`BOM parent ID ${bomParentId} (${bomParent.stock}): ${children.length} children, allComplete: ${allChildrenComplete}`);

    // 更新BOM父项的完成状态
    if (allChildrenComplete !== bomParent.bom_children_completed) {
      bomParent.bom_children_completed = allChildrenComplete;
      await this.detailRepository.save(bomParent);
      this.logger.log(`Updated BOM parent ID ${bomParentId} (${bomParent.stock}) completion status to: ${allChildrenComplete}`);
    }
  }

  async checkBySupervisor(supervisorId: number, documentNo: string, line: number) {
    // 使用事务来优化性能并确保数据一致性
    return await this.detailRepository.manager.transaction(async manager => {
      // 一次性获取所有需要的数据，减少数据库查询次数
      const [supervisor, details] = await Promise.all([
        manager.findOne(Staff, { where: { id: supervisorId } }),
        manager
          .createQueryBuilder(Detail, 'detail')
          .leftJoinAndSelect('detail.main', 'main')
          .where('(detail.document_no = :exactDocNo OR detail.document_no LIKE :docNoPattern)', {
            exactDocNo: documentNo,
            docNoPattern: `${documentNo}/%`
          })
          .andWhere('detail.line = :line', { line })
          .getMany()
      ]);

      if (!supervisor) {
        throw new NotFoundException('Supervisor not found');
      }

      if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin') {
        throw new ForbiddenException('Only supervisors can perform supervisor checks');
      }

      if (!details || details.length === 0) {
        throw new NotFoundException('Detail record not found');
      }

      // 优先选择 BOM 父项，如果没有 BOM 父项则使用第一条记录
      const detail = details.find(d => d.is_bom_parent) || details[0];
      const main = detail.main; // 已经通过 leftJoinAndSelect 加载

      // 检查是否是无库存商品、无货架位置商品或BOM父项，如果是则跳过staff和staff_bom检查
      if (!detail.have_stock || detail.bin_shelf_no === '' || detail.is_bom_parent) {
        // 无库存商品、无货架位置商品或BOM父项可以直接由Supervisor确认，不需要经过staff和staff_bom检查
        const skipReason = !detail.have_stock ? 'no stock' :
          detail.bin_shelf_no === '' ? 'no bin location' : 'BOM parent';
        this.logger.log(`Item ${detail.document_no}, line ${detail.line} is ${skipReason}, skipping staff/BOM specialist check`);
      } else {
        // 有库存商品需要按正常流程检查

        // 对于BOM子项（有parent_code的项目），处理检查逻辑
        if (detail.parent_code) {
          // 检查是否为特殊条件的BOM子项（bin_shelf_no为空或have_stock为false）
          const isSpecialBomItem = detail.bin_shelf_no === '' || !detail.have_stock;

          if (isSpecialBomItem) {
            // 特殊BOM子项：自动标记为已被staff和BOM专员检查，直接由Supervisor处理
            this.logger.log(`Auto-marking special BOM item as checked: ${detail.document_no}, line ${detail.line}, bin_shelf_no='${detail.bin_shelf_no}', have_stock=${detail.have_stock}`);

            if (!detail.staff_checked) {
              detail.staff_checked = true;
              detail.staff_checked_at = new Date();
              detail.staff_id = supervisorId; // 使用主管ID作为staff_id
            }

            if (!detail.bom_specialist_checked) {
              detail.bom_specialist_checked = true;
              detail.bom_specialist_checked_at = new Date();
              detail.bom_specialist_id = supervisorId; // 使用主管ID作为bom_specialist_id
            }
          } else {
            // 正常流程的BOM子项：需要确保已被BOM专员检查过
            if (!detail.bom_specialist_checked || !detail.bom_specialist_id) {
              throw new ForbiddenException('BOM items must be checked by a BOM specialist before supervisor verification');
            }

            // 优化：只在必要时查询BOM专员信息，并使用缓存
            if (!this.bomSpecialistCache.has(detail.bom_specialist_id)) {
              const bomSpecialist = await manager.findOne(Staff, { where: { id: detail.bom_specialist_id } });
              this.bomSpecialistCache.set(detail.bom_specialist_id, bomSpecialist);
            }

            const bomSpecialist = this.bomSpecialistCache.get(detail.bom_specialist_id);
            if (!bomSpecialist || bomSpecialist.level !== 'staff_bom') {
              throw new ForbiddenException('BOM items must be properly checked by a valid BOM specialist');
            }
          }
        } else {
          // 对于非BOM项目，确保已被员工检查过
          if (!detail.staff_checked) {
            throw new ForbiddenException('Item must be checked by staff first');
          }
        }
      }

      // 检查被选中的项目是否已经被主管检查过
      if (detail.supervisor_checked) {
        throw new ForbiddenException('This item has already been verified by a supervisor');
      }

      // 如果是BOM父项，需要特殊处理
      if (detail.is_bom_parent) {
        const bomGroup = detail.bom_group || this.extractBaseDocNo(detail.document_no);
        this.logger.log(`Processing BOM parent: documentNo=${detail.document_no}, line=${detail.line}, stock=${detail.stock}, bomGroup=${bomGroup}`);

        // 获取该父项的所有子项 - 使用 bom_parent_id 而不是 parent_code
        const children = await manager.find(Detail, {
          where: {
            bom_parent_id: detail.id,
            is_bom_parent: false
          }
        });

        this.logger.log(`Found ${children.length} BOM children for parent ID ${detail.id} (stock: ${detail.stock})`);

        if (children.length > 0) {
          children.forEach((child, index) => {
            this.logger.log(`  Child ${index + 1}: documentNo=${child.document_no}, line=${child.line}, stock=${child.stock}, bom_specialist_checked=${child.bom_specialist_checked}, supervisor_checked=${child.supervisor_checked}`);
          });
        }

        // 验证所有子项都已被BOM专员检查
        const allChildrenBomChecked = children.every(c => c.bom_specialist_checked);
        if (!allChildrenBomChecked) {
          this.logger.warn(`Not all BOM children are checked by BOM specialist for parent ${detail.stock}`);
          throw new ForbiddenException('All BOM children must be checked by BOM specialist first');
        }

        // 同时标记父项和所有子项为supervisor_checked
        const updatePromises = [
          // 更新父项
          manager.save(Detail, {
            ...detail,
            supervisor_checked: true,
            supervisor_checked_at: new Date(),
            supervisor_id: supervisorId
          }),
          // 更新所有子项
          ...children.map(child =>
            manager.save(Detail, {
              ...child,
              supervisor_checked: true,
              supervisor_checked_at: new Date(),
              supervisor_id: supervisorId
            })
          )
        ];

        await Promise.all(updatePromises);
        this.logger.log(`Successfully updated BOM parent and all ${children.length} children to supervisor_checked=true`);

        // 更新detail对象以便返回
        detail.supervisor_checked = true;
        detail.supervisor_checked_at = new Date();
        detail.supervisor_id = supervisorId;
      } else {
        // 非BOM父项的正常处理
        detail.supervisor_checked = true;
        detail.supervisor_checked_at = new Date();
        detail.supervisor_id = supervisorId;

        // 保存更新后的明细记录
        await manager.save(Detail, detail);
      }

      // 构建包含主表信息的返回对象
      const result = {
        ...detail,
        document_date: main.document_date,
        customer: main.customer,
        customer_name: main.customer_name,
        priority: main.priority || 0
      };

      return {
        success: true,
        message: `Item verified by supervisor ${supervisor.username}`,
        detail: result
      };
    });
  }

  // 添加BOM专员缓存
  private bomSpecialistCache = new Map<number, Staff>();

  // 批量主管检查
  async batchCheckBySupervisor(supervisorId: number, items: Array<{ documentNo: string; line: number }>) {
    if (!items || items.length === 0) {
      throw new BadRequestException('No items provided for batch check');
    }

    // 限制批量操作的数量，防止性能问题
    if (items.length > 50) {
      throw new BadRequestException('Batch size cannot exceed 50 items');
    }

    return await this.detailRepository.manager.transaction(async manager => {
      const results = [];
      const errors = [];

      // 验证主管权限（只需要验证一次）
      const supervisor = await manager.findOne(Staff, { where: { id: supervisorId } });
      if (!supervisor) {
        throw new NotFoundException('Supervisor not found');
      }

      if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin') {
        throw new ForbiddenException('Only supervisors can perform supervisor checks');
      }

      // 批量处理每个项目
      for (const item of items) {
        try {
          const result = await this.checkBySupervisor(supervisorId, item.documentNo, item.line);
          results.push({
            documentNo: item.documentNo,
            line: item.line,
            success: true,
            result
          });
        } catch (error) {
          errors.push({
            documentNo: item.documentNo,
            line: item.line,
            success: false,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: `Batch check completed. ${results.length} successful, ${errors.length} failed.`,
        results,
        errors,
        summary: {
          total: items.length,
          successful: results.length,
          failed: errors.length
        }
      };
    });
  }

  async checkByBomSpecialist(bomSpecialistId: number, documentNo: string, line: number) {
    console.log(`Starting BOM specialist check for document ${documentNo}, line ${line}`);

    const staff = await this.staffRepository.findOne({ where: { id: bomSpecialistId } });
    if (!staff) {
      throw new NotFoundException('BOM specialist not found');
    }

    // 检查权限 - 只有BOM专员可以执行此操作
    if (staff.level !== 'staff_bom') {
      throw new ForbiddenException('Only BOM specialists can perform this check');
    }

    // 获取明细记录
    const details = await this.detailRepository
      .createQueryBuilder('detail')
      .where('(detail.document_no = :exactDocNo OR detail.document_no LIKE :docNoPattern)', {
        exactDocNo: documentNo,
        docNoPattern: `${documentNo}/%`
      })
      .andWhere('detail.line = :line', { line })
      .getMany();

    if (!details || details.length === 0) {
      throw new NotFoundException('Detail record not found');
    }

    // 使用第一条匹配的记录
    const detail = details[0];

    // BOM专员只能检查有parent_code的BOM子项，不能检查BOM父项
    if (!detail.parent_code || detail.is_bom_parent) {
      throw new ForbiddenException('BOM specialists can only check BOM child items (with parent_code and not a BOM parent)');
    }

    // 检查是否为特殊条件的BOM子项（无货架位置或无库存）
    const isSpecialBomItem = detail.bin_shelf_no === '' || !detail.have_stock;

    // 对于特殊条件的BOM子项，BOM专员可以直接检查，跳过staff检查要求
    if (!isSpecialBomItem) {
      // 非特殊条件的BOM子项，需要先被普通员工检查过
      if (!detail.staff_checked) {
        throw new ForbiddenException('This BOM item must be checked by regular staff first');
      }
    }

    // 检查是否已被BOM专员检查过
    if (detail.bom_specialist_checked) {
      throw new ForbiddenException('This item has already been checked by a BOM specialist');
    }

    // BOM专员独占检查验证：确保同一BOM父项下的所有子项由同一专员完成
    if (detail.bom_parent_id) {
      // 查询该BOM父项下已被其他专员检查的子项
      const existingChecks = await this.detailRepository.find({
        where: {
          bom_parent_id: detail.bom_parent_id,
          bom_specialist_checked: true,
          bom_specialist_id: Not(bomSpecialistId) // 排除当前专员
        },
        take: 1 // 只需要知道是否存在，不需要全部记录
      });

      if (existingChecks.length > 0) {
        // 获取负责专员的姓名
        const responsibleSpecialist = await this.staffRepository.findOne({
          where: { id: existingChecks[0].bom_specialist_id }
        });

        const specialistName = responsibleSpecialist?.full_name || 'Unknown Specialist';

        // 根据当前BOM专员的preferred_language显示相应语言的错误信息
        const currentLanguage = staff.preferred_language || 'zh'; // 默认中文

        const errorMessages = {
          zh: `该BOM父项已由专员 ${specialistName} 开始检查，所有子项必须由同一专员完成`,
          en: `This BOM parent has been started by specialist ${specialistName}, all child items must be completed by the same specialist`,
          ms: `BOM induk ini telah dimulakan oleh pakar ${specialistName}, semua item anak mesti diselesaikan oleh pakar yang sama`
        };

        const errorMessage = errorMessages[currentLanguage] || errorMessages.zh;
        throw new ForbiddenException(errorMessage);
      }
    }

    // 特殊BOM子项现在由Supervisor直接处理，BOM专员不再处理这些项目

    // 标记为BOM专员已检查
    detail.bom_specialist_checked = true;
    detail.bom_specialist_checked_at = new Date();
    detail.bom_specialist_id = bomSpecialistId;

    // 保存更新后的明细记录
    await this.detailRepository.save(detail);

    // 检查并更新BOM父项的完成状态
    // 直接使用BOM父项ID进行更新，避免查找错误的父项
    if (detail.bom_parent_id) {
      await this.updateBomParentCompletionStatusById(detail.bom_parent_id);
    }

    // 获取关联的主表数据（仅用于返回）
    const main = await this.mainRepository.findOne({
      where: { id: detail.main_id }
    });

    // 构建包含主表信息的返回对象
    const result = {
      ...detail,
      document_date: main.document_date,
      customer: main.customer,
      customer_name: main.customer_name,
      priority: main.priority || 0
    };

    return {
      success: true,
      message: `BOM item checked by specialist ${staff.username}`,
      detail: result
    };
  }

  async getFloorCheckStats(floor: string): Promise<{ pendingStaffCheck: number, pendingSupervisorCheck: number, totalPending: number }> {
    // 查询指定楼层的所有待检查项目
    const details = await this.detailRepository
      .createQueryBuilder('detail')
      .leftJoin('detail.main', 'main')
      .where('detail.bin_shelf_no LIKE :floorPattern', { floorPattern: `${floor}-%` })
      .getMany();

    // 计算待员工检查数量
    const pendingStaffCheck = details.filter(item => !item.staff_checked).length;

    // 计算待主管检查数量
    const pendingSupervisorCheck = details.filter(item => item.staff_checked && !item.supervisor_checked).length;

    // 计算总待检查数量
    const totalPending = pendingStaffCheck + pendingSupervisorCheck;

    return {
      pendingStaffCheck,
      pendingSupervisorCheck,
      totalPending
    };
  }

  async getFloorStats(floor: string): Promise<{
    pendingStaffCheck: number;
    pendingSupervisorCheck: number;
    totalPending: number;
  }> {
    const pendingStaffCheck = await this.detailRepository.count({
      where: {
        bin_shelf_no: Like(`${floor}%`),
        staff_checked: false
      }
    });

    const pendingSupervisorCheck = await this.detailRepository.count({
      where: {
        bin_shelf_no: Like(`${floor}%`),
        staff_checked: true,
        supervisor_checked: false
      }
    });

    return {
      pendingStaffCheck,
      pendingSupervisorCheck,
      totalPending: pendingStaffCheck + pendingSupervisorCheck
    };
  }

  /**
   * 获取待检查列表 - 支持搜索，返回完整数据集
   * 用于Queue页面，移除分页功能
   */
  async getPendingChecksPaginated(
    staffId: number,
    page: number = 1,
    limit: number = 10,
    searchQuery?: string
  ): Promise<{ data: any, total: number, hasMore: boolean }> {
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    if (staff.level === 'regular') {
      // 普通员工的分页逻辑 - 改为按main文档分页
      const floor = staff.floor.replace('F', '');

      // 第一步：获取符合条件的main文档ID（使用子查询方式）
      let baseQuery = this.mainRepository
        .createQueryBuilder('main')
        .select(['main.id', 'main.created_at', 'main.priority'])  // 添加 priority 字段
        .innerJoin('main.details', 'detail')
        .where('detail.bin_shelf_no LIKE :floorPattern', { floorPattern: `${floor}-%` })
        .andWhere('detail.is_bom_parent = :isBomParent', { isBomParent: false })
        .andWhere(`(
          (detail.staff_checked = false AND detail.supervisor_checked = false AND detail.parent_code IS NULL AND detail.have_stock = true) OR
          (detail.staff_checked = false AND detail.supervisor_checked = false AND detail.have_stock = true) OR
          (detail.staff_checked = true AND detail.supervisor_checked = false AND detail.staff_id = :staffId)
        )`, { staffId });

      // 添加搜索条件
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = `%${searchQuery.trim()}%`;
        baseQuery = baseQuery.andWhere(`(
          detail.document_no LIKE :searchTerm OR
          main.customer_name LIKE :searchTerm OR
          detail.stock LIKE :searchTerm OR
          detail.description LIKE :searchTerm
        )`, { searchTerm });
      }

      // 获取所有符合条件的main文档
      const allMainDocs = await baseQuery
        .groupBy('main.id')
        .orderBy('COALESCE(main.priority, 0)', 'DESC')  // 优先级降序，null值当作0处理
        .addOrderBy('main.created_at', 'ASC')           // 创建时间升序
        .getMany();

      if (allMainDocs.length === 0) {
        return {
          data: {
            pendingRegularCheck: [],
            pendingBomCheck: [],
            pendingSupervisorCheck: [],
            total: 0
          },
          total: 0,
          hasMore: false
        };
      }

      const mainIds = allMainDocs.map(doc => doc.id);

      // 第二步：获取这些main文档对应的所有detail记录
      // 获取BOM父项
      const bomParents = await this.detailRepository.find({
        where: {
          main_id: In(mainIds),
          bin_shelf_no: Like(`${floor}-%`),
          is_bom_parent: true
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      // 获取非BOM项目
      const nonBomItems = await this.detailRepository.find({
        where: {
          main_id: In(mainIds),
          bin_shelf_no: Like(`${floor}-%`),
          is_bom_parent: false,
          parent_code: IsNull()
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      const allDetails = [...bomParents, ...nonBomItems];

      // 分离BOM父项和非BOM项目
      const bomParentsOnly = allDetails.filter(detail => detail.is_bom_parent);
      const nonBomItemsOnly = allDetails.filter(detail => !detail.is_bom_parent);

      // 为BOM父项添加子项状态信息
      const enhancedBomParents = await this.enhanceBomParentsWithChildStatus(bomParentsOnly);

      // 转换非BOM项目数据
      const transformedNonBomItems = nonBomItemsOnly.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.main.created_at,
        priority: detail.main.priority || 0
      }));

      // 合并数据
      const transformedData = [...enhancedBomParents, ...transformedNonBomItems];

      // 调试信息（已注释）
      // console.log(`[getPendingChecksPaginated - regular] 调试信息:`);
      // console.log(`  - 页码: ${pageNum}, 每页数量: ${limitNum}, 跳过: ${skip}`);
      // console.log(`  - 总文档数: ${totalCount}, 返回文档数: ${paginatedMainDocs.length}`);
      // console.log(`  - 返回detail数: ${transformedData.length}`);
      // console.log(`  - 文档排序检查:`);
      // paginatedMainDocs.forEach((doc, index) => {
      //   console.log(`    ${index + 1}. ID: ${doc.id}, Priority: ${doc.priority || 0}, Created: ${doc.created_at}`);
      // });
      // console.log(`  - hasMore计算: ${skip} + ${limitNum} < ${totalCount} = ${skip + limitNum < totalCount}`);

      return {
        data: {
          pendingRegularCheck: transformedData.filter(d => !d.parent_code && !d.is_bom_parent && !d.staff_checked),
          pendingBomCheck: transformedData.filter(d => d.parent_code && !d.is_bom_parent && !d.staff_checked),
          pendingSupervisorCheck: transformedData.filter(d => d.staff_checked && !d.supervisor_checked),
          total: transformedData.length
        },
        total: transformedData.length,
        hasMore: false
      };
    } else if (staff.level === 'staff_bom') {
      // BOM专员逻辑 - 返回完整数据集
      // 获取包含BOM项的main文档
      let bomBaseQuery = this.mainRepository
        .createQueryBuilder('main')
        .select(['main.id', 'main.created_at', 'main.priority'])  // 添加 priority 字段
        .innerJoin('main.details', 'detail')
        .where('(detail.is_bom_parent = :isBomParent OR detail.bom_parent_id IS NOT NULL OR detail.parent_code IS NOT NULL)', { isBomParent: true })
        .andWhere('detail.is_bom_parent = false');

      // 添加搜索条件
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = `%${searchQuery.trim()}%`;
        bomBaseQuery = bomBaseQuery.andWhere(`(
          detail.document_no LIKE :searchTerm OR
          main.customer_name LIKE :searchTerm OR
          detail.stock LIKE :searchTerm OR
          detail.description LIKE :searchTerm
        )`, { searchTerm });
      }

      // 获取所有符合条件的main文档
      const allBomMainDocs = await bomBaseQuery
        .groupBy('main.id')
        .orderBy('COALESCE(main.priority, 0)', 'DESC')  // 优先级降序，null值当作0处理
        .addOrderBy('main.created_at', 'ASC')           // 创建时间升序
        .getMany();

      if (allBomMainDocs.length === 0) {
        return {
          data: {
            waitingList: [],
            pendingStaffCheck: [],
            pendingBomCheck: [],
            pendingSupervisorCheck: [],
            total: 0
          },
          total: 0,
          hasMore: false
        };
      }

      const bomMainIds = allBomMainDocs.map(doc => doc.id);

      // 第二步：获取这些main文档对应的BOM父项和非BOM项目
      // 获取BOM父项
      const bomParents = await this.detailRepository.find({
        where: {
          main_id: In(bomMainIds),
          is_bom_parent: true
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      // 获取非BOM项目
      const nonBomItems = await this.detailRepository.find({
        where: {
          main_id: In(bomMainIds),
          is_bom_parent: false,
          parent_code: IsNull()
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      const allDetails = [...bomParents, ...nonBomItems];

      // 分离BOM父项和非BOM项目
      const bomParentsOnly = allDetails.filter(detail => detail.is_bom_parent);
      const nonBomItemsOnly = allDetails.filter(detail => !detail.is_bom_parent);

      // 过滤掉所有子项都被BOM专员检查完毕的BOM父项
      // 使用新的 bom_children_completed 字段，简化逻辑
      const activeBomParents = bomParentsOnly.filter(parent => {
        const shouldKeep = !parent.bom_children_completed;
        this.logger.log(`[getPendingChecksPaginated] BOM parent ${parent.document_no} (${parent.stock}): bom_children_completed=${parent.bom_children_completed}, shouldKeep=${shouldKeep}`);
        return shouldKeep;
      });

      // 为活跃的BOM父项添加子项状态信息（用于前端显示）
      const enhancedBomParents = await this.enhanceBomParentsWithChildStatus(activeBomParents);

      // 转换非BOM项目数据
      const transformedNonBomItems = nonBomItemsOnly.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.main.created_at,
        priority: detail.main.priority || 0
      }));

      // 合并数据
      const transformedData = [...enhancedBomParents, ...transformedNonBomItems];

      // 调试信息（已注释）
      // console.log(`[getPendingChecksPaginated - staff_bom] 调试信息:`);
      // console.log(`  - 页码: ${pageNum}, 每页数量: ${limitNum}, 跳过: ${skip}`);
      // console.log(`  - 总文档数: ${totalCount}, 返回文档数: ${paginatedBomMainDocs.length}`);
      // console.log(`  - 返回detail数: ${transformedData.length}`);
      // console.log(`  - hasMore计算: ${skip} + ${limitNum} < ${totalCount} = ${skip + limitNum < totalCount}`);

      return {
        data: {
          waitingList: transformedData,
          pendingStaffCheck: transformedData.filter(d => !d.staff_checked && !d.parent_code && d.have_stock),
          pendingBomCheck: transformedData.filter(d => {
            // 基本条件：必须是BOM子项且未被BOM专员检查
            const isBomChild = d.parent_code && !d.is_bom_parent && !d.bom_specialist_checked;
            if (!isBomChild) return false;

            // 检查是否为特殊条件的BOM子项（无货架位置或无库存）
            const isSpecialBomItem = d.bin_shelf_no === '' || !d.have_stock;

            // 特殊条件的BOM子项可以直接由BOM专员检查，不需要等待staff检查
            // 正常流程的BOM子项需要先被普通员工检查
            return isSpecialBomItem || d.staff_checked;
          }),
          pendingSupervisorCheck: transformedData.filter(d => !d.supervisor_checked && d.bom_specialist_checked),
          total: transformedData.length
        },
        total: transformedData.length,
        hasMore: false
      };
    } else {
      // 主管/管理员逻辑 - 返回完整数据集
      // 获取符合条件的main文档
      let supervisorBaseQuery = this.mainRepository
        .createQueryBuilder('main')
        .select(['main.id', 'main.created_at', 'main.priority'])  // 添加 priority 字段
        .innerJoin('main.details', 'detail')
        .where('detail.supervisor_checked = false')
        .andWhere('detail.is_bom_parent = false');

      // 添加搜索条件
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = `%${searchQuery.trim()}%`;
        supervisorBaseQuery = supervisorBaseQuery.andWhere(`(
          detail.document_no LIKE :searchTerm OR
          main.customer_name LIKE :searchTerm OR
          detail.stock LIKE :searchTerm OR
          detail.description LIKE :searchTerm
        )`, { searchTerm });
      }

      // 获取所有符合条件的main文档
      const allMainDocs = await supervisorBaseQuery
        .groupBy('main.id')
        .orderBy('COALESCE(main.priority, 0)', 'DESC')  // 优先级降序，null值当作0处理
        .addOrderBy('main.created_at', 'ASC')           // 创建时间升序
        .getMany();

      if (allMainDocs.length === 0) {
        return {
          data: {
            waitingList: [],
            pendingSupervisorCheck: [],
            total: 0
          },
          total: 0,
          hasMore: false
        };
      }

      const mainIds = allMainDocs.map(doc => doc.id);

      // 第二步：获取这些main文档对应的所有detail记录
      // 获取BOM父项和非BOM项目
      const bomParents = await this.detailRepository.find({
        where: {
          main_id: In(mainIds),
          supervisor_checked: false,
          is_bom_parent: true
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      // 获取非BOM项目
      const nonBomItems = await this.detailRepository.find({
        where: {
          main_id: In(mainIds),
          supervisor_checked: false,
          is_bom_parent: false,
          parent_code: IsNull()
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      const waitingListDetails = [...bomParents, ...nonBomItems];

      // 分离BOM父项和非BOM项目
      const bomParentsOnly = waitingListDetails.filter(detail => detail.is_bom_parent);
      const nonBomItemsOnly = waitingListDetails.filter(detail => !detail.is_bom_parent);

      // 为BOM父项添加子项状态信息
      const enhancedBomParents = await this.enhanceBomParentsWithChildStatus(bomParentsOnly);

      // 转换非BOM项目数据
      const transformedNonBomItems = nonBomItemsOnly.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.main.created_at,
        priority: detail.main.priority || 0
      }));

      // 合并数据
      const transformedData = [...enhancedBomParents, ...transformedNonBomItems];

      // 调试信息（已注释）
      // const hasMoreResult = skip + limitNum < totalCount;
      // console.log(`[getPendingChecksPaginated - supervisor] 调试信息:`);
      // console.log(`  - 页码: ${pageNum}, 每页数量: ${limitNum}, 跳过: ${skip}`);
      // console.log(`  - 总文档数: ${totalCount}, 返回文档数: ${paginatedMainDocs.length}`);
      // console.log(`  - 返回detail数: ${transformedData.length}`);
      // console.log(`  - 文档排序检查:`);
      // paginatedMainDocs.forEach((doc, index) => {
      //   console.log(`    ${index + 1}. ID: ${doc.id}, Priority: ${doc.priority || 0}, Created: ${doc.created_at}`);
      // });
      // console.log(`  - hasMore计算: ${skip + limitNum} < ${totalCount} = ${hasMoreResult}`);

      return {
        data: {
          waitingList: transformedData,
          pendingSupervisorCheck: [], // 主管版本中已整合到waitingList
          total: transformedData.length
        },
        total: transformedData.length,
        hasMore: false
      };
    }
  }

  async rejectStaffCheck(supervisorId: number, documentNo: string, line: number, reason: string) {
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    if (!supervisor) {
      throw new NotFoundException('Supervisor not found');
    }

    if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin' && supervisor.level !== 'staff_bom') {
      throw new ForbiddenException('Only supervisors or BOM specialists can reject checks');
    }

    // 获取明细记录 - 使用LIKE查询匹配document_no，它可能带有后缀
    const details = await this.detailRepository
      .createQueryBuilder('detail')
      .where('(detail.document_no = :exactDocNo OR detail.document_no LIKE :docNoPattern)', {
        exactDocNo: documentNo,
        docNoPattern: `${documentNo}/%`
      })
      .andWhere('detail.line = :line', { line })
      .getMany();

    if (!details || details.length === 0) {
      throw new NotFoundException('Detail record not found');
    }

    // 使用第一条匹配的记录
    const detail = details[0];

    // 确保该项目已经被员工检查过了
    if (!detail.staff_checked) {
      throw new ForbiddenException('Item must be checked by staff first before it can be rejected');
    }

    // 如果已经被主管确认，则不允许拒绝
    if (detail.supervisor_checked) {
      throw new ForbiddenException('This item has already been verified by a supervisor and cannot be rejected');
    }

    // 保存当前检查员工的ID以便发送通知
    const staffId = detail.staff_id;
    const bomSpecialistId = detail.bom_specialist_id;

    // 更新检查状态，重置所有检查标记
    detail.staff_checked = false;
    detail.staff_checked_at = null;
    // 保留staff_id以便知道是谁曾经检查过

    // 根据拒绝者的角色决定如何重置BOM专员检查状态
    if (supervisor.level === 'staff_bom') {
      // 如果是BOM专员拒绝，无条件重置BOM专员相关字段
      detail.bom_specialist_checked = false;
      detail.bom_specialist_checked_at = null;
      // 保留bom_specialist_id以便知道是谁曾经检查过
    } else {
      // 如果是主管或司机拒绝，只有当是BOM物品且已被BOM专员检查过时才重置
      if (detail.parent_code && detail.bom_specialist_checked) {
        detail.bom_specialist_checked = false;
        detail.bom_specialist_checked_at = null;
        // 保留bom_specialist_id以便知道是谁曾经检查过
      }
    }

    // 保存更新后的明细记录
    await this.detailRepository.save(detail);

    // 创建通知给普通员工
    if (staffId) {
      await this.notificationService.createRejectNotification(
        supervisorId,
        staffId,
        detail.document_no, // 使用detail原始的document_no
        detail.line,
        detail.id,
        reason
      );
    }

    // 如果已有BOM专员检查过，也创建通知给BOM专员
    // 但如果拒绝者就是BOM专员自己，则不需要发送通知
    if (bomSpecialistId && bomSpecialistId !== staffId && bomSpecialistId !== supervisorId) {
      await this.notificationService.createRejectNotification(
        supervisorId,
        bomSpecialistId,
        detail.document_no,
        detail.line,
        detail.id,
        reason + " (BOM specialist check rejected)"
      );
    }

    // 根据拒绝者的角色生成不同的消息
    let rejecterRole = '';
    if (supervisor.level === 'supervisor' || supervisor.level === 'admin') {
      rejecterRole = 'supervisor';
    } else if (supervisor.level === 'staff_bom') {
      rejecterRole = 'BOM specialist';
    }

    return {
      success: true,
      message: `Staff check for item ${detail.document_no}-${detail.line} has been rejected by ${rejecterRole} ${supervisor.username}`,
      detail
    };
  }

  // 检查文档是否可以完成出货（所有项目都已被主管确认）
  // 简化版本：BOM父项直接检查bom_children_completed字段，不再遍历子项
  async isDocumentReady(documentNo: string): Promise<{ ready: boolean, unconfirmedItems?: number[] }> {
    // 检查文档是否已出货
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 如果已出货，则直接返回
    if (main.is_shipped) {
      return { ready: false, unconfirmedItems: [0] };
    }

    // 获取所有与此main关联的detail记录
    const details = await this.detailRepository.find({
      where: { main_id: main.id }
    });

    if (!details.length) {
      throw new NotFoundException(`No details found for document ${documentNo}`);
    }

    // 过滤出未经主管确认的项目
    const unconfirmedItems = details.filter(item => {
      // 如果不是BOM父项，直接检查supervisor_checked状态
      if (!item.is_bom_parent) {
        return !item.supervisor_checked;
      }

      // 如果是BOM父项，检查bom_children_completed状态
      // bom_children_completed为true表示所有子项已完成，父项也算完成
      // bom_children_completed为false或null表示父项未完成
      return !item.bom_children_completed;
    });



    return {
      ready: unconfirmedItems.length === 0,
      unconfirmedItems: unconfirmedItems.length > 0
        ? unconfirmedItems.map(item => item.line)
        : undefined
    };
  }

  /**
   * 获取所有未出货的文档（包括未准备好的）
   * 每个文档包含准备状态信息，支持搜索功能，返回完整数据集
   */
  async getAllUnshippedDocuments(staffId: number, page: number = 1, limit: number = 10, searchQuery?: string): Promise<{ data: any[], total: number, hasMore: boolean }> {
    // 权限检查
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    if (staff.level !== 'supervisor' && staff.level !== 'admin' && staff.level !== 'driver') {
      throw new ForbiddenException('Only supervisors or drivers can view documents');
    }

    // 构建查询条件
    let whereCondition: any = { is_shipped: false };

    // 如果有搜索查询，添加搜索条件
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = `%${searchQuery.trim()}%`;
      whereCondition = [
        { is_shipped: false, document_no: Like(searchTerm) },
        { is_shipped: false, customer: Like(searchTerm) },
        { is_shipped: false, customer_name: Like(searchTerm) },
        { is_shipped: false, remarks: Like(searchTerm) }
      ];
    }

    // 获取所有未出货文档
    const documents = await this.mainRepository.find({
      where: whereCondition,
      order: {
        priority: 'DESC', // 优先级高的在前
        created_at: 'ASC'  // 然后按创建时间升序
      }
    });

    // 为每个文档检查准备状态
    const documentsWithStatus = [];
    for (const doc of documents) {
      try {
        const { ready, unconfirmedItems } = await this.isDocumentReady(doc.document_no);
        documentsWithStatus.push({
          ...doc,
          ready_to_ship: ready,
          unconfirmed_items: unconfirmedItems || []
        });
      } catch (error) {
        // 如果检查失败，标记为未准备好
        console.warn(`Failed to check document ${doc.document_no}:`, error.message);
        documentsWithStatus.push({
          ...doc,
          ready_to_ship: false,
          unconfirmed_items: []
        });
      }
    }

    return {
      data: documentsWithStatus,
      total: documentsWithStatus.length,
      hasMore: false
    };
  }

  /**
   * 获取准备好的文档 - 返回完整数据集，移除分页功能
   */
  async getToShipDocuments(staffId: number, page: number = 1, limit: number = 10): Promise<{ data: Main[], total: number, hasMore: boolean }> {
    // 权限检查
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    if (staff.level !== 'supervisor' && staff.level !== 'admin' && staff.level !== 'driver') {
      throw new ForbiddenException('Only supervisors or drivers can view ready documents');
    }

    try {
      // 获取所有未出货的文档，按优先级和创建时间排序
      const unshippedDocs = await this.mainRepository
        .createQueryBuilder('main')
        .where('main.is_shipped = :isShipped', { isShipped: false })
        .orderBy('COALESCE(main.priority, 0)', 'DESC')
        .addOrderBy('main.created_at', 'ASC')
        .getMany();

      // 筛选出所有准备好出货的文档
      const readyDocuments = [];

      for (const doc of unshippedDocs) {
        // 检查文档是否准备好
        const { ready } = await this.isDocumentReady(doc.document_no);
        if (ready) {
          readyDocuments.push(doc);
        }
      }

      return {
        data: readyDocuments,
        total: readyDocuments.length,
        hasMore: false
      };
    } catch (error) {
      this.logger.error('Error in getToShipDocuments:', error);
      throw new InternalServerErrorException('Failed to fetch ready documents');
    }
  }

  // 获取可以完成出货的文档列表 - 返回完整数据集，移除分页功能
  async getReadyDocuments(staffId: number, page: number = 1, limit: number = 10): Promise<{ data: Main[], total: number, hasMore: boolean }> {
    // 检查用户是否存在
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    // 检查用户权限
    if (staff.level !== 'supervisor' && staff.level !== 'admin' && staff.level !== 'driver') {
      throw new ForbiddenException('Only supervisors or drivers can view ready documents');
    }

    // 查找所有未出货的文档
    const documents = await this.mainRepository.find({
      where: { is_shipped: false },
      order: {
        priority: 'DESC', // 优先级高的在前
        created_at: 'ASC'  // 然后按创建时间升序，优先处理较早的订单
      }
    });

    // 筛选出所有项目都已确认的文档
    const readyDocuments = [];

    for (const doc of documents) {
      try {
        // 检查该文档是否可以出货（所有项目都已确认）
        const { ready } = await this.isDocumentReady(doc.document_no);

        if (ready) {
          readyDocuments.push(doc);
        }
      } catch (error) {
        // 记录错误但继续处理其他文档
        console.warn(`Failed to check document ${doc.document_no}:`, error.message);
        continue;
      }
    }

    return {
      data: readyDocuments,
      total: readyDocuments.length,
      hasMore: false
    };
  }

  // 标记文档为已出货
  async completeDocument(documentNo: string, supervisorId: number, driverId?: number): Promise<{ success: boolean, message: string, document: Main, pdfPath?: string }> {
    // 检查文档是否存在
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 如果已经出货，则返回错误
    if (main.is_shipped) {
      throw new BadRequestException(`Document ${documentNo} has already been shipped`);
    }

    // 检查是否所有项目都已确认
    const { ready, unconfirmedItems } = await this.isDocumentReady(documentNo);
    const unconfirmedItemsArray = unconfirmedItems || [];

    if (!ready) {
      throw new ForbiddenException(
        `Cannot complete shipment for document ${documentNo}. There are still ${unconfirmedItemsArray.length} unconfirmed items (lines: ${unconfirmedItemsArray.join(', ')}).`
      );
    }

    // 检查supervisor是否存在
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    if (!supervisor) {
      throw new NotFoundException('Supervisor not found');
    }

    // 检查supervisor级别
    if (supervisor.level !== 'supervisor' && supervisor.level !== 'admin' && supervisor.level !== 'driver') {
      throw new ForbiddenException('Only supervisors or drivers can complete shipment');
    }

    // 如果指定了司机，检查司机是否存在及权限
    let driver = null;
    if (driverId) {
      driver = await this.staffRepository.findOne({ where: { id: driverId } });
      if (!driver) {
        throw new NotFoundException(`Driver with ID ${driverId} not found`);
      }

      if (driver.level !== 'driver') {
        throw new ForbiddenException(`Staff with ID ${driverId} is not a driver`);
      }
    }

    // 标记为已出货
    main.is_shipped = true;
    main.shipped_at = new Date();
    main.shipped_by = supervisorId; // 记录主管ID

    // 如果指定了司机，则记录司机ID
    if (driverId) {
      main.driver_id = driverId;
    }

    // 预设空白签名，防止中途关闭app导致PDF排版问题
    if (!main.customer_signature) {
      main.customer_signature = ''; // 设置为空字符串
      main.signature_date = new Date().toISOString().split('T')[0]; // 设置当前日期
    }

    const savedDocument = await this.mainRepository.save(main);

    // 根据是否指派司机返回不同消息
    let message = '';
    let pdfPath = undefined;

    if (driverId) {
      message = `Document ${documentNo} has been marked as shipped by ${supervisor.username} and assigned to driver ${driver.username} for delivery`;
    } else {
      message = `Document ${documentNo} has been marked as shipped by ${supervisor.username}`;

      // 不在这里生成PDF，等待签名完成后再生成
      // 这样确保Firebase上存储的始终是包含签名的最终版本
      pdfPath = null;
    }

    // 处理 pdfPath，确保它不是空字符串
    let formattedPdfPath = undefined;
    if (pdfPath) {
      formattedPdfPath = pdfPath.replace(/\\/g, '/').replace(process.cwd().replace(/\\/g, '/'), '');
      console.log(`Formatted PDF path: ${formattedPdfPath}`);

      // 确保 formattedPdfPath 不是空字符串
      if (formattedPdfPath === '') {
        formattedPdfPath = '/PDF_Output/' + new Date().toISOString().split('T')[0] + '/' + documentNo + '.pdf';
        console.log(`Empty formatted path, using fallback: ${formattedPdfPath}`);
      }
    }

    return {
      success: true,
      message,
      document: savedDocument,
      pdfPath: formattedPdfPath
    };
  }

  // 获取司机待送达的订单列表
  async getDriverDeliveryList(driverId: number): Promise<Main[]> {
    // 检查司机是否存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // 检查级别
    if (driver.level !== 'driver') {
      throw new ForbiddenException('Only drivers can view delivery list');
    }

    // 查找由该司机处理的已出货但未送达的订单，限制数量避免性能问题
    const deliveryList = await this.mainRepository.find({
      where: {
        is_shipped: true,
        shipped_by: driverId,
        delivered: false
      },
      order: { shipped_at: 'DESC' }, // 按出货时间倒序，优先显示最新的
      // take: 50 // 限制最多返回50个订单 - 暂时注释掉
    });

    return deliveryList;
  }

  // A更加宽松的版本，司机可以看到所有已出货但未送达的订单
  async getAllPendingDeliveries(driverId: number): Promise<Main[]> {
    // 检查司机是否存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // 检查级别
    if (driver.level !== 'driver') {
      throw new ForbiddenException('Only drivers can view delivery list');
    }

    // 查找所有已出货但未送达的订单，限制数量避免性能问题
    const deliveryList = await this.mainRepository.find({
      where: {
        is_shipped: true,
        delivered: false
      },
      order: { shipped_at: 'DESC' }, // 按出货时间倒序，优先显示最新的
      // take: 50 // 限制最多返回50个订单 - 暂时注释掉
    });

    return deliveryList;
  }

  // 司机确认订单送达
  async confirmDelivery(documentNo: string, driverId: number, notes: string): Promise<{ success: boolean, message: string, document: Main }> {
    // 检查文档是否存在
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 检查司机是否存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // 检查级别
    if (driver.level !== 'driver') {
      throw new ForbiddenException('Only drivers can confirm delivery');
    }

    // 检查订单状态
    if (!main.is_shipped) {
      throw new ForbiddenException(`Document ${documentNo} has not been shipped yet`);
    }

    // 检查是否已送达
    if (main.delivered) {
      throw new BadRequestException(`Document ${documentNo} has already been delivered`);
    }

    // 检查是否是由该司机负责出货的订单
    if (main.shipped_by !== driverId) {
      throw new ForbiddenException(`This document was not assigned to you (shipped_by=${main.shipped_by}) and cannot be confirmed as delivered`);
    }

    // 标记为已送达
    main.delivered = true;
    main.delivered_at = new Date();
    main.delivered_by = driverId;
    main.delivery_notes = notes;

    const savedDocument = await this.mainRepository.save(main);

    return {
      success: true,
      message: `Document ${documentNo} has been confirmed as delivered by driver ${driver.username}`,
      document: savedDocument
    };
  }



  /**
   * @deprecated 此方法已移至ShippingService，请使用ShippingService.rejectDelivery
   * 司机拒绝分配的订单，让订单回到未出货的状态
   */
  async rejectDelivery(documentNo: string, driverId: number, reason: string) {
    console.warn('CheckService.rejectDelivery is deprecated. Use ShippingService.rejectDelivery instead.');

    // 验证司机存在
    const driver = await this.staffRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // 验证司机权限
    if (driver.level !== 'driver') {
      throw new ForbiddenException('Only drivers can reject deliveries');
    }

    // 查找对应的主单据
    const main = await this.mainRepository.findOne({
      where: { document_no: documentNo }
    });

    if (!main) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 验证单据是否已出货
    if (!main.is_shipped) {
      throw new BadRequestException('This document has not been shipped yet');
    }

    // 验证是否是分配给这个司机的订单
    if (main.driver_id != driverId) {
      throw new ForbiddenException('You are not authorized to reject this delivery');
    }

    // 重置出货状态
    main.is_shipped = false;
    main.shipped_at = null;
    main.shipped_by = null;
    main.driver_id = null;
    main.rejection_reason = reason;
    main.rejected_by = driverId;
    main.rejected_at = new Date();

    // 保存更新
    await this.mainRepository.save(main);

    // 通知相关人员
    await this.notificationService.createNotification({
      type: 'delivery_rejected',
      message: `Driver ${driver.username} has rejected the delivery of document ${documentNo}. Reason: ${reason}`,
      recipient_id: main.shipped_by || 1, // 发给原出货人，如果为空则发给ID为1的默认管理员
      sender_id: driver.id,
      document_no: documentNo,
      line: 0,
      detail_id: 0,
      reject_reason: reason
    });

    // 返回结果
    return {
      success: true,
      message: `Document ${documentNo} has been rejected by driver ${driver.username} and reset to unshipped state`,
      document: {
        document_no: main.document_no,
        is_shipped: main.is_shipped,
        rejected_by: main.rejected_by,
        rejection_reason: main.rejection_reason
      }
    };
  }

  // 设置订单紧急状态（仅限主管）
  async setUrgentStatus(documentNo: string, supervisorId: number, priority: number = 1): Promise<{ success: boolean, message: string, document: any }> {
    // 检查主管是否存在
    const supervisor = await this.staffRepository.findOne({ where: { id: supervisorId } });
    if (!supervisor) {
      throw new NotFoundException('Supervisor not found');
    }

    // 检查用户权限 - 只有 admin 级别的用户可以设置紧急状态
    if (supervisor.level !== 'admin') {
      throw new ForbiddenException('Only admin users can set urgent status');
    }

    // 查找文档
    const document = await this.mainRepository.findOne({ where: { document_no: documentNo } });
    if (!document) {
      throw new NotFoundException(`Document ${documentNo} not found`);
    }

    // 更新优先级
    document.priority = priority;
    await this.mainRepository.save(document);

    this.logger.log(`Supervisor ${supervisor.username} set document ${documentNo} priority to ${priority}`);

    return {
      success: true,
      message: `Order priority updated successfully`,
      document: {
        document_no: document.document_no,
        priority: document.priority
      }
    };
  }

  /**
   * 获取普通员工待检查列表 - 返回完整数据集，移除分页功能
   */
  async getPendingRegularChecksPaginated(
    staffId: number,
    page: number = 1,
    limit: number = 10,
    searchQuery?: string
  ): Promise<{ data: any, total: number, hasMore: boolean }> {
    console.log(`getPendingRegularChecksPaginated: staffId=${staffId}, search=${searchQuery}`);
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    if (staff.level === 'regular') {
      // 普通员工的分页逻辑 - 只返回普通检查项目
      const floor = staff.floor.replace('F', '');

      // 第一步：获取符合条件的main文档ID（使用子查询方式）
      let baseQuery = this.mainRepository
        .createQueryBuilder('main')
        .select(['main.id', 'main.created_at', 'main.priority'])
        .innerJoin('main.details', 'detail')
        .where('detail.bin_shelf_no LIKE :floorPattern', { floorPattern: `${floor}-%` })
        .andWhere('detail.is_bom_parent = :isBomParent', { isBomParent: false })
        .andWhere('detail.parent_code IS NULL') // 只要普通项目，不要BOM子项
        .andWhere('detail.staff_checked = false')
        .andWhere('detail.supervisor_checked = false')
        .andWhere('detail.have_stock = true');

      // 添加搜索条件
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = `%${searchQuery.trim()}%`;
        baseQuery = baseQuery.andWhere(`(
          detail.document_no LIKE :searchTerm OR
          main.customer_name LIKE :searchTerm OR
          detail.stock LIKE :searchTerm OR
          detail.description LIKE :searchTerm
        )`, { searchTerm });
      }

      // 获取所有符合条件的main文档
      const allMainDocs = await baseQuery
        .groupBy('main.id')
        .orderBy('COALESCE(main.priority, 0)', 'DESC')
        .addOrderBy('main.created_at', 'ASC')
        .getMany();

      if (allMainDocs.length === 0) {
        return {
          data: {
            pendingRegularCheck: [],
            total: 0
          },
          total: 0,
          hasMore: false
        };
      }

      const mainIds = allMainDocs.map(doc => doc.id);

      // 获取这些main文档对应的所有普通检查项目
      const regularDetails = await this.detailRepository.find({
        where: {
          main_id: In(mainIds),
          bin_shelf_no: Like(`${floor}-%`),
          is_bom_parent: false,
          parent_code: IsNull(), // 只要普通项目
          staff_checked: false,
          supervisor_checked: false,
          have_stock: true
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      // 转换数据
      const transformedData = regularDetails.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.main.created_at,
        priority: detail.main.priority || 0
      }));

      return {
        data: {
          pendingRegularCheck: transformedData,
          total: transformedData.length
        },
        total: transformedData.length,
        hasMore: false
      };
    } else {
      // 非普通员工返回空结果
      return {
        data: {
          pendingRegularCheck: [],
          total: 0
        },
        total: 0,
        hasMore: false
      };
    }
  }

  /**
   * 获取BOM待检查列表 - 返回完整数据集，移除分页功能
   */
  async getPendingBomChecksPaginated(
    staffId: number,
    page: number = 1,
    limit: number = 10,
    searchQuery?: string
  ): Promise<{ data: any, total: number, hasMore: boolean }> {
    console.log(`getPendingBomChecksPaginated: staffId=${staffId}, search=${searchQuery}`);
    const staff = await this.staffRepository.findOne({ where: { id: staffId } });
    if (!staff) {
      throw new NotFoundException('Staff not found');
    }

    if (staff.level === 'regular') {
      // 普通员工的BOM检查逻辑 - 返回完整数据集
      const floor = staff.floor.replace('F', '');
      console.log(`Regular staff BOM check: floor=${floor}`);

      // 获取符合条件的main文档
      let baseQuery = this.mainRepository
        .createQueryBuilder('main')
        .select(['main.id', 'main.created_at', 'main.priority'])
        .innerJoin('main.details', 'detail')
        .where('detail.bin_shelf_no LIKE :floorPattern', { floorPattern: `${floor}-%` })
        .andWhere('detail.is_bom_parent = :isBomParent', { isBomParent: false })
        .andWhere('detail.parent_code IS NOT NULL') // 只要BOM子项
        .andWhere('detail.staff_checked = false')
        .andWhere('detail.supervisor_checked = false')
        .andWhere('detail.have_stock = true');

      // 添加搜索条件
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = `%${searchQuery.trim()}%`;
        baseQuery = baseQuery.andWhere(`(
          detail.document_no LIKE :searchTerm OR
          main.customer_name LIKE :searchTerm OR
          detail.stock LIKE :searchTerm OR
          detail.description LIKE :searchTerm
        )`, { searchTerm });
      }

      // 获取所有符合条件的main文档
      const allMainDocs = await baseQuery
        .groupBy('main.id')
        .orderBy('COALESCE(main.priority, 0)', 'DESC')
        .addOrderBy('main.created_at', 'ASC')
        .getMany();

      console.log(`Total main docs with BOM items: ${allMainDocs.length}`);

      if (allMainDocs.length === 0) {
        return {
          data: {
            pendingBomCheck: [],
            total: 0
          },
          total: 0,
          hasMore: false
        };
      }

      const mainIds = allMainDocs.map(doc => doc.id);
      console.log(`All main docs: ${mainIds.length}`);

      // 获取这些main文档对应的所有BOM检查项目
      const bomDetails = await this.detailRepository.find({
        where: {
          main_id: In(mainIds),
          bin_shelf_no: Like(`${floor}-%`),
          is_bom_parent: false,
          parent_code: Not(IsNull()), // 只要BOM子项
          staff_checked: false,
          supervisor_checked: false,
          have_stock: true
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      console.log(`Found BOM details: ${bomDetails.length}`);

      // 转换数据
      const transformedData = bomDetails.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.main.created_at,
        priority: detail.main.priority || 0
      }));

      return {
        data: {
          pendingBomCheck: transformedData,
          total: transformedData.length
        },
        total: transformedData.length,
        hasMore: false
      };
    } else if (staff.level === 'staff_bom') {
      // BOM专员逻辑 - 返回完整数据集
      let bomBaseQuery = this.mainRepository
        .createQueryBuilder('main')
        .select(['main.id', 'main.created_at', 'main.priority'])
        .innerJoin('main.details', 'detail')
        .where('(detail.is_bom_parent = :isBomParent OR detail.bom_parent_id IS NOT NULL OR detail.parent_code IS NOT NULL)', { isBomParent: true })
        .andWhere('detail.is_bom_parent = false')
        .andWhere('detail.bom_specialist_checked = false')
        .andWhere('detail.have_stock = true');

      // 添加搜索条件
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = `%${searchQuery.trim()}%`;
        bomBaseQuery = bomBaseQuery.andWhere(`(
          detail.document_no LIKE :searchTerm OR
          main.customer_name LIKE :searchTerm OR
          detail.stock LIKE :searchTerm OR
          detail.description LIKE :searchTerm
        )`, { searchTerm });
      }

      // 获取所有符合条件的main文档
      const allBomMainDocs = await bomBaseQuery
        .groupBy('main.id')
        .orderBy('COALESCE(main.priority, 0)', 'DESC')
        .addOrderBy('main.created_at', 'ASC')
        .getMany();

      if (allBomMainDocs.length === 0) {
        return {
          data: {
            pendingBomCheck: [],
            total: 0
          },
          total: 0,
          hasMore: false
        };
      }

      const bomMainIds = allBomMainDocs.map(doc => doc.id);

      // 获取这些main文档对应的所有BOM检查项目
      const bomDetails = await this.detailRepository.find({
        where: {
          main_id: In(bomMainIds),
          is_bom_parent: false,
          bom_specialist_checked: false,
          have_stock: true
        },
        relations: ['main'],
        order: {
          created_at: 'ASC'
        }
      });

      // 转换数据
      const transformedData = bomDetails.map(detail => ({
        ...detail,
        document_date: detail.main.document_date,
        customer: detail.main.customer,
        customer_name: detail.main.customer_name,
        created_at: detail.main.created_at,
        priority: detail.main.priority || 0
      }));

      return {
        data: {
          pendingBomCheck: transformedData,
          total: transformedData.length
        },
        total: transformedData.length,
        hasMore: false
      };
    } else {
      // 其他角色返回空结果
      return {
        data: {
          pendingBomCheck: [],
          total: 0
        },
        total: 0,
        hasMore: false
      };
    }
  }
}