// Windows生产环境PM2配置模板
// 复制此文件为 ecosystem.production.config.js 并修改实际值

module.exports = {
  apps: [
    {
      name: 'backend-nestjs-prod',
      script: 'dist/main.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        
        // 🪟 Windows生产环境路径配置
        // 请修改为你的实际生产环境路径
        PWD: 'C:\\Production\\QueueSystem\\backend_nestjs',
        PDF_OUTPUT_DIR: 'C:\\Production\\QueueSystem\\backend_nestjs\\PDF_Output',
        
        // 🗄️ SQL Server配置 - 请修改为实际值
        SQL_SERVER_HOST: '{YOUR_SQL_SERVER_HOST}',        // 例如: 'PROD-SERVER\\SQLEXPRESS'
        SQL_SERVER_PORT: '1433',
        SQL_SERVER_USERNAME: '{YOUR_SQL_USERNAME}',       // 例如: 'sa'
        SQL_SERVER_PASSWORD: '{YOUR_SQL_PASSWORD}',       // 例如: 'YourSecurePassword123'
        SQL_SERVER_DATABASE: '{YOUR_SQL_DATABASE}',       // 例如: 'ProductionDB'
        
        // 🐘 PostgreSQL配置 - 请修改为实际值
        POSTGRES_HOST: '{YOUR_POSTGRES_HOST}',            // 例如: 'localhost' 或 'prod-pg-server'
        POSTGRES_PORT: '5432',
        POSTGRES_USERNAME: '{YOUR_POSTGRES_USERNAME}',    // 例如: 'postgres'
        POSTGRES_PASSWORD: '{YOUR_POSTGRES_PASSWORD}',    // 例如: 'YourPgPassword123'
        POSTGRES_DATABASE: '{YOUR_POSTGRES_DATABASE}',    // 例如: 'production_db'
        
        // 📧 邮件配置 - 请修改为实际值
        EMAIL_HOST: '{YOUR_EMAIL_HOST}',                  // 例如: 'smtp.gmail.com'
        EMAIL_PORT: '465',                                // 或 '587' for TLS
        EMAIL_SECURE: 'true',                             // 'true' for SSL, 'false' for TLS
        EMAIL_USER: '{YOUR_EMAIL_USER}',                  // 例如: '<EMAIL>'
        EMAIL_PASS: '{YOUR_EMAIL_PASSWORD}',              // 例如: 'YourEmailPassword123'
        EMAIL_FROM: '{YOUR_EMAIL_FROM}'                   // 例如: '<EMAIL>'
      },
      
      // 🪟 Windows生产环境工作目录
      cwd: 'C:\\Production\\QueueSystem\\backend_nestjs',
      
      // 📝 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 🔄 生产环境重启配置
      autorestart: true,
      max_restarts: 5,
      min_uptime: '30s',
      
      // 🪟 Windows特定配置
      windowsHide: true,              // 隐藏Windows控制台窗口
      
      // ⚙️ 进程管理
      kill_timeout: 10000,
      listen_timeout: 10000,
      restart_delay: 4000,            // 重启延迟
      exp_backoff_restart_delay: 100, // 指数退避重启延迟
      
      // 📁 忽略监听文件变化
      ignore_watch: [
        'node_modules',
        'logs',
        'PDF_Output',
        '.git',
        '*.tmp',
        '*.log'
      ],
      
      // 📝 日志管理
      merge_logs: true,
      time: true
    }
  ]
};

/*
🔧 Windows生产环境配置说明：

📋 必须修改的配置项：
1. PWD 和 PDF_OUTPUT_DIR - 修改为实际的生产环境路径
2. SQL_SERVER_* - 修改为实际的SQL Server连接信息
3. POSTGRES_* - 修改为实际的PostgreSQL连接信息
4. EMAIL_* - 修改为实际的邮件服务器配置

🪟 Windows特有配置：
- windowsHide: true - 隐藏控制台窗口
- 路径使用双反斜杠 (\\) 或正斜杠 (/)
- restart_delay 和 exp_backoff_restart_delay - Windows重启优化

📝 路径示例：
- 开发环境: D:\\Project\\QueueSystem\\backend_nestjs
- 生产环境: C:\\Production\\QueueSystem\\backend_nestjs
- 备用路径: E:\\Apps\\QueueSystem\\backend_nestjs

🔐 安全建议：
1. 不要将此文件提交到Git（包含密码）
2. 使用强密码
3. 考虑使用环境变量存储敏感信息
4. 定期更换密码

🚀 部署命令：
1. 复制并修改此模板: cp ecosystem.windows-production.template.js ecosystem.production.config.js
2. 编辑配置文件，替换所有 {YOUR_*} 占位符
3. 运行部署: npm run deploy:windows
4. 验证部署: npm run test:pdf

📞 如需帮助，请参考 WINDOWS_PRODUCTION_GUIDE.md
*/
