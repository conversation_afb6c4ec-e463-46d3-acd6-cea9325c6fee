import 'package:flutter/material.dart';
import 'package:queue_system/models/staff.dart';
import 'package:queue_system/services/staff_service.dart';

class StaffProvider with ChangeNotifier {
  List<Staff> _staffList = [];
  bool _isLoading = false;
  String? _error;

  List<Staff> get staffList => _staffList;
  bool get isLoading => _isLoading;
  String? get error => _error;

  final StaffService _staffService;

  StaffProvider({required String baseUrl})
      : _staffService = StaffService(baseUrl: baseUrl);

  // 加载员工列表
  Future<void> loadStaffList() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _staffList = await _staffService.getStaffList();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 修改密码
  Future<bool> changePassword(int staffId, String newPassword) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _staffService.changePassword(staffId, newPassword);
      return true;
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 更新员工状态
  Future<bool> updateStaffStatus(int staffId, String status) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _staffService.updateStaffStatus(staffId, status);
      
      // 更新本地列表中的员工状态
      final index = _staffList.indexWhere((staff) => staff.id == staffId);
      if (index != -1) {
        // 创建新的Staff对象来替换原有的
        final updatedStaff = Staff(
          id: _staffList[index].id,
          username: _staffList[index].username,
          level: _staffList[index].level,
          floor: _staffList[index].floor,
          fullName: _staffList[index].fullName,
          status: status,
          lastLogin: _staffList[index].lastLogin,
          createdAt: _staffList[index].createdAt,
        );
        _staffList[index] = updatedStaff;
      }
      
      return true;
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
