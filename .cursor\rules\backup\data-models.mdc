---
description:
globs:
alwaysApply: false
---
# 系统数据模型

## 主要实体关系

本系统包含以下主要数据实体：

### 1. 员工实体 (Staff)
- `id`: 主键
- `username`: 用户名，唯一
- `password`: 加密存储的密码
- `level`: 权限级别 ('senior', 'regular', 'staff_bom', 'driver')
- `floor`: 负责的楼层 ('1F', '2F', '3F', 'ALL')
- `full_name`: 全名
- `status`: 状态 ('active', 'inactive')
- `last_login`: 最后登录时间
- `created_at`: 创建时间

### 2. 出货单主表 (DeliveryOrderMain)
- `id`: 主键
- `document_no`: 单据编号，唯一
- `document_date`: 单据日期
- `customer`: 客户编码
- `customer_name`: 客户名称
- `customer_email`: 客户邮箱，用于发送PDF
- `customer_deliver_address`: 客户送货地址
- `customer_telephone`: 客户电话
- `customer_fax`: 客户传真
- `remarks`: 备注
- `is_shipped`: 是否已出货
- `shipped_at`: 出货时间
- `shipped_by`: 执行出货操作的员工ID
- `delivered`: 是否已送达
- `delivered_at`: 送达时间
- `delivered_by`: 确认送达的司机ID
- `delivery_notes`: 送达备注
- `transporter_code`: 运输商代码
- `transporter_name`: 运输商名称
- `salesman_code`: 销售员代码
- `issue_by`: 发行人
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 3. 出货单明细表 (DeliveryOrderDetail)
- `id`: 主键
- `main_id`: 关联主表ID
- `document_no`: 单据编号(格式可能为D00001/1)
- `line`: 行号
- `stock`: 库存编码
- `description`: 描述
- `quantity`: 数量
- `uom`: 计量单位
- `bin_shelf_no`: 货架位置编码
- `parent_code`: BOM父级编码(为NULL则非BOM项目)
- `brand_code`: 品牌代码
- `unit_price`: 单价
- `total_amount`: 总金额
- `staff_checked`: 是否已被员工检查
- `staff_checked_at`: 员工检查时间
- `staff_id`: 执行检查的员工ID
- `bom_specialist_checked`: 是否已被BOM专员检查
- `bom_specialist_checked_at`: BOM专员检查时间
- `bom_specialist_id`: 执行检查的BOM专员ID
- `supervisor_checked`: 是否已被主管检查
- `supervisor_checked_at`: 主管检查时间
- `supervisor_id`: 执行检查的主管ID
- `have_stock`: 是否有库存
- `created_at`: 创建时间

### 4. 通知实体 (Notification)
- `id`: 主键
- `type`: 通知类型 ('reject', 'complete', 'assignment', 'reminder')
- `message`: 通知内容
- `recipient_id`: 接收者ID
- `sender_id`: 发送者ID
- `document_no`: 关联的单据编号
- `line`: 关联的行号
- `detail_id`: 关联的明细表ID
- `read`: 是否已读
- `read_at`: 阅读时间
- `reject_reason`: 拒绝原因
- `created_at`: 创建时间

### 5. 库存实体 (Stock)
- `stock_code`: 库存代码，主键
- `bin_shelf_no`: 存放位置
- `uom`: 计量单位
- `current_quantity`: 当前库存数量

## PDF 和签名相关功能

系统支持以下 PDF 和签名相关功能：

### PDF 生成
- 支持两种 PDF 生成方式：
  1. 传统方式：使用 PDFKit 库生成 PDF
  2. HTML 模板方式：使用 Handlebars 模板和 Puppeteer 生成 PDF
- PDF 文件保存在服务器的 `PDF_Output/{日期}` 目录下
- 文件命名格式为 `{document_no}.pdf`

### 签名功能
- 支持在 PDF 文件中添加客户签名
- 签名以 Base64 编码格式传输
- 签名会添加到 PDF 的 "RECEIVED BY" 位置
- 签名后的 PDF 会包含签名时间信息

### 邮件发送
- 支持将生成的 PDF 文件通过邮件发送给客户
- 邮件主题和正文可自定义
- 默认使用客户邮箱作为收件人地址

## 实体关系
1. 主表(DeliveryOrderMain)与明细表(DeliveryOrderDetail)：一对多关系，通过`main_id`字段关联
2. 员工(Staff)与明细表(DeliveryOrderDetail)：多对多关系，员工可以检查多个明细，明细可以被多个不同角色的员工检查
3. 员工(Staff)与通知(Notification)：一对多关系，一个员工可以收到多个通知

## 移动应用数据模型

移动应用使用以下数据模型与后端交互：

### 1. 员工模型 (StaffModel)
```dart
class StaffModel {
  final int id;
  final String username;
  final String level; // 'senior', 'regular', 'staff_bom', 'driver'
  final String floor; // '1F', '2F', '3F', 'ALL'
  final String fullName;
  final String status; // 'active', 'inactive'
  final DateTime? lastLogin;
  final DateTime createdAt;
}
```

### 2. 出货单模型 (DeliveryOrderMainModel)
```dart
class DeliveryOrderMainModel {
  final int id;
  final String documentNo;
  final DateTime documentDate;
  final String customer;
  final String customerName;
  final String? customerEmail;
  final String remarks;
  final bool isShipped;
  final DateTime? shippedAt;
  final int? shippedBy;
  final bool delivered;
  final DateTime? deliveredAt;
  final int? deliveredBy;
  final String? deliveryNotes;
  final String? transporterName;
  final String? issueBy;
  final DateTime createdAt;
}
```

### 3. 出货单明细模型 (DeliveryOrderDetailModel)
```dart
class DeliveryOrderDetailModel {
  final int id;
  final int mainId;
  final String documentNo;
  final int line;
  final String stock;
  final String description;
  final double quantity;
  final String uom;
  final String binShelfNo;
  final String? parentCode;
  final bool staffChecked;
  final DateTime? staffCheckedAt;
  final int? staffId;
  final bool bomSpecialistChecked;
  final DateTime? bomSpecialistCheckedAt;
  final int? bomSpecialistId;
  final bool supervisorChecked;
  final DateTime? supervisorCheckedAt;
  final int? supervisorId;
  final bool haveStock;
  final DateTime createdAt;
}
```

### 4. 通知模型 (NotificationModel)
```dart
class NotificationModel {
  final int id;
  final String type;
  final String message;
  final int recipientId;
  final int? senderId;
  final String? documentNo;
  final int? line;
  final int? detailId;
  final bool read;
  final DateTime? readAt;
  final String? rejectReason;
  final DateTime createdAt;
}
```

