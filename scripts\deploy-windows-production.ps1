# Windows Production Environment Deployment Script

param(
    [Parameter(Mandatory=$false)]
    [string]$ProductionPath = ""
)

# Import common path functions
. "$PSScriptRoot\common-paths.ps1"

Write-Host "=== Windows Production Environment Deployment ===" -ForegroundColor Green

# Get project paths
$paths = Get-ProjectPaths -CustomBasePath $ProductionPath

# Validate project structure
if (!(Test-ProjectStructure -Paths $paths)) {
    exit 1
}

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "WARNING Recommend running this script as administrator for proper permissions" -ForegroundColor Yellow
}

Write-Host "`n1. Check production environment configuration" -ForegroundColor Cyan
if (!(Test-Path "ecosystem.production.config.js")) {
    Write-Host "ERROR ecosystem.production.config.js does not exist" -ForegroundColor Red
    Write-Host "Please create production config based on ecosystem.template.config.js" -ForegroundColor Yellow
    exit 1
}

# Check for placeholders in config file
$configContent = Get-Content "ecosystem.production.config.js" -Raw
$placeholders = @(
    "your-production-sql-server",
    "your-prod-username",
    "your-prod-password",
    "your-production-postgres",
    "your-prod-pg-username",
    "your-prod-pg-password",
    "your-production-email-host",
    "<EMAIL>",
    "{YOUR_",
    "your-prod-email-password"
)

$foundPlaceholders = @()
# 只检查env配置部分，忽略注释和说明文档
$envSection = $configContent -split "env:\s*\{" | Select-Object -Skip 1 | Select-Object -First 1
if ($envSection) {
    $envSection = $envSection -split "\}" | Select-Object -First 1
    foreach ($placeholder in $placeholders) {
        if ($envSection -like "*$placeholder*") {
            $foundPlaceholders += $placeholder
        }
    }
} else {
    Write-Host "WARNING: Could not parse env section" -ForegroundColor Yellow
}

if ($foundPlaceholders.Count -gt 0) {
    Write-Host "ERROR Configuration file still contains placeholders" -ForegroundColor Red
    Write-Host "Placeholders that need to be replaced:" -ForegroundColor Yellow
    foreach ($placeholder in $foundPlaceholders) {
        Write-Host "  - $placeholder" -ForegroundColor Gray
    }
    exit 1
}

Write-Host "OK Production environment configuration check passed" -ForegroundColor Green

Write-Host "`n2. Initialize production directories" -ForegroundColor Cyan
Initialize-ProjectDirectories -Paths $paths

Write-Host "`n3. Check Node.js and PM2" -ForegroundColor Cyan
try {
    $nodeVersion = node --version
    Write-Host "OK Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR Node.js not installed or not in PATH" -ForegroundColor Red
    exit 1
}

try {
    $pm2Version = pm2 --version
    Write-Host "OK PM2 version: $pm2Version" -ForegroundColor Green
} catch {
    Write-Host "PM2 not installed, installing..." -ForegroundColor Yellow
    npm install -g pm2
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK PM2 installed successfully" -ForegroundColor Green
    } else {
        Write-Host "ERROR PM2 installation failed" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n4. Build project" -ForegroundColor Cyan
if (!(Test-Path "dist")) {
    Write-Host "Starting project build..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK Project build successful" -ForegroundColor Green
    } else {
        Write-Host "ERROR Project build failed" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "OK dist directory exists" -ForegroundColor Green
    $rebuild = Read-Host "Rebuild project? (y/N)"
    if ($rebuild -eq "y" -or $rebuild -eq "Y") {
        npm run build
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERROR Project build failed" -ForegroundColor Red
            exit 1
        }
    }
}

# Directory creation is now handled by Initialize-ProjectDirectories above

Write-Host "`n5. Check Windows Firewall" -ForegroundColor Cyan
try {
    $firewallRule = Get-NetFirewallRule -DisplayName "Node.js Server Port 3000" -ErrorAction SilentlyContinue
    if ($firewallRule) {
        Write-Host "OK Firewall rule exists" -ForegroundColor Green
    } else {
        Write-Host "Firewall rule not found. Create it? (y/N)" -ForegroundColor Yellow
        $createRule = Read-Host
        if ($createRule -eq "y" -or $createRule -eq "Y") {
            if ($isAdmin) {
                New-NetFirewallRule -DisplayName "Node.js Server Port 3000" -Direction Inbound -Protocol TCP -LocalPort 3000 -Action Allow
                Write-Host "OK Firewall rule created" -ForegroundColor Green
            } else {
                Write-Host "WARNING Need admin rights to create firewall rule" -ForegroundColor Yellow
            }
        }
    }
} catch {
    Write-Host "WARNING Cannot check firewall rules, please verify port 3000 is open" -ForegroundColor Yellow
}

Write-Host "`n6. Stop existing production process" -ForegroundColor Cyan
pm2 stop backend-nestjs-prod 2>$null
pm2 delete backend-nestjs-prod 2>$null
Write-Host "OK Cleanup completed" -ForegroundColor Green

Write-Host "`n7. Start production environment" -ForegroundColor Cyan

# 检查配置文件中的工作目录是否与当前目录匹配
if ($configContent -match "cwd:\s*'([^']+)'") {
    $configCwd = $matches[1] -replace '\\\\', '\'
    $currentPath = (Get-Location).Path

    if ($configCwd -ne $currentPath) {
        Write-Host "WARNING: Configuration mismatch detected!" -ForegroundColor Yellow
        Write-Host "  Config file cwd: $configCwd" -ForegroundColor Gray
        Write-Host "  Current directory: $currentPath" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Options:" -ForegroundColor Cyan
        Write-Host "  1. Run this script from the production directory: $configCwd" -ForegroundColor Gray
        Write-Host "  2. Update ecosystem.production.config.js to use current path" -ForegroundColor Gray
        Write-Host "  3. Continue anyway (may cause permission issues)" -ForegroundColor Gray
        Write-Host ""
        $choice = Read-Host "Continue anyway? (y/N)"
        if ($choice -ne "y" -and $choice -ne "Y") {
            Write-Host "Deployment cancelled" -ForegroundColor Yellow
            exit 1
        }
    }
}

pm2 start ecosystem.production.config.js

if ($LASTEXITCODE -eq 0) {
    Write-Host "OK Production environment started successfully!" -ForegroundColor Green

    # Wait a few seconds for the process to fully start
    Start-Sleep -Seconds 5

    # Show status
    pm2 status

    Write-Host "`n=== Deployment Complete ===" -ForegroundColor Green
    Write-Host "Production environment info:" -ForegroundColor Cyan
    Write-Host "  Process name: backend-nestjs-prod" -ForegroundColor Gray
    Write-Host "  Working directory: $($paths.ProjectRoot)" -ForegroundColor Gray
    Write-Host "  PDF Output: $($paths.PDFOutput)" -ForegroundColor Gray
    Write-Host "  Logs: $($paths.Logs)" -ForegroundColor Gray

    # 从配置文件中提取端口号
    $port = "3000"  # 默认端口
    if ($configContent -match "PORT:\s*(\d+)") {
        $port = $matches[1]
    }
    Write-Host "  Access URL: http://localhost:$port" -ForegroundColor Gray
    Write-Host "  API docs: http://localhost:$port/api-docs" -ForegroundColor Gray

    Write-Host "`nManagement commands:" -ForegroundColor Yellow
    Write-Host "  Check status: pm2 status" -ForegroundColor Gray
    Write-Host "  View logs: pm2 logs backend-nestjs-prod" -ForegroundColor Gray
    Write-Host "  Restart service: pm2 restart backend-nestjs-prod" -ForegroundColor Gray
    Write-Host "  Stop service: pm2 stop backend-nestjs-prod" -ForegroundColor Gray

    Write-Host "`nWindows production environment deployed successfully!" -ForegroundColor Green

} else {
    Write-Host "ERROR Production environment startup failed" -ForegroundColor Red
    Write-Host "Please check configuration files and logs" -ForegroundColor Yellow
    exit 1
}
