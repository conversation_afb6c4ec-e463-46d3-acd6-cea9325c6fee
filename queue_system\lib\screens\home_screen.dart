import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/locale_provider.dart';
import 'package:queue_system/screens/login_screen.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/models/shipping_document.dart';
// import 'package:queue_system/widgets/bom_review_tab.dart'; // 已注释，使用Queue页面替代
import 'package:queue_system/widgets/delivery_tab.dart';
import 'package:queue_system/providers/notification_provider.dart';
import 'package:queue_system/providers/stock_return_provider.dart';
import 'package:queue_system/providers/stock_return_tracking_provider.dart';
import 'package:queue_system/widgets/notification_icon.dart';
import 'package:queue_system/widgets/stock_return_icon.dart';
import 'package:queue_system/widgets/to_check_tab.dart';

import 'package:queue_system/widgets/to_delivery_tab.dart';
import 'package:queue_system/widgets/to_ship_tab.dart';
import 'package:queue_system/widgets/user_info_bar.dart';
import 'package:queue_system/widgets/queue_tab.dart';
import 'package:queue_system/screens/pdf_list_screen.dart';
import 'package:queue_system/screens/staff_management_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // 不需要服务实例

  // 添加刷新键来控制 FutureBuilder 的重新创建
  int _refreshKey = 0;

  @override
  void initState() {
    super.initState();
    // 延迟执行，确保widget已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final checkListProvider =
          Provider.of<CheckListProvider>(context, listen: false);
      final notificationProvider =
          Provider.of<NotificationProvider>(context, listen: false);
      final stockReturnProvider =
          Provider.of<StockReturnProvider>(context, listen: false);
      final stockReturnTrackingProvider =
          Provider.of<StockReturnTrackingProvider>(context, listen: false);

      if (authProvider.currentStaff != null) {
        // 启用分页模式
        // 分页模式已移除，无需启用

        // 加载员工的检查列表，使用分页版本
        checkListProvider
            .loadCheckListPaginated(
          authProvider.currentStaff!.id,
          currentStaff: authProvider.currentStaff,
          isRefresh: true,
        )
            .then((_) {
          if (!mounted) return;

          if (checkListProvider.error != null) {
            // 显示错误消息
            String errorMessage = checkListProvider.error!;

            // 清理错误消息中的多余内容
            if (errorMessage.startsWith("Exception: ")) {
              errorMessage = errorMessage.substring("Exception: ".length);
            }

            // 显示错误对话框，更明显地提示用户
            if (mounted) {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(context.t('loading_failed')),
                  content: Text(errorMessage),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(context.t('confirm')),
                    ),
                  ],
                ),
              );

              // 同时在底部显示错误消息
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('${context.t('loading_failed')}: $errorMessage'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 5),
                ),
              );
            }
          }
        }).catchError((e) {
          if (mounted) {
            // 捕获可能的未处理错误
            String errorMessage = e.toString();
            if (errorMessage.startsWith("Exception: ")) {
              errorMessage = errorMessage.substring("Exception: ".length);
            }

            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text(context.t('loading_failed')),
                content: Text(errorMessage),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(context.t('confirm')),
                  ),
                ],
              ),
            );

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${context.t('loading_failed')}: $errorMessage'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        });

        // 加载通知
        notificationProvider.fetchNotifications();

        // 加载库存退回数据（仅对普通员工）
        if (authProvider.currentStaff!.level == 'regular') {
          stockReturnProvider.loadPendingReturns();
          stockReturnTrackingProvider
              .loadPendingTracking(authProvider.currentStaff!.id);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final staff = authProvider.currentStaff;

    return Scaffold(
      appBar: AppBar(
        title: Text('${context.t('warehouse_system')} - ${staff?.floor ?? ""}'),
        actions: [
          // 语言切换按钮
          IconButton(
            icon: const Icon(Icons.language),
            tooltip: context.t('switch_language'),
            onPressed: () {
              final localeProvider =
                  Provider.of<LocaleProvider>(context, listen: false);
              localeProvider.toggleLocale();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (staff != null) {
                // 直接刷新数据
                final checkListProvider =
                    Provider.of<CheckListProvider>(context, listen: false);
                checkListProvider.loadCheckListPaginated(
                  staff.id,
                  currentStaff: staff,
                  isRefresh: true,
                );

                // 刷新通知
                final notificationProvider =
                    Provider.of<NotificationProvider>(context, listen: false);
                notificationProvider.fetchNotifications();

                // 刷新库存退回数据（仅对普通员工）
                if (staff.level == 'regular') {
                  final stockReturnProvider =
                      Provider.of<StockReturnProvider>(context, listen: false);
                  final stockReturnTrackingProvider =
                      Provider.of<StockReturnTrackingProvider>(context,
                          listen: false);
                  stockReturnProvider.loadPendingReturns();
                  stockReturnTrackingProvider.loadPendingTracking(staff.id);
                }

                // 刷新 FutureBuilder 的计数
                setState(() {
                  _refreshKey++;
                });
              }
            },
            tooltip: context.t('refresh'),
          ),
          // 通知图标
          const NotificationIcon(),

          // 库存退回图标 - 仅对普通员工显示
          Consumer<AuthProvider>(
            builder: (context, authProvider, _) {
              final staff = authProvider.currentStaff;
              final bool canViewStockReturn =
                  staff != null && staff.level == 'regular';

              return canViewStockReturn
                  ? const StockReturnIcon()
                  : const SizedBox.shrink(); // 对其他权限用户隐藏
            },
          ),
          // 员工管理按钮 - 仅对管理员显示
          Consumer<AuthProvider>(
            builder: (context, authProvider, _) {
              final staff = authProvider.currentStaff;
              final bool isAdmin = staff != null && staff.level == 'admin';

              return isAdmin
                  ? IconButton(
                      icon: const Icon(Icons.people),
                      onPressed: () => _navigateToStaffManagement(context),
                      tooltip: context.t('staff_management'),
                    )
                  : const SizedBox.shrink(); // 对其他权限用户隐藏
            },
          ),
          // 查看历史PDF按钮 - 仅对主管和司机显示
          Consumer<AuthProvider>(
            builder: (context, authProvider, _) {
              final staff = authProvider.currentStaff;
              final bool canViewPdf = staff != null &&
                  (staff.level == 'supervisor' ||
                      staff.level == 'admin' ||
                      staff.level == 'driver');

              return canViewPdf
                  ? IconButton(
                      icon: const Icon(Icons.picture_as_pdf),
                      onPressed: () => _navigateToPdfList(context),
                      tooltip: context.t('view_pdf_history'),
                    )
                  : const SizedBox.shrink(); // 对其他权限用户隐藏
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _showLogoutConfirmation(context),
            tooltip: context.t('logout'),
          ),
        ],
      ),
      body: Column(
        children: [
          const UserInfoBar(),
          Expanded(
            child: _buildTabView(context),
          ),
        ],
      ),
    );
  }

  Widget _buildTabView(BuildContext context) {
    return Consumer2<AuthProvider, CheckListProvider>(
      builder: (context, authProvider, checkListProvider, _) {
        final staff = authProvider.currentStaff;

        if (staff == null) {
          return Center(
            child: Text(context.t('login_first')),
          );
        }

        if (checkListProvider.isLoading && checkListProvider.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (checkListProvider.error != null && checkListProvider.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.signal_wifi_off,
                  color: Colors.grey,
                  size: 60,
                ),
                const SizedBox(height: 16),
                Text(
                  context.t('connection_failed'),
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(context.t('unable_connect_server')),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _refreshData(context, staff.id),
                  icon: const Icon(Icons.refresh),
                  label: Text(context.t('retry')),
                ),
              ],
            ),
          );
        }

        // 即使没有待检查的项目，也应该显示标签页结构
        // 移除了原来的代码块，允许标签页正常显示

        final isBomSpecialist = staff.level == 'staff_bom';
        final isSupervisor =
            staff.level == 'supervisor' || staff.level == 'admin';
        final isDriver = staff.level == 'driver';

        // 确定tab数量
        int tabCount;
        if (isBomSpecialist) {
          tabCount = 1; // BOM专员：只有queue页面（移除bom_review）
        } else if (isSupervisor) {
          tabCount = 3; // 主管：waiting(整合), to_ship, delivery
        } else if (isDriver) {
          tabCount = 2; // 司机：to_ship, to_delivery
        } else {
          tabCount = 2; // 普通员工：to_check, to_check_bom（移除to_confirm）
        }

        return DefaultTabController(
          length: tabCount,
          child: Column(
            children: [
              Material(
                color: Colors.grey.shade100,
                child: Container(
                  width: MediaQuery.of(context).size.width, // 确保容器占据整个屏幕宽度
                  margin: EdgeInsets.zero, // 确保没有外边距
                  padding: EdgeInsets.zero, // 确保没有内边距
                  alignment: Alignment.centerLeft, // 确保内容左对齐
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.grey.shade300,
                        width: 1.0,
                      ),
                    ),
                  ),
                  child: MediaQuery(
                    // 移除系统边距的影响
                    data: MediaQuery.of(context).removePadding(
                      removeLeft: true,
                      removeRight: true,
                    ),
                    child: TabBar(
                      isScrollable: true, // 添加滚动属性，使标签可以左右滑动
                      padding: EdgeInsets.zero, // 移除默认的内边距，确保标签从最左侧开始
                      labelPadding: EdgeInsets.zero, // 完全移除标签内边距，改为在Tab中控制
                      indicatorPadding: EdgeInsets.zero, // 移除指示器内边距
                      // tabAlignment: TabAlignment.start, // 在较旧版本的Flutter中可能不支持
                      labelColor: Theme.of(context).primaryColor,
                      unselectedLabelColor: Colors.grey.shade700,
                      indicatorWeight: 3,
                      indicatorColor: Theme.of(context).primaryColor,
                      tabs: [
                        if (!isDriver)
                          _buildTabWithCount(
                            isBomSpecialist || isSupervisor
                                ? context.t('queue')
                                : context.t('to_check'),
                            isBomSpecialist || isSupervisor
                                ? checkListProvider.getWaitingDocumentCount()
                                : checkListProvider
                                    .getPendingRegularCheckDocumentCount(),
                            isFirstTab: true, // 标记为第一个标签，移除左侧内边距
                          ),
                        // 普通员工的BOM检查标签
                        if (!isDriver && !isBomSpecialist && !isSupervisor)
                          _buildTabWithCount(
                            context.t('to_check_bom'),
                            checkListProvider.getPendingBomCheckDocumentCount(),
                          ),
                        // BOM专员的BOM Review标签已移除，只使用Queue页面

                        if (isSupervisor)
                          FutureBuilder<int>(
                            key: ValueKey('to_ship_$_refreshKey'),
                            future: _getToShipCount(staff.id),
                            builder: (context, snapshot) {
                              return _buildTabWithCount(
                                context.t('to_ship'),
                                snapshot.data ?? 0,
                              );
                            },
                          ),
                        if (isSupervisor)
                          FutureBuilder<int>(
                            key: ValueKey('delivery_$_refreshKey'),
                            future: _getSupervisorDeliveryCount(staff.id),
                            builder: (context, snapshot) {
                              return _buildTabWithCount(
                                context.t('delivery'),
                                snapshot.data ?? 0,
                              );
                            },
                          ),
                        if (isDriver)
                          FutureBuilder<int>(
                            key: ValueKey('driver_to_ship_$_refreshKey'),
                            future: _getToShipCount(staff.id),
                            builder: (context, snapshot) {
                              return _buildTabWithCount(
                                context.t('to_ship'),
                                snapshot.data ?? 0,
                                isFirstTab: true, // 标记为第一个标签，移除左侧内边距
                              );
                            },
                          ),
                        if (isDriver)
                          FutureBuilder<int>(
                            key: ValueKey('driver_delivery_$_refreshKey'),
                            future: _getDriverDeliveryCount(staff.id),
                            builder: (context, snapshot) {
                              return _buildTabWithCount(
                                context.t('to_delivery'),
                                snapshot.data ?? 0,
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: TabBarView(
                  children: [
                    // 第一个标签: 待检查/等待检查 (对司机隐藏)
                    if (!isDriver)
                      isBomSpecialist || isSupervisor
                          ? QueueTab(
                              items: checkListProvider.waitingList,
                              staffId: staff.id,
                              onConfirmAction: _confirmActionDialog,
                            )
                          : ToCheckTab(
                              items: checkListProvider.pendingRegularCheck,
                              staffId: staff.id,
                              isSenior: isSupervisor,
                              isBomSpecialist: isBomSpecialist,
                              onConfirmAction: _confirmActionDialog,
                            ),

                    // 普通员工的BOM检查标签
                    if (!isDriver && !isBomSpecialist && !isSupervisor)
                      ToCheckTab(
                        items: checkListProvider.pendingBomCheck,
                        staffId: staff.id,
                        isSenior: isSupervisor,
                        isBomSpecialist: isBomSpecialist,
                        onConfirmAction: _confirmActionDialog,
                        isBomTab: true, // 标记为BOM标签页
                      ),

                    // 第二个标签: BOM专员审核 (已注释，使用Queue页面替代)
                    // if (isBomSpecialist)
                    //   BomReviewTab(
                    //     items: checkListProvider
                    //         .pendingBomCheck, // 修改为pendingBomCheck
                    //     staffId: staff.id,
                    //     isSenior: true,
                    //     onConfirmAction: _confirmActionDialog,
                    //   ),

                    // 主管专用标签: 待出货
                    if (isSupervisor)
                      ToShipTab(
                        staffId: staff.id,
                        onConfirmAction: _confirmActionDialog,
                        onDataChanged: () {
                          // 当数据变化时，刷新标签页计数
                          setState(() {
                            _refreshKey++;
                          });
                        },
                      ),

                    // 主管专用标签: 送货状态
                    if (isSupervisor)
                      DeliveryTab(
                        staffId: staff.id,
                        isDriver: false,
                      ),

                    // 司机专用标签: 待出货
                    if (isDriver)
                      ToShipTab(
                        staffId: staff.id,
                        onConfirmAction: _confirmActionDialog,
                        onDataChanged: () {
                          // 当数据变化时，刷新标签页计数
                          setState(() {
                            _refreshKey++;
                          });
                        },
                      ),

                    // 司机专用标签: 待送达
                    if (isDriver)
                      ToDeliveryTab(
                        driverId: staff.id,
                        onDataChanged: () {
                          // 当数据变化时，刷新标签页计数
                          setState(() {
                            _refreshKey++;
                          });
                        },
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 简化版的确认对话框
  void _confirmActionDialog(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) {
    // 保存需要的本地变量
    final cancelText = context.t('cancel');
    final confirmText = context.t('confirm');

    // 获取当前staff信息
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentStaff = authProvider.currentStaff;
    final staffId = currentStaff?.id ?? 0;

    // 使用同步方法显示确认对话框
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed != true || !mounted) return;

      // 执行操作
      _executeAction(action, staffId);
    });
  }

  // 辅助方法：执行操作并处理结果
  void _executeAction(Future<void> Function() action, int staffId) {
    // 保存需要的本地变量
    final operationSuccessText = context.t('operation_success');
    final operationFailedText = context.t('operation_failed');
    final failedText = context.t('failed');
    final confirmText = context.t('confirm');

    // 显示加载指示器
    if (!mounted) return;

    // 创建一个变量来跟踪对话框是否已经关闭
    bool isDialogOpen = true;

    // 使用同步方法显示加载指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          content: Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );

    // 使用 try-catch-finally 确保对话框总是被关闭
    try {
      // 执行操作
      action().then((_) {
        // 关闭加载指示器
        if (mounted && isDialogOpen) {
          Navigator.of(context).pop();
          isDialogOpen = false;
        }

        if (!mounted) return;

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(operationSuccessText),
            backgroundColor: Colors.green,
          ),
        );

        // 延迟刷新数据，避免可能的竞态条件
        Future.delayed(const Duration(milliseconds: 300), () {
          // 刷新数据
          if (staffId > 0 && mounted) {
            // 使用Provider直接刷新数据
            final checkListProvider =
                Provider.of<CheckListProvider>(context, listen: false);
            final authProvider =
                Provider.of<AuthProvider>(context, listen: false);

            checkListProvider.loadCheckListPaginated(
              staffId,
              currentStaff: authProvider.currentStaff,
              isRefresh: true,
            );

            // 刷新 FutureBuilder 的计数
            setState(() {
              _refreshKey++;
            });
          }
        });
      }).catchError((e) {
        // 关闭加载指示器
        if (mounted && isDialogOpen) {
          Navigator.of(context).pop();
          isDialogOpen = false;
        }

        if (!mounted) return;

        // 显示错误消息
        String errorMessage = e.toString();

        // 清理错误消息中的多余内容
        if (errorMessage.startsWith("Exception: ")) {
          errorMessage = errorMessage.substring("Exception: ".length);
        }

        // 特殊处理error_reject_check错误
        if (errorMessage == 'error_reject_check') {
          debugPrint("_executeAction - 忽略error_reject_check错误，将其视为成功");

          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(operationSuccessText),
              backgroundColor: Colors.green,
            ),
          );

          return;
        }

        // 特殊处理document已被reject的错误
        if (errorMessage.toLowerCase().contains('has been rejected') ||
            errorMessage.toLowerCase().contains('已被拒绝') ||
            errorMessage.toLowerCase().contains('已重置') ||
            errorMessage.toLowerCase().contains('reset') ||
            errorMessage.toLowerCase().contains('success')) {
          debugPrint("_executeAction - 忽略document已被reject错误，将其视为成功");

          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(operationSuccessText),
              backgroundColor: Colors.green,
            ),
          );

          return;
        }

        if (!mounted) return;

        // 显示错误对话框
        showDialog(
          context: context,
          builder: (dialogContext) => AlertDialog(
            title: Text(operationFailedText),
            content: Text(errorMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(confirmText),
              ),
            ],
          ),
        );

        if (!mounted) return;

        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$failedText: $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );

        // 延迟刷新数据，避免可能的竞态条件
        Future.delayed(const Duration(milliseconds: 300), () {
          // 刷新数据
          if (staffId > 0 && mounted) {
            // 使用Provider直接刷新数据
            final checkListProvider =
                Provider.of<CheckListProvider>(context, listen: false);
            final authProvider =
                Provider.of<AuthProvider>(context, listen: false);

            checkListProvider.loadCheckListPaginated(
              staffId,
              currentStaff: authProvider.currentStaff,
              isRefresh: true,
            );

            // 刷新 FutureBuilder 的计数
            setState(() {
              _refreshKey++;
            });
          }
        });
      });
    } catch (e) {
      // 确保对话框关闭
      if (mounted && isDialogOpen) {
        Navigator.of(context).pop();
        isDialogOpen = false;
      }

      // 重新抛出异常，让上层处理
      rethrow;
    }
  }

  // 构建带有数据计数的标签
  Widget _buildTabWithCount(String title, int count,
      {bool isFirstTab = false}) {
    return Tab(
      // Tab 组件没有 padding 参数，我们在内部控制内边距
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 获取屏幕宽度
          final screenWidth = MediaQuery.of(context).size.width;
          // 根据屏幕宽度调整标签样式
          final isSmallScreen = screenWidth < 360;

          // 使用Container而不是Padding，以便更精确地控制边距
          return Container(
            // 水平内边距，第一个标签左侧完全无内边距，其他标签正常内边距
            padding: isFirstTab
                ? const EdgeInsets.only(right: 12)
                : const EdgeInsets.symmetric(horizontal: 12),
            // 确保第一个标签完全贴合左侧
            margin: EdgeInsets.zero,
            alignment: isFirstTab ? Alignment.centerLeft : Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min, // 使Row只占用所需的空间
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 在小屏幕上使用更紧凑的布局
                Text(
                  title,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 13 : 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(width: isSmallScreen ? 2 : 4),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 4 : 6,
                      vertical: isSmallScreen ? 1 : 2),
                  decoration: BoxDecoration(
                    color:
                        count > 0 ? Colors.blue.shade100 : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '$count',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 10 : 12,
                      color: count > 0
                          ? Colors.blue.shade800
                          : Colors.grey.shade700,
                      fontWeight:
                          count > 0 ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // 获取待出货文档数量
  Future<int> _getToShipCount(int staffId) async {
    try {
      final checkService = CheckService(baseUrl: AppConfig.baseUrl);
      final documents = await checkService.getReadyDocuments(staffId);
      return documents.length;
    } catch (e) {
      debugPrint("获取待出货文档数量失败: $e");
      return 0;
    }
  }

  // 获取主管的送货状态文档数量
  Future<int> _getSupervisorDeliveryCount(int staffId) async {
    try {
      final checkService = CheckService(baseUrl: AppConfig.baseUrl);
      final result = await checkService.getSupervisorShipments(staffId,
          page: 1, limit: 1000);
      final documents = result['data'] as List<ShippingDocument>;

      // 过滤掉未指派司机的订单，只计算已指派司机的订单数量
      final filteredDocuments = documents
          .where((doc) =>
              doc.driverId != null && doc.driverId.toString().isNotEmpty)
          .toList();

      return filteredDocuments.length;
    } catch (e) {
      debugPrint("获取送货状态文档数量失败: $e");
      return 0;
    }
  }

  // 获取司机的待送达文档数量
  Future<int> _getDriverDeliveryCount(int driverId) async {
    try {
      final checkService = CheckService(baseUrl: AppConfig.baseUrl);
      final documents = await checkService.getDriverDeliveryList(driverId);
      return documents.length;
    } catch (e) {
      debugPrint("获取司机待送达文档数量失败: $e");
      return 0;
    }
  }

  // 刷新数据
  void _refreshData(BuildContext context, int staffId) {
    if (staffId <= 0 || !mounted) return;

    // 显示加载指示器
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.t('loading')),
        duration: const Duration(seconds: 1),
      ),
    );

    // 使用Provider直接刷新数据
    final checkListProvider =
        Provider.of<CheckListProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    checkListProvider.loadCheckListPaginated(
      staffId,
      currentStaff: authProvider.currentStaff,
      isRefresh: true,
    );

    // 触发UI刷新，更新标签页计数
    if (mounted) {
      setState(() {
        _refreshKey++;
      });
    }
  }

  // 显示退出确认对话框
  Future<void> _showLogoutConfirmation(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('logout_confirmation')),
        content: Text(context.t('logout_message')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(context.t('confirm')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // 退出到登录页面
      if (!context.mounted) return;

      // 登出处理
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final checkListProvider =
          Provider.of<CheckListProvider>(context, listen: false);

      // 清理所有数据和状态
      checkListProvider.clearAllData();
      authProvider.logout();

      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false, // 清除所有路由历史
      );
    }
  }

  // 导航到PDF列表页面
  void _navigateToPdfList(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PdfListScreen()),
    );
  }

  // 导航到员工管理页面
  void _navigateToStaffManagement(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const StaffManagementScreen()),
    );
  }
}
