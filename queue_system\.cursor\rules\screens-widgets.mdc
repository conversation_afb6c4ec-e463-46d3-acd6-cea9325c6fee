---
description:
globs:
alwaysApply: false
---
# 应用屏幕和组件

## 主要屏幕

应用包含以下主要屏幕:

### 1. 登录屏幕 (LoginScreen)

```dart
class LoginScreen extends StatefulWidget {
  static const routeName = '/login';
  
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final appLocalizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(appLocalizations.loginTitle),
        actions: [
          _buildLanguageSelector(context),
        ],
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 400),
          padding: EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 用户名和密码输入框
                // 登录按钮
                // 错误信息显示
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildLanguageSelector(BuildContext context) {
    // 构建语言选择器...
  }
}
```

### 2. 检查列表屏幕 (CheckListScreen)

```dart
class CheckListScreen extends StatefulWidget {
  static const routeName = '/check-list';
  
  @override
  _CheckListScreenState createState() => _CheckListScreenState();
}

class _CheckListScreenState extends State<CheckListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 加载检查列表数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CheckListProvider>(context, listen: false).fetchCheckList();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    final authProvider = Provider.of<AuthProvider>(context);
    final checkListProvider = Provider.of<CheckListProvider>(context);
    
    // 构建界面，包含TabBar和TabView...
  }
  
  // 构建不同类型的检查项Tab...
}
```

### 3. 出货屏幕 (ShippingScreen)

```dart
class ShippingScreen extends StatefulWidget {
  static const routeName = '/shipping';
  
  @override
  _ShippingScreenState createState() => _ShippingScreenState();
}

class _ShippingScreenState extends State<ShippingScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    // 根据用户角色决定Tab数量
    final tabCount = authProvider.credentials?.level == 'drive' ? 2 : 1;
    _tabController = TabController(length: tabCount, vsync: this);
    
    // 加载出货列表数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ShippingProvider>(context, listen: false).fetchShipments();
      
      if (authProvider.credentials?.level == 'drive') {
        Provider.of<ShippingProvider>(context, listen: false).fetchDeliveries();
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    // 构建界面，包含TabBar和TabView...
  }
  
  // 构建不同类型的出货Tab...
}
```

### 4. 通知屏幕 (NotificationsScreen)

```dart
class NotificationsScreen extends StatefulWidget {
  static const routeName = '/notifications';
  
  @override
  _NotificationsScreenState createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  void initState() {
    super.initState();
    
    // 加载通知数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationProvider>(context, listen: false).fetchNotifications();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    // 构建通知列表界面...
  }
}
```

### 5. 主页面 (HomeScreen)

```dart
class HomeScreen extends StatefulWidget {
  static const routeName = '/home';
  
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  
  final List<Widget> _screens = [
    CheckListScreen(),
    ShippingScreen(),
    NotificationsScreen(),
    ProfileScreen(),
  ];
  
  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    final authProvider = Provider.of<AuthProvider>(context);
    
    // 根据用户角色决定可用的功能选项
    // 构建底部导航栏界面...
  }
}
```

## 主要组件

### 1. 通用加载指示器 (LoadingIndicator)

```dart
class LoadingIndicator extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(),
    );
  }
}
```

### 2. 通用错误显示 (ErrorDisplay)

```dart
class ErrorDisplay extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;
  
  const ErrorDisplay({
    required this.error,
    this.onRetry,
  });
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 48),
          SizedBox(height: 16),
          Text(error, textAlign: TextAlign.center),
          if (onRetry != null) ...[
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: Text(AppLocalizations.of(context)!.retry),
            ),
          ],
        ],
      ),
    );
  }
}
```

### 3. 检查项卡片 (CheckItemCard)

```dart
class CheckItemCard extends StatelessWidget {
  final CheckItemModel item;
  final VoidCallback? onCheck;
  
  const CheckItemCard({
    required this.item,
    this.onCheck,
  });
  
  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单号和行号
            // 库存编码和描述
            // 数量和位置
            // BOM信息
            // 检查按钮
          ],
        ),
      ),
    );
  }
}
```

### 4. 订单卡片 (DocumentCard)

```dart
class DocumentCard extends StatelessWidget {
  final DeliveryOrderMainModel document;
  final VoidCallback? onShip;
  final VoidCallback? onDeliver;
  
  const DocumentCard({
    required this.document,
    this.onShip,
    this.onDeliver,
  });
  
  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单号和日期
            // 客户信息
            // 出货和送达状态
            // 操作按钮
          ],
        ),
      ),
    );
  }
}
```

### 5. 通知卡片 (NotificationCard)

```dart
class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onMarkAsRead;
  
  const NotificationCard({
    required this.notification,
    this.onMarkAsRead,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: notification.read ? null : Colors.blue.shade50,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 通知标题和时间
            // 通知内容
            // 相关单据信息
            // 标记为已读按钮
          ],
        ),
      ),
    );
  }
}
```

### 6. 底部导航栏 (BottomNavBar)

```dart
class BottomNavBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;
  
  const BottomNavBar({
    required this.selectedIndex,
    required this.onItemSelected,
  });
  
  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    final authProvider = Provider.of<AuthProvider>(context);
    
    return BottomNavigationBar(
      currentIndex: selectedIndex,
      onTap: onItemSelected,
      type: BottomNavigationBarType.fixed,
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.checklist),
          label: appLocalizations.checkList,
        ),
        if (authProvider.credentials?.level == 'supervisor' ||
            authProvider.credentials?.level == 'admin' ||
            authProvider.credentials?.level == 'driver')
          BottomNavigationBarItem(
            icon: Icon(Icons.local_shipping),
            label: appLocalizations.shipping,
          ),
        BottomNavigationBarItem(
          icon: Stack(
            children: [
              Icon(Icons.notifications),
              // 未读通知数量徽章
            ],
          ),
          label: appLocalizations.notifications,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: appLocalizations.profile,
        ),
      ],
    );
  }
}
```

### 7. 对话框组件 (模态框)

```dart
class ConfirmDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  
  const ConfirmDialog({
    required this.title,
    required this.message,
    required this.confirmText,
    required this.cancelText,
    required this.onConfirm,
  });
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(cancelText),
        ),
        TextButton(
          onPressed: () {
            onConfirm();
            Navigator.of(context).pop();
          },
          child: Text(confirmText),
        ),
      ],
    );
  }
  
  static Future<bool> show(
    BuildContext context, {
    required String title,
    required String message,
    required String confirmText,
    required String cancelText,
    required VoidCallback onConfirm,
  }) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
      ),
    ) ?? false;
  }
}
```
