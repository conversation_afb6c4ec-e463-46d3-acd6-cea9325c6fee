class Staff {
  final int id;
  final String username;
  final String level; // 'supervisor', 'admin', 'regular', 'staff_bom', 'driver'
  final String floor; // 例如 '1F', '2F', '3F', 'ALL'
  final String fullName;
  final String status;
  final DateTime? lastLogin; // 改为可空类型
  final DateTime createdAt;

  Staff({
    required this.id,
    required this.username,
    required this.level,
    required this.floor,
    required this.fullName,
    required this.status,
    this.lastLogin, // 改为可选参数
    required this.createdAt,
  });

  factory Staff.fromJson(Map<String, dynamic> json) {
    try {
      return Staff(
        id: json['id'] as int,
        username: json['username'] as String,
        level: json['level'] as String,
        floor: json['floor'] as String,
        fullName: json['full_name'] as String,
        status: json['status'] as String,
        lastLogin: json['last_login'] != null
            ? DateTime.parse(json['last_login'] as String)
            : null,
        createdAt: DateTime.parse(json['created_at'] as String),
      );
    } catch (e) {
      rethrow;
    }
  }

  // 用于创建新员工时的请求体
  Map<String, dynamic> toCreateJson() {
    return {
      'username': username,
      'level': level,
      'floor': floor,
      'full_name': fullName,
    };
  }
}
