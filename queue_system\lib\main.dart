import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/providers/document_provider.dart';
import 'package:queue_system/providers/locale_provider.dart';
import 'package:queue_system/providers/notification_provider.dart';
import 'package:queue_system/providers/stock_return_provider.dart';
import 'package:queue_system/providers/stock_return_tracking_provider.dart';
import 'package:queue_system/providers/staff_provider.dart';

import 'package:queue_system/screens/splash_screen.dart';
import 'package:queue_system/utils/app_config.dart';

void main() {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 我们已经在Android和iOS配置中禁用了原生启动屏幕
  // 应用将直接显示我们的自定义启动屏幕

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => AuthProvider(baseUrl: AppConfig.baseUrl),
        ),
        ChangeNotifierProvider(
          create: (context) => DocumentProvider(baseUrl: AppConfig.baseUrl),
        ),
        ChangeNotifierProvider(
          create: (context) => CheckListProvider(baseUrl: AppConfig.baseUrl),
        ),
        ChangeNotifierProvider(
          create: (context) => LocaleProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => StaffProvider(baseUrl: AppConfig.baseUrl),
        ),
        ChangeNotifierProxyProvider<AuthProvider, NotificationProvider>(
          create: (context) => NotificationProvider(
            authProvider: Provider.of<AuthProvider>(context, listen: false),
          ),
          update: (context, authProvider, previous) => NotificationProvider(
            authProvider: authProvider,
          ),
        ),
        ChangeNotifierProxyProvider<AuthProvider, StockReturnProvider>(
          create: (context) => StockReturnProvider(
            checkService: Provider.of<CheckListProvider>(context, listen: false)
                .checkService,
            authProvider: Provider.of<AuthProvider>(context, listen: false),
          ),
          update: (context, authProvider, previous) => StockReturnProvider(
            checkService: Provider.of<CheckListProvider>(context, listen: false)
                .checkService,
            authProvider: authProvider,
          ),
        ),
        ChangeNotifierProxyProvider<AuthProvider, StockReturnTrackingProvider>(
          create: (context) => StockReturnTrackingProvider(
            checkService: Provider.of<CheckListProvider>(context, listen: false)
                .checkService,
            authProvider: Provider.of<AuthProvider>(context, listen: false),
          ),
          update: (context, authProvider, previous) =>
              StockReturnTrackingProvider(
            checkService: Provider.of<CheckListProvider>(context, listen: false)
                .checkService,
            authProvider: authProvider,
          ),
        ),
      ],
      child: Consumer<LocaleProvider>(
        builder: (context, localeProvider, _) {
          return MaterialApp(
            title: 'Tregoal',
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
              useMaterial3: true,
            ),
            locale: localeProvider.locale,
            supportedLocales: const [
              Locale('zh', 'CN'),
              Locale('en', 'US'),
              Locale('ms', 'MY'),
            ],
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            localeResolutionCallback: (locale, supportedLocales) {
              for (var supportedLocale in supportedLocales) {
                if (supportedLocale.languageCode == locale?.languageCode) {
                  return supportedLocale;
                }
              }
              return supportedLocales.last;
            },
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}
