import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';

class EmailDialog extends StatefulWidget {
  final String documentNo;
  final String? defaultEmail;

  const EmailDialog({
    super.key,
    required this.documentNo,
    this.defaultEmail,
  });

  @override
  State<EmailDialog> createState() => _EmailDialogState();
}

class _EmailDialogState extends State<EmailDialog> {
  late TextEditingController _emailController;
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController(text: widget.defaultEmail ?? '');
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(context.t('send_pdf_email')),
      content: Form(
        key: _form<PERSON><PERSON>,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${context.t('document_no')}: ${widget.documentNo}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: InputDecoration(
                labelText: context.t('email'),
                hintText: '<EMAIL>, <EMAIL>',
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return context.t('email_required');
                }

                // 支持多个邮箱地址，用逗号分隔
                List<String> emails =
                    value.split(',').map((e) => e.trim()).toList();

                // 更灵活的邮箱格式验证，支持多级域名
                final emailRegex =
                    RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

                for (String email in emails) {
                  if (email.isEmpty) continue; // 跳过空字符串
                  if (!emailRegex.hasMatch(email)) {
                    return context.t('invalid_email');
                  }
                }
                return null;
              },
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text(context.t('cancel')),
        ),
        ElevatedButton(
          onPressed: _isLoading
              ? null
              : () {
                  if (_formKey.currentState!.validate()) {
                    Navigator.of(context).pop(_emailController.text);
                  }
                },
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(context.t('send')),
        ),
      ],
    );
  }
}
