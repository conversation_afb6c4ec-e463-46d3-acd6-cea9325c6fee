import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/providers/check_list_provider.dart';

class BomParentStatusWidget extends StatefulWidget {
  final CheckItem parentItem;

  const BomParentStatusWidget({
    super.key,
    required this.parentItem,
  });

  @override
  State<BomParentStatusWidget> createState() => _BomParentStatusWidgetState();
}

class _BomParentStatusWidgetState extends State<BomParentStatusWidget> {
  bool _isLoading = false;
  List<CheckItem> _children = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadBomChildren();
  }

  Future<void> _loadBomChildren() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 使用更精确的查询参数来区分不同的BOM父项
      // 优先使用父项的stock作为唯一标识符，结合bomGroup来确保获取正确的子项
      String bomGroup = widget.parentItem.bomGroup ?? '';
      String parentStock = widget.parentItem.stock;

      if (bomGroup.isEmpty && widget.parentItem.documentNo.contains('/')) {
        // 如果bomGroup为空，尝试从documentNo提取主文档编号作为bomGroup
        bomGroup = widget.parentItem.documentNo.split('/').first;
      }

      final checkListProvider =
          Provider.of<CheckListProvider>(context, listen: false);
      // 传递父项的stock和bomGroup，以便在服务端精确查询
      final children =
          await checkListProvider.getBomChildrenStatus(bomGroup, parentStock);

      if (mounted) {
        setState(() {
          _children = children;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_error != null) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          '${context.t('error_loading_bom_status')}: $_error',
          style: TextStyle(color: Colors.red.shade700, fontSize: 12),
        ),
      );
    }

    if (_children.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          context.t('no_bom_children_found'),
          style: TextStyle(color: Colors.orange.shade700, fontSize: 12),
        ),
      );
    }

    // 检查所有子项是否都已被BOM专员检查
    final allCheckedByBomSpecialist =
        _children.every((child) => child.bomSpecialistChecked);

    // 根据检查状态显示不同的提示信息
    final statusText = allCheckedByBomSpecialist
        ? context.t('waiting_for_supervisor')
        : context.t('waiting_for_staff_bom');

    final statusColor =
        allCheckedByBomSpecialist ? Colors.green : Colors.orange;
    final statusIcon =
        allCheckedByBomSpecialist ? Icons.check_circle : Icons.hourglass_empty;

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.t('bom_status'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: Colors.grey.shade800,
                  ),
                ),
                Text(
                  statusText,
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${_children.length} ${context.t('bom_items')} - ${_children.where((c) => c.bomSpecialistChecked).length} ${context.t('checked')}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
