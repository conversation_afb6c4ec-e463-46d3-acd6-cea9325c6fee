---
description: 
globs: 
alwaysApply: false
---
# 队列系统 NestJS 后端 (SQL-Sync) 项目概述

## 项目描述
这是一个基于NestJS框架构建的队列系统后端服务，主要提供排队系统的API服务，支持数据同步、员工认证、仓库检查、订单出货等功能。

## 项目结构
- `src/` - 源代码目录
  - `main.ts` - 应用程序入口点
  - `app.module.ts` - 根模块
  - `modules/` - 业务模块目录
  - `config/` - 配置文件
  - `database/` - 数据库连接与实体定义
  - `common/` - 公共工具和辅助类

## 主要功能模块
1. **员工管理模块** - 处理员工认证、权限控制等
2. **仓库检查模块** - 提供仓库物品检查、确认等功能
3. **订单出货模块** - 处理订单出货、司机送货流程
4. **通知系统模块** - 管理系统内部通知
5. **数据同步模块** - 负责与SQL Server数据库同步

## 技术栈
- NestJS - 后端框架
- TypeORM - ORM工具
- PostgreSQL - 主数据库
- Microsoft SQL Server - 源数据库(用于同步)
- Swagger - API文档

## 版本信息
- 当前版本: 0.0.1

