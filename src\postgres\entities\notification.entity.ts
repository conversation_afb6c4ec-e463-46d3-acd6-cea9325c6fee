import { <PERSON>ti<PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Staff } from './staff.entity';
import { Detail } from './detail.entity';

@Entity('notification')
export class Notification {
  @ApiProperty({ description: 'ID', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '通知类型', example: 'reject' })
  @Column()
  type: string;

  @ApiProperty({ description: '通知内容', example: '您的检查被主管退回，请重新检查' })
  @Column()
  message: string;

  @ApiProperty({ description: '接收者ID', example: 1 })
  @Column()
  recipient_id: number;

  @ApiProperty({ description: '发送者ID', example: 2 })
  @Column()
  sender_id: number;

  @ApiProperty({ description: '关联单据编号', example: 'D00001' })
  @Column()
  document_no: string;

  @ApiProperty({ description: '关联行号', example: 1 })
  @Column()
  line: number;

  @ApiProperty({ description: '关联Detail ID', example: 1 })
  @Column()
  detail_id: number;

  @ApiProperty({ description: '是否已读', example: false })
  @Column({ default: false })
  read: boolean;

  @ApiProperty({ description: '拒绝原因', example: '数量不符' })
  @Column({ nullable: true })
  reject_reason: string;

  @ApiProperty({ description: '创建时间', example: '2025-04-04T12:00:00Z' })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @ApiProperty({ description: '已读时间', example: '2025-04-04T12:00:00Z', nullable: true })
  @Column({ nullable: true })
  read_at: Date;

  @ManyToOne(() => Staff)
  @JoinColumn({ name: 'recipient_id' })
  recipient: Staff;

  @ManyToOne(() => Staff)
  @JoinColumn({ name: 'sender_id' })
  sender: Staff;

  @ManyToOne(() => Detail, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'detail_id' })
  detail: Detail;
} 