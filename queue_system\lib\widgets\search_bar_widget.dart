import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

class SearchBarWidget extends StatefulWidget {
  final String? hintText;
  final Function(String) onSearchChanged;
  final VoidCallback? onClear;
  final String? initialValue;
  final bool enabled;

  const SearchBarWidget({
    super.key,
    this.hintText,
    required this.onSearchChanged,
    this.onClear,
    this.initialValue,
    this.enabled = true,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  late TextEditingController _controller;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _hasText = _controller.text.isNotEmpty;
    _controller.addListener(_onTextChanged);
  }

  @override
  void didUpdateWidget(SearchBarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 只有当 initialValue 真正改变，且用户没有在编辑时才更新
    if (widget.initialValue != oldWidget.initialValue) {
      final newValue = widget.initialValue ?? '';

      // 安全地检查用户是否正在编辑
      bool isUserEditing = false;
      try {
        // 检查选择范围是否有效且不是折叠状态
        final selection = _controller.selection;
        isUserEditing = selection.isValid &&
            selection.start >= 0 &&
            selection.end <= _controller.text.length &&
            !selection.isCollapsed;
      } catch (e) {
        // 如果访问选择范围出错，假设用户没有在编辑
        isUserEditing = false;
        print('SearchBarWidget.didUpdateWidget: 选择范围检查出错: $e');
      }

      print(
          'SearchBarWidget.didUpdateWidget: oldValue="${oldWidget.initialValue}", newValue="$newValue", currentText="${_controller.text}", isUserEditing=$isUserEditing');

      if (_controller.text != newValue && !isUserEditing) {
        print(
            'SearchBarWidget.didUpdateWidget: 更新控制器文本从 "${_controller.text}" 到 "$newValue"');
        // 暂时移除监听器，避免触发 _onTextChanged
        _controller.removeListener(_onTextChanged);
        _controller.text = newValue;
        _hasText = newValue.isNotEmpty;
        // 重新添加监听器
        _controller.addListener(_onTextChanged);
      }
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
    // 调试日志：记录文本变化
    print('SearchBarWidget._onTextChanged: "${_controller.text}"');
    widget.onSearchChanged(_controller.text);
  }

  void _clearSearch() {
    _controller.clear();
    widget.onClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextField(
        controller: _controller,
        enabled: widget.enabled,
        decoration: InputDecoration(
          hintText: widget.hintText ?? context.t('search_documents'),
          hintStyle: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Colors.grey.shade600,
            size: 20,
          ),
          suffixIcon: _hasText
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  onPressed: _clearSearch,
                  splashRadius: 16,
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: const TextStyle(fontSize: 14),
        textInputAction: TextInputAction.search,
      ),
    );
  }
}
