import 'package:dio/dio.dart';
import 'package:queue_system/models/check_item.dart';
import 'package:queue_system/models/document.dart';
import 'package:queue_system/models/shipping_document.dart';
import 'package:queue_system/models/stock_return_model.dart';
import 'package:queue_system/models/stock_return_tracking_model.dart';

// 错误代码常量
class CheckErrorCode {
  static const String getChecklistFailed = 'error_get_checklist';
  static const String staffCheckFailed = 'error_staff_check';
  static const String supervisorCheckFailed = 'error_supervisor_check';
  static const String bomCheckFailed = 'error_bom_check';
  static const String rejectCheckFailed = 'error_reject_check';
  static const String documentReadyFailed = 'error_document_ready';
  static const String getReadyDocumentsFailed = 'error_get_ready_documents';
  static const String completeDocumentFailed = 'error_complete_document';
  static const String getDeliveryListFailed = 'error_get_delivery_list';
  static const String confirmDeliveryFailed = 'error_confirm_delivery';
  static const String getShipmentsFailed = 'error_get_shipments';
  static const String rejectDeliveryFailed = 'error_reject_delivery';
  static const String syncDetailFailed = 'error_sync_detail';
  static const String stockReturnFailed = 'error_stock_return';
  static const String bomChildrenDetailsFailed = 'error_bom_children_details';
  static const String networkError = 'error_network';
}

class CheckService {
  final Dio _dio;
  final String baseUrl;

  CheckService({required this.baseUrl})
      : _dio = Dio(BaseOptions(
          baseUrl: baseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout:
              const Duration(seconds: 30), // 增加到30秒，因为包含PDF生成和Firebase上传
        ));

  // 获取员工的检查列表（简化版本，移除分页）
  Future<Map<String, dynamic>> getCheckListPaginated(int staffId,
      {String? searchQuery}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'staffId': staffId,
      };

      // 如果有搜索查询，添加到参数中
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['search'] = searchQuery.trim();
      }

      final response = await _dio.get(
        '/check/list-paginated',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // 解析分页数据
        final responseData = data['data'] as Map<String, dynamic>;
        final int total = data['total'] as int;

        // 解析各类型的检查项目
        List<CheckItem> pendingRegularCheck = [];
        List<CheckItem> pendingBomCheck = [];
        List<CheckItem> pendingSupervisorCheck = [];
        List<CheckItem> waitingList = [];

        // 安全地解析普通库存项目待检查列表
        if (responseData['pendingRegularCheck'] != null) {
          try {
            final List<dynamic> regularCheckList =
                responseData['pendingRegularCheck'] as List<dynamic>;

            for (var i = 0; i < regularCheckList.length; i++) {
              try {
                final item = regularCheckList[i];
                pendingRegularCheck
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析BOM子项待检查列表
        if (responseData['pendingBomCheck'] != null) {
          try {
            final List<dynamic> bomCheckList =
                responseData['pendingBomCheck'] as List<dynamic>;

            for (var i = 0; i < bomCheckList.length; i++) {
              try {
                final item = bomCheckList[i];
                pendingBomCheck
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析待主管确认列表
        if (responseData['pendingSupervisorCheck'] != null) {
          try {
            final List<dynamic> supervisorCheckList =
                responseData['pendingSupervisorCheck'] as List<dynamic>;

            for (var i = 0; i < supervisorCheckList.length; i++) {
              try {
                final item = supervisorCheckList[i];
                pendingSupervisorCheck
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析等待列表
        if (responseData['waitingList'] != null) {
          try {
            final List<dynamic> waitingListData =
                responseData['waitingList'] as List<dynamic>;

            for (var i = 0; i < waitingListData.length; i++) {
              try {
                final item = waitingListData[i];
                waitingList
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        return {
          'data': {
            'pendingRegularCheck': pendingRegularCheck,
            'pendingBomCheck': pendingBomCheck,
            'pendingSupervisorCheck': pendingSupervisorCheck,
            'waitingList': waitingList,
            'total': responseData['total'] ?? 0
          },
          'total': total,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw e.toString();
    }
  }

  // 获取员工的检查列表（原版本，保持兼容性）
  Future<Map<String, List<CheckItem>>> getCheckList(int staffId) async {
    try {
      final response = await _dio.get(
        '/check/list',
        queryParameters: {'staffId': staffId},
      );
      if (response.statusCode == 200) {
        final data = response.data;
        List<CheckItem> pendingRegularCheck = []; // 改名：普通库存项目
        List<CheckItem> pendingBomCheck = []; // 新增：BOM子项
        List<CheckItem> pendingSupervisorCheck = [];
        List<CheckItem> checkedItems = [];
        List<CheckItem> waitingList = [];

        // 安全地解析普通库存项目待检查列表
        if (data['pendingRegularCheck'] != null) {
          try {
            final List<dynamic> regularCheckList =
                data['pendingRegularCheck'] as List<dynamic>;

            for (var i = 0; i < regularCheckList.length; i++) {
              try {
                final item = regularCheckList[i];
                pendingRegularCheck
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析BOM子项待检查列表
        if (data['pendingBomCheck'] != null) {
          try {
            final List<dynamic> bomCheckList =
                data['pendingBomCheck'] as List<dynamic>;

            for (var i = 0; i < bomCheckList.length; i++) {
              try {
                final item = bomCheckList[i];
                pendingBomCheck
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析主管待确认列表
        if (data['pendingSupervisorCheck'] != null) {
          try {
            final List<dynamic> supervisorCheckList =
                data['pendingSupervisorCheck'] as List<dynamic>;

            for (var i = 0; i < supervisorCheckList.length; i++) {
              try {
                final item = supervisorCheckList[i];
                pendingSupervisorCheck
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析已检查项列表 - 为BOM专员提供
        if (data['checkedItems'] != null) {
          try {
            final List<dynamic> checkedItemsList =
                data['checkedItems'] as List<dynamic>;

            for (var i = 0; i < checkedItemsList.length; i++) {
              try {
                final item = checkedItemsList[i];
                checkedItems
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        // 安全地解析等待列表 - 为BOM专员提供
        if (data['waitingList'] != null) {
          try {
            final List<dynamic> waitingItemsList =
                data['waitingList'] as List<dynamic>;

            for (var i = 0; i < waitingItemsList.length; i++) {
              try {
                final item = waitingItemsList[i];
                waitingList
                    .add(CheckItem.fromJson(item as Map<String, dynamic>));
              } catch (e) {
                // 如发生解析错误，继续处理下一项
              }
            }
          } catch (e) {
            // 处理整个列表解析错误
          }
        }

        return {
          'pendingRegularCheck': pendingRegularCheck,
          'pendingBomCheck': pendingBomCheck,
          'pendingSupervisorCheck': pendingSupervisorCheck,
          'checkedItems': checkedItems,
          'waitingList': waitingList,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getChecklistFailed}: $e';
    }
  }

  // 获取BOM子项状态
  Future<List<CheckItem>> getBomChildrenStatus(String bomGroup,
      [String? parentStock, int? bomParentId]) async {
    try {
      // 构建查询参数
      final Map<String, dynamic> queryParams = {'bomGroup': bomGroup};
      if (bomParentId != null) {
        queryParams['bomParentId'] = bomParentId;
      } else if (parentStock != null && parentStock.isNotEmpty) {
        queryParams['parentStock'] = parentStock;
      }

      final response = await _dio.get(
        '/check/bom-children',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        List<CheckItem> bomChildren = [];

        if (data is List) {
          print("===== getBomChildrenStatus API返回数据 =====");
          print("返回数据条数: ${data.length}");

          for (var i = 0; i < data.length; i++) {
            try {
              final item = data[i];

              // 打印原始数据中的bin_shelf_no字段
              if (item is Map<String, dynamic>) {
                print(
                    "项目 #$i: document_no=${item['document_no']}, bin_shelf_no='${item['bin_shelf_no']}', parent_code=${item['parent_code']}, is_bom_parent=${item['is_bom_parent']}");
              }

              final checkItem =
                  CheckItem.fromJson(item as Map<String, dynamic>);

              // 打印转换后的binShelfNo字段
              print(
                  "转换后: documentNo=${checkItem.documentNo}, binShelfNo='${checkItem.binShelfNo}', parentCode=${checkItem.parentCode}, isBomParent=${checkItem.isBomParent}");

              bomChildren.add(checkItem);
            } catch (e) {
              print("解析项目时出错: $e");
              // 如发生解析错误，继续处理下一项
            }
          }
          print("===== getBomChildrenStatus API返回数据结束 =====");
        }

        return bomChildren;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getChecklistFailed}: $e';
    }
  }

  // 获取BOM子项详细信息用于逐个检查
  Future<List<dynamic>> getBomChildrenDetails(
      String bomGroup, String parentStock, int supervisorId) async {
    try {
      print(
          "CheckService.getBomChildrenDetails - 开始: bomGroup=$bomGroup, parentStock=$parentStock, supervisorId=$supervisorId");

      final response = await _dio.get(
        '/check/bom-children-details',
        queryParameters: {
          'bomGroup': bomGroup,
          'parentStock': parentStock,
          'supervisorId': supervisorId,
        },
      );

      print(
          "CheckService.getBomChildrenDetails - 收到响应: statusCode=${response.statusCode}");

      if (response.statusCode != 200) {
        throw CheckErrorCode.bomChildrenDetailsFailed;
      }

      final List<dynamic> data = response.data;
      print("CheckService.getBomChildrenDetails - 数据长度: ${data.length}");

      return data;
    } catch (e) {
      print("CheckService.getBomChildrenDetails - 错误: $e");
      throw CheckErrorCode.bomChildrenDetailsFailed;
    }
  }

  // 获取等待列表
  Future<List<CheckItem>> getWaitingList(int staffId) async {
    try {
      final response = await _dio.get(
        '/check/waiting',
        queryParameters: {'staffId': staffId},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        List<CheckItem> waitingList = [];

        if (data is List) {
          for (var i = 0; i < data.length; i++) {
            try {
              final item = data[i];
              waitingList.add(CheckItem.fromJson(item as Map<String, dynamic>));
            } catch (e) {
              // 如发生解析错误，继续处理下一项
            }
          }
        }

        return waitingList;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getChecklistFailed}: $e';
    }
  }

  // 员工执行检查
  Future<void> staffCheck(String documentNo, int line, int staffId) async {
    try {
      final response = await _dio.post(
        '/check/staff',
        queryParameters: {
          'documentNo': documentNo,
          'line': line,
          'staffId': staffId,
        },
      );

      if (response.statusCode != 200) {
        throw CheckErrorCode.staffCheckFailed;
      }
    } on DioException catch (e) {
      if (e.response != null) {
        // 从错误响应中获取详细信息
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.staffCheckFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      rethrow;
    }
  }

  // 主管执行确认
  Future<void> supervisorCheck(
      String documentNo, int line, int supervisorId) async {
    try {
      // 优化网络请求配置
      final options = Options(
        sendTimeout: const Duration(seconds: 8),
        receiveTimeout: const Duration(seconds: 8),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      // 直接调用主管确认API，让后端负责验证逻辑
      final response = await _dio.post(
        '/check/supervisor',
        queryParameters: {
          'documentNo': documentNo,
          'line': line,
          'supervisorId': supervisorId,
        },
        options: options,
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw CheckErrorCode.supervisorCheckFailed;
      }

      print("CheckService.supervisorCheck - 成功完成");
    } on DioException catch (e) {
      print(
          "CheckService.supervisorCheck - DioException: ${e.message}, type=${e.type}");
      if (e.response != null) {
        // 从错误响应中获取详细信息
        final message = e.response?.data?['message'] ??
            CheckErrorCode.supervisorCheckFailed;
        print(
            "CheckService.supervisorCheck - 错误响应: ${e.response?.statusCode}, message=$message");
        throw message.toString();
      }
      print("CheckService.supervisorCheck - 网络错误");
      throw CheckErrorCode.networkError;
    } catch (e) {
      print("CheckService.supervisorCheck - 未知错误: $e");
      rethrow;
    } finally {
      // 恢复默认超时设置
      _dio.options.connectTimeout = const Duration(seconds: 10);
      _dio.options.receiveTimeout = const Duration(seconds: 30);
    }
  }

  // BOM专员执行检查
  Future<void> bomCheck(
      String documentNo, int line, int bomSpecialistId) async {
    try {
      print("===== BOM专员检查调试信息 =====");
      print(
          "开始BOM专员检查: documentNo=$documentNo, line=$line, bomSpecialistId=$bomSpecialistId");

      final response = await _dio.post(
        '/check/bom',
        queryParameters: {
          'documentNo': documentNo,
          'line': line,
          'bomSpecialistId': bomSpecialistId,
        },
      );

      print("BOM专员检查API响应状态码: ${response.statusCode}");
      print("BOM专员检查API响应数据: ${response.data}");

      if (response.statusCode != 200) {
        print("BOM专员检查失败: 非200状态码");
        throw CheckErrorCode.bomCheckFailed;
      }

      print("BOM专员检查成功完成");
      print("===== BOM专员检查调试信息结束 =====");
    } on DioException catch (e) {
      print("BOM专员检查DioException: ${e.message}, type=${e.type}");
      if (e.response != null) {
        // 从错误响应中获取详细信息
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.bomCheckFailed;
        print("BOM专员检查错误响应: ${e.response?.statusCode}, message=$message");
        throw message.toString();
      }
      print("BOM专员检查网络错误");
      throw CheckErrorCode.networkError;
    } catch (e) {
      print("BOM专员检查未知错误: $e");
      rethrow;
    }
  }

  // 主管拒绝员工检查
  Future<void> rejectStaffCheck(
      String documentNo, int line, int supervisorId, String reason) async {
    try {
      print(
          "开始拒绝员工检查: documentNo=$documentNo, line=$line, supervisorId=$supervisorId");

      final response = await _dio.post(
        '/check/reject',
        queryParameters: {
          'documentNo': documentNo,
          'line': line,
          'supervisorId': supervisorId,
          'reason': reason,
        },
      );

      print(
          "拒绝员工检查响应: statusCode=${response.statusCode}, data=${response.data}");

      if (response.statusCode != 200 && response.statusCode != 201) {
        print("拒绝员工检查失败: 非200或201状态码");
        throw CheckErrorCode.rejectCheckFailed;
      }

      print("拒绝员工检查成功");
      // 成功时不需要返回任何内容
      return;
    } on DioException catch (e) {
      print("拒绝员工检查DioException: ${e.message}, type=${e.type}");
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.rejectCheckFailed;
        print("拒绝员工检查错误响应: ${e.response?.statusCode}, message=$message");
        throw message.toString();
      }
      print("拒绝员工检查网络错误");
      throw CheckErrorCode.networkError;
    } catch (e) {
      print("拒绝员工检查未知错误: $e");
      rethrow;
    }
  }

  // 获取所有未出货的文档列表（简化版本，移除分页）
  Future<Map<String, dynamic>> getAllUnshippedDocuments(int staffId,
      {String? searchQuery}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'staffId': staffId,
      };

      // 如果有搜索查询，添加到参数中
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['search'] = searchQuery.trim();
      }

      final response = await _dio.get(
        '/check/all-unshipped-documents',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final List<dynamic> documentsJson = data['data'] as List<dynamic>;

        final List<ShippingDocument> documents = documentsJson
            .map((json) =>
                ShippingDocument.fromJson(json as Map<String, dynamic>))
            .toList();

        return {
          'data': documents,
          'total': data['total'] as int,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getReadyDocumentsFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            CheckErrorCode.getReadyDocumentsFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getReadyDocumentsFailed}: $e';
    }
  }

  // 检查文档是否可以完成出货
  Future<Map<String, dynamic>> isDocumentReady(
      String documentNo, int staffId) async {
    try {
      final response = await _dio.get(
        '/check/document-ready',
        queryParameters: {
          'documentNo': documentNo,
          'staffId': staffId,
        },
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.documentReadyFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.documentReadyFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.documentReadyFailed}: $e';
    }
  }

  // 获取可以完成出货的文档列表 - 支持分页
  // 新的 to_ship 专用API - 性能更好，分页更准确
  Future<Map<String, dynamic>> getToShipDocuments(int staffId,
      {int page = 1, int limit = 10}) async {
    try {
      final response = await _dio.get(
        '/check/to-ship-documents',
        queryParameters: {
          'staffId': staffId,
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> dataList = responseData['data'] as List<dynamic>;

        return {
          'data': dataList
              .map((item) =>
                  ShippingDocument.fromJson(item as Map<String, dynamic>))
              .toList(),
          'total': responseData['total'] as int,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getReadyDocumentsFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            CheckErrorCode.getReadyDocumentsFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getReadyDocumentsFailed}: $e';
    }
  }

  // 旧版本API - 保留兼容性
  Future<Map<String, dynamic>> getReadyDocuments(int staffId,
      {int page = 1, int limit = 10}) async {
    try {
      final response = await _dio.get(
        '/check/ready-documents',
        queryParameters: {
          'staffId': staffId,
          'page': page,
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> dataList = responseData['data'] as List<dynamic>;

        return {
          'data': dataList
              .map((item) =>
                  ShippingDocument.fromJson(item as Map<String, dynamic>))
              .toList(),
          'total': responseData['total'] as int,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getReadyDocumentsFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            CheckErrorCode.getReadyDocumentsFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getReadyDocumentsFailed}: $e';
    }
  }

  // 标记文档为已出货
  Future<Map<String, dynamic>> completeDocument(
      String documentNo, int supervisorId, int? driverId) async {
    try {
      final Map<String, dynamic> queryParams = {
        'documentNo': documentNo,
        'supervisorId': supervisorId,
      };

      if (driverId != null) {
        queryParams['driverId'] = driverId;
      }

      final response = await _dio.post(
        '/check/complete',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        // 确保 pdfPath 字段存在
        if (responseData.containsKey('pdfPath')) {
          if (responseData['pdfPath'] == null) {
            responseData['pdfPath'] = '';
          }
        } else {
          responseData['pdfPath'] = '';
        }

        return responseData;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.completeDocumentFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            CheckErrorCode.completeDocumentFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.completeDocumentFailed}: $e';
    }
  }

  // 获取司机待送达订单列表
  Future<List<ShippingDocument>> getDriverDeliveryList(int driverId) async {
    try {
      final response = await _dio.get(
        '/shipping/delivery-list',
        queryParameters: {'driverId': driverId},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((item) =>
                ShippingDocument.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getDeliveryListFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            CheckErrorCode.getDeliveryListFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getDeliveryListFailed}: $e';
    }
  }

  // 司机确认订单送达
  Future<Map<String, dynamic>> confirmDelivery(
      String documentNo, int driverId, String notes) async {
    try {
      final response = await _dio.post(
        '/shipping/confirm-delivery',
        queryParameters: {
          'documentNo': documentNo,
          'driverId': driverId,
          'notes': notes,
        },
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.confirmDeliveryFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            CheckErrorCode.confirmDeliveryFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.confirmDeliveryFailed}: $e';
    }
  }

  // 获取主管指派的订单列表及其送达状态
  Future<Map<String, dynamic>> getSupervisorShipments(int supervisorId,
      {int page = 1, int limit = 10, String? searchQuery}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'supervisorId': supervisorId,
        'page': page,
        'limit': limit,
      };

      // 如果有搜索查询，添加到参数中
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['search'] = searchQuery.trim();
      }

      final response = await _dio.get(
        '/shipping/supervisor-shipments',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        final List<dynamic> dataList = responseData['data'] as List<dynamic>;

        return {
          'data': dataList
              .map((item) =>
                  ShippingDocument.fromJson(item as Map<String, dynamic>))
              .toList(),
          'total': responseData['total'] as int,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getShipmentsFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getShipmentsFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.getShipmentsFailed}: $e';
    }
  }

  // 司机拒绝分配的订单
  Future<Map<String, dynamic>> rejectDelivery(
      String documentNo, int driverId, String reason) async {
    try {
      final response = await _dio.post(
        '/shipping/reject-delivery',
        queryParameters: {
          'documentNo': documentNo,
          'driverId': driverId,
          'reason': reason,
        },
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.rejectDeliveryFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.rejectDeliveryFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.rejectDeliveryFailed}: $e';
    }
  }

  // 主管拒绝待出货订单
  Future<Map<String, dynamic>> rejectShipment(
      String documentNo, int supervisorId, String reason) async {
    try {
      final response = await _dio.post(
        '/shipping/reject-shipment',
        queryParameters: {
          'documentNo': documentNo,
          'supervisorId': supervisorId,
          'reason': reason,
        },
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? 'Failed to reject shipment';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? 'Failed to reject shipment';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to reject shipment: $e';
    }
  }

  // 设置订单紧急状态（仅限主管）
  Future<Map<String, dynamic>> setUrgentStatus(
      String documentNo, int supervisorId,
      {int priority = 1}) async {
    try {
      final response = await _dio.post(
        '/check/set-urgent',
        queryParameters: {
          'documentNo': documentNo,
          'supervisorId': supervisorId,
          'priority': priority,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? 'Failed to set urgent status';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? 'Failed to set urgent status';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to set urgent status: $e';
    }
  }

  // 获取司机列表
  Future<List<Map<String, dynamic>>> getDrivers() async {
    try {
      final response = await _dio.get('/shipping/drivers');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        final message = response.data?['message'] ?? 'Failed to get drivers';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ?? 'Failed to get drivers';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to get drivers: $e';
    }
  }

  // 获取PDF文件URL
  String getPdfViewUrl(String documentNo, {String? date}) {
    final queryParams = {
      'documentNo': documentNo,
    };

    if (date != null) {
      queryParams['date'] = date;
    }

    final queryString = queryParams.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$baseUrl/pdf/view?$queryString';
  }

  // 注释掉同步单个 detail 记录方法 - 2025-06-18
  /*
  Future<Map<String, dynamic>> syncDetail(String documentNo, int line) async {
    try {
      final response = await _dio.post(
        '/sync/detail',
        queryParameters: {
          'documentNo': documentNo,
          'line': line,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.syncDetailFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.syncDetailFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.syncDetailFailed}: $e';
    }
  }
  */

  // 库存归位
  Future<Map<String, dynamic>> returnStock(
      String documentNo, int line, int staffId) async {
    try {
      final response = await _dio.post(
        '/stock/return',
        queryParameters: {
          'documentNo': documentNo,
          'line': line,
          'staffId': staffId,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.stockReturnFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.stockReturnFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw '${CheckErrorCode.stockReturnFailed}: $e';
    }
  }

  // 获取待退回库存列表
  Future<List<StockReturnModel>> getPendingReturnStocks(int staffId) async {
    try {
      final response = await _dio.get(
        '/stock/return/pending',
        queryParameters: {
          'staffId': staffId,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final items = data['items'] as List;
        return items.map((item) => StockReturnModel.fromJson(item)).toList();
      } else {
        final message =
            response.data?['message'] ?? 'Failed to get pending return stocks';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            'Failed to get pending return stocks';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to get pending return stocks: $e';
    }
  }

  // 确认库存已退回
  Future<Map<String, dynamic>> confirmStockReturn(
      int detailId, int staffId) async {
    try {
      final response = await _dio.post(
        '/stock/return/confirm',
        queryParameters: {
          'detailId': detailId,
          'staffId': staffId,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        final message =
            response.data?['message'] ?? 'Failed to confirm stock return';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? 'Failed to confirm stock return';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to confirm stock return: $e';
    }
  }

  // 重新同步整个订单
  Future<Map<String, dynamic>> refreshOrder(
      String documentNo, int supervisorId, String reason) async {
    try {
      print(
          "开始重新同步订单: documentNo=$documentNo, supervisorId=$supervisorId, reason=$reason");

      final response = await _dio.post(
        '/sync/refresh-order',
        queryParameters: {
          'documentNo': documentNo,
          'supervisorId': supervisorId,
          'reason': reason,
        },
      );

      print(
          "重新同步订单响应: statusCode=${response.statusCode}, data=${response.data}");

      if (response.statusCode != 200 && response.statusCode != 201) {
        print("重新同步订单失败: 非200或201状态码");
        throw 'error_refresh_order_failed';
      }

      print("重新同步订单成功");
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      print("重新同步订单DioException: ${e.message}, type=${e.type}");
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? 'error_refresh_order_failed';
        print("重新同步订单错误响应: ${e.response?.statusCode}, message=$message");
        throw message.toString();
      }
      print("重新同步订单网络错误");
      throw CheckErrorCode.networkError;
    } catch (e) {
      print("重新同步订单未知错误: $e");
      throw 'Failed to refresh order: $e';
    }
  }

  // 获取员工的待归还库存追踪列表
  Future<List<StockReturnTrackingModel>> getPendingStockReturnTracking(
      int staffId) async {
    try {
      final response = await _dio.get(
        '/stock-return-tracking/pending',
        queryParameters: {'staffId': staffId},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((item) =>
                StockReturnTrackingModel.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        final message = response.data?['message'] ??
            'Failed to get pending stock return tracking';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            'Failed to get pending stock return tracking';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to get pending stock return tracking: $e';
    }
  }

  // 确认库存归还完成
  Future<StockReturnTrackingModel> confirmStockReturnTracking(
      int trackingId, int staffId,
      {String? notes}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'trackingId': trackingId,
        'staffId': staffId,
      };

      if (notes != null && notes.isNotEmpty) {
        queryParams['notes'] = notes;
      }

      final response = await _dio.post(
        '/stock-return-tracking/confirm',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // 后端返回的是 StockReturnTracking 对象，转换为 Model
        return StockReturnTrackingModel.fromJson(
            response.data as Map<String, dynamic>);
      } else {
        final message =
            response.data?['message'] ?? 'Failed to confirm stock return';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? 'Failed to confirm stock return';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to confirm stock return: $e';
    }
  }

  // 获取库存归还统计信息
  Future<Map<String, dynamic>> getStockReturnStatistics(int staffId) async {
    try {
      final response = await _dio.get(
        '/stock-return-tracking/statistics',
        queryParameters: {'staffId': staffId},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        final message = response.data?['message'] ??
            'Failed to get stock return statistics';
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message = e.response?.data?['message'] ??
            'Failed to get stock return statistics';
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw 'Failed to get stock return statistics: $e';
    }
  }

  // 获取普通员工待检查列表（简化版本，移除分页）
  Future<Map<String, dynamic>> getRegularCheckListPaginated(int staffId,
      {String? searchQuery}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'staffId': staffId,
      };

      // 如果有搜索查询，添加到参数中
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['search'] = searchQuery.trim();
      }

      final response = await _dio.get(
        '/check/regular-paginated',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;

        // 转换数据结构
        final responseData = data['data'] as Map<String, dynamic>;
        final List<dynamic> regularCheckData =
            responseData['pendingRegularCheck'] ?? [];

        // 转换为CheckItem对象
        final List<CheckItem> regularCheckItems = [];
        for (var i = 0; i < regularCheckData.length; i++) {
          try {
            final item = regularCheckData[i];
            regularCheckItems
                .add(CheckItem.fromJson(item as Map<String, dynamic>));
          } catch (e) {
            // 如发生解析错误，继续处理下一项
          }
        }

        return {
          'data': {
            'pendingRegularCheck': regularCheckItems,
            'total': responseData['total'] ?? 0,
          },
          'total': data['total'] ?? 0,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw e.toString();
    }
  }

  // 获取BOM待检查列表（简化版本，移除分页）
  Future<Map<String, dynamic>> getBomCheckListPaginated(int staffId,
      {String? searchQuery}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'staffId': staffId,
      };

      // 如果有搜索查询，添加到参数中
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        queryParams['search'] = searchQuery.trim();
      }

      final response = await _dio.get(
        '/check/bom-paginated',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;

        // 转换数据结构
        final responseData = data['data'] as Map<String, dynamic>;
        final List<dynamic> bomCheckData =
            responseData['pendingBomCheck'] ?? [];

        // 转换为CheckItem对象
        final List<CheckItem> bomCheckItems = [];
        for (var i = 0; i < bomCheckData.length; i++) {
          try {
            final item = bomCheckData[i];
            bomCheckItems.add(CheckItem.fromJson(item as Map<String, dynamic>));
          } catch (e) {
            // 如发生解析错误，继续处理下一项
          }
        }

        return {
          'data': {
            'pendingBomCheck': bomCheckItems,
            'total': responseData['total'] ?? 0,
          },
          'total': data['total'] ?? 0,
        };
      } else {
        final message =
            response.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
    } on DioException catch (e) {
      if (e.response != null) {
        final message =
            e.response?.data?['message'] ?? CheckErrorCode.getChecklistFailed;
        throw message.toString();
      }
      throw CheckErrorCode.networkError;
    } catch (e) {
      throw e.toString();
    }
  }
}
