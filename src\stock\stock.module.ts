import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StockController } from './stock.controller';
import { StockService } from './stock.service';
import { StockReturnTrackingController } from './stock-return-tracking.controller';
import { StockReturnTrackingService } from './stock-return-tracking.service';
import { SqlServerStock } from './entities/stock.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { CnDetail } from '../postgres/entities/cn-detail.entity';
import { CnMain } from '../postgres/entities/cn-main.entity';
import { StockReturnTracking } from '../postgres/entities/stock-return-tracking.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SqlServerStock], 'sqlServerConnection'),
    TypeOrmModule.forFeature([Detail, Staff, CnDetail, CnMain, StockReturnTracking], 'postgresConnection'),
  ],
  controllers: [StockController, StockReturnTrackingController],
  providers: [StockService, StockReturnTrackingService],
  exports: [StockService, StockReturnTrackingService]
})
export class StockModule { }
