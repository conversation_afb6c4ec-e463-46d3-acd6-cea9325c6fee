import 'package:flutter/material.dart';
import 'package:queue_system/l10n/app_localizations.dart';

class InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? color;
  final bool isEmpty; // 新增参数，标识值是否为空

  const InfoChip({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    this.color,
    this.isEmpty = false, // 默认为false
  });

  @override
  Widget build(BuildContext context) {
    // 如果值为空，使用特殊样式
    final displayValue = isEmpty ? context.t('no_shelf_assigned') : value;
    final displayColor = isEmpty ? Colors.orange : color;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color: displayColor != null
            ? displayColor.withOpacity(0.1)
            : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: displayColor ?? Colors.grey.shade700),
          const SizedBox(width: 4),
          Text(
            '$label: $displayValue',
            style: TextStyle(
              fontSize: 12,
              color: displayColor ?? Colors.grey.shade800,
              fontWeight:
                  displayColor != null ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
