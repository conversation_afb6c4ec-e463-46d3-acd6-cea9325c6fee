import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StatusController } from './status.controller';
import { StatusService } from './status.service';
import { Detail } from '../postgres/entities/detail.entity';
import { Main } from '../postgres/entities/main.entity';
import { Staff } from '../postgres/entities/staff.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Detail, Main, Staff], 'postgresConnection'),
  ],
  controllers: [StatusController],
  providers: [StatusService],
})
export class StatusModule { }
