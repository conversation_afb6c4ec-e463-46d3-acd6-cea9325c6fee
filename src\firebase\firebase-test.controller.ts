import { Controller, Get, Post, Query } from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import { FirebaseSchedulerService } from './firebase-scheduler.service';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Firebase测试')
@Controller('firebase-test')
export class FirebaseTestController {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly firebaseSchedulerService: FirebaseSchedulerService,
  ) { }

  @Get('status')
  @ApiOperation({ summary: '检查Firebase服务状态' })
  @ApiResponse({
    status: 200,
    description: '返回Firebase服务状态',
    schema: {
      type: 'object',
      properties: {
        initialized: { type: 'boolean' },
        enabled: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  getFirebaseStatus() {
    const status = this.firebaseService.getStatus();
    return {
      ...status,
      message: status.initialized
        ? 'Firebase服务已初始化并可用'
        : status.enabled
          ? 'Firebase服务已启用但初始化失败，请检查配置'
          : 'Firebase服务已禁用',
    };
  }

  @Get('diagnose')
  @ApiOperation({ summary: '诊断Firebase配置问题' })
  @ApiResponse({
    status: 200,
    description: '返回详细的配置诊断信息',
  })
  diagnoseFirebaseConfig() {
    const configService = this.firebaseService['configService'];
    const firebaseConfig = configService.get('firebase');

    const diagnosis = {
      configExists: !!firebaseConfig,
      projectId: firebaseConfig?.projectId || 'NOT_SET',
      storageBucket: firebaseConfig?.storageBucket || 'NOT_SET',
      uploadEnabled: firebaseConfig?.uploadConfig?.enabled || false,
      serviceAccountPath: firebaseConfig?.serviceAccountPath || 'NOT_SET',
      serviceAccountFileExists: false,
      hasPrivateKey: !!firebaseConfig?.privateKey,
      hasClientEmail: !!firebaseConfig?.clientEmail,
      issues: [] as string[],
    };

    // 检查服务账户文件是否存在
    if (firebaseConfig?.serviceAccountPath) {
      try {
        const fs = require('fs');
        diagnosis.serviceAccountFileExists = fs.existsSync(firebaseConfig.serviceAccountPath);
      } catch (error) {
        diagnosis.issues.push(`无法检查服务账户文件: ${error.message}`);
      }
    }

    // 检查配置问题
    if (!firebaseConfig) {
      diagnosis.issues.push('Firebase配置未找到，请检查环境变量');
    } else {
      if (!firebaseConfig.projectId) {
        diagnosis.issues.push('缺少FIREBASE_PROJECT_ID环境变量');
      }
      if (!firebaseConfig.storageBucket) {
        diagnosis.issues.push('缺少FIREBASE_STORAGE_BUCKET环境变量');
      }
      if (!firebaseConfig.uploadConfig?.enabled) {
        diagnosis.issues.push('Firebase上传功能已禁用 (FIREBASE_UPLOAD_ENABLED=false)');
      }

      // 检查认证配置
      const hasServiceAccountFile = firebaseConfig.serviceAccountPath && diagnosis.serviceAccountFileExists;
      const hasEnvCredentials = firebaseConfig.privateKey && firebaseConfig.clientEmail;

      if (!hasServiceAccountFile && !hasEnvCredentials) {
        diagnosis.issues.push('缺少Firebase认证配置：');
        diagnosis.issues.push('- 方式1: 设置FIREBASE_SERVICE_ACCOUNT_PATH并确保文件存在');
        diagnosis.issues.push('- 方式2: 设置FIREBASE_PRIVATE_KEY和FIREBASE_CLIENT_EMAIL环境变量');
      }
    }

    return {
      diagnosis,
      recommendations: diagnosis.issues.length > 0 ? [
        '1. 检查.env文件中的Firebase配置',
        '2. 确保Firebase项目已创建并获取了正确的配置',
        '3. 如果使用服务账户文件，确保文件路径正确且文件存在',
        '4. 如果使用环境变量，确保私钥格式正确（包含\\n换行符）',
        '5. 重启应用以重新加载配置'
      ] : ['配置看起来正常，如果仍有问题请检查Firebase项目权限']
    };
  }

  @Post('test-upload')
  @ApiOperation({ summary: '测试PDF上传功能（仅用于开发测试）' })
  @ApiQuery({ name: 'documentNo', description: '测试用的订单编号' })
  @ApiResponse({
    status: 200,
    description: '返回上传测试结果',
  })
  async testUpload(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      return {
        success: false,
        error: '请提供documentNo参数',
      };
    }

    // 这里只是测试Firebase服务的状态，不实际上传文件
    const status = this.firebaseService.getStatus();

    if (!status.enabled) {
      return {
        success: false,
        error: 'Firebase上传功能已禁用',
        status,
      };
    }

    if (!status.initialized) {
      return {
        success: false,
        error: 'Firebase服务未初始化，请检查配置',
        status,
      };
    }

    return {
      success: true,
      message: `Firebase服务正常，可以上传文档: ${documentNo}`,
      status,
      note: '这只是状态测试，未实际上传文件。实际上传将在PDF生成后自动进行。',
    };
  }

  @Post('upload-existing-pdf')
  @ApiOperation({ summary: '上传现有PDF文件到Firebase Storage' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiQuery({ name: 'filePath', description: 'PDF文件的完整路径' })
  @ApiResponse({
    status: 200,
    description: '返回上传结果',
  })
  async uploadExistingPdf(
    @Query('documentNo') documentNo: string,
    @Query('filePath') filePath: string
  ) {
    if (!documentNo || !filePath) {
      return {
        success: false,
        error: '请提供documentNo和filePath参数',
      };
    }

    try {
      const result = await this.firebaseService.uploadPdf(filePath, documentNo);
      return {
        success: result.success,
        documentNo,
        filePath,
        firebase: result,
        message: result.success
          ? `PDF文件上传成功: ${documentNo}`
          : `PDF文件上传失败: ${result.error}`,
      };
    } catch (error) {
      return {
        success: false,
        error: `上传过程中发生异常: ${error.message}`,
        documentNo,
        filePath,
      };
    }
  }

  @Get('check-database-fields')
  @ApiOperation({ summary: '检查数据库Firebase字段是否存在' })
  @ApiResponse({
    status: 200,
    description: '返回数据库字段检查结果',
  })
  async checkDatabaseFields() {
    try {
      const configService = this.firebaseService['configService'];
      const mainRepository = this.firebaseService['mainRepository'];

      // 尝试查询一个订单的Firebase字段
      const testQuery = await mainRepository
        .createQueryBuilder('main')
        .select([
          'main.document_no',
          'main.firebase_download_url',
          'main.firebase_file_path',
          'main.firebase_uploaded_at'
        ])
        .where('main.document_no = :documentNo', { documentNo: 'DO396400' })
        .getOne();

      return {
        success: true,
        message: 'Firebase字段检查成功',
        fieldsExist: true,
        testRecord: testQuery,
        note: '如果testRecord为null，说明订单不存在；如果Firebase字段为null，说明尚未上传'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        fieldsExist: false,
        message: '数据库字段可能不存在，请执行SQL脚本添加字段',
        sqlScript: 'add-firebase-fields.sql'
      };
    }
  }

  @Post('test-database-save')
  @ApiOperation({ summary: '测试数据库保存功能' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiResponse({
    status: 200,
    description: '返回数据库保存测试结果',
  })
  async testDatabaseSave(@Query('documentNo') documentNo: string) {
    if (!documentNo) {
      return {
        success: false,
        error: '请提供documentNo参数',
      };
    }

    try {
      const mainRepository = this.firebaseService['mainRepository'];

      // 测试更新Firebase字段
      const testUrl = 'https://test-firebase-url.com/test.pdf';
      const testPath = 'pdfs/test/test.pdf';

      const updateResult = await mainRepository.update(
        { document_no: documentNo },
        {
          firebase_download_url: testUrl,
          firebase_file_path: testPath,
          firebase_uploaded_at: new Date(),
        }
      );

      if (updateResult.affected > 0) {
        // 验证更新是否成功
        const updatedRecord = await mainRepository.findOne({
          where: { document_no: documentNo },
          select: ['document_no', 'firebase_download_url', 'firebase_file_path', 'firebase_uploaded_at']
        });

        return {
          success: true,
          message: '数据库保存测试成功',
          affectedRows: updateResult.affected,
          updatedRecord,
        };
      } else {
        return {
          success: false,
          message: '未找到指定的订单记录',
          documentNo,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '数据库保存测试失败，可能是字段不存在',
      };
    }
  }

  @Get('check-upload-status')
  @ApiOperation({ summary: '检查PDF上传状态' })
  @ApiQuery({ name: 'documentNo', description: '订单编号（可选，不提供则检查所有）' })
  @ApiResponse({
    status: 200,
    description: '返回PDF上传状态检查结果',
  })
  async checkUploadStatus(@Query('documentNo') documentNo?: string) {
    try {
      const mainRepository = this.firebaseService['mainRepository'];

      let whereCondition = {};
      if (documentNo) {
        whereCondition = { document_no: documentNo };
      }

      // 查询订单记录
      const orders = await mainRepository.find({
        where: whereCondition,
        select: ['document_no', 'firebase_download_url', 'firebase_file_path', 'firebase_uploaded_at', 'created_at'],
        order: { created_at: 'DESC' },
        take: documentNo ? 1 : 50, // 如果指定订单则只查1个，否则查最近50个
      });

      const results = [];
      const fs = require('fs');
      const path = require('path');

      for (const order of orders) {
        // 检查本地PDF文件是否存在
        const pdfPath = this.findLocalPdfPath(order.document_no);
        const localExists = pdfPath ? fs.existsSync(pdfPath) : false;

        // 检查Firebase上传状态
        const firebaseUploaded = !!order.firebase_download_url;

        // 判断是否需要重新上传
        const needsReupload = localExists && !firebaseUploaded;

        results.push({
          documentNo: order.document_no,
          localPdfExists: localExists,
          localPdfPath: localExists ? pdfPath : null,
          firebaseUploaded,
          firebaseUrl: order.firebase_download_url,
          uploadedAt: order.firebase_uploaded_at,
          needsReupload,
          status: needsReupload ? 'NEEDS_REUPLOAD' :
            firebaseUploaded ? 'UPLOADED' :
              localExists ? 'LOCAL_ONLY' : 'NO_PDF',
        });
      }

      const summary = {
        total: results.length,
        uploaded: results.filter(r => r.firebaseUploaded).length,
        needsReupload: results.filter(r => r.needsReupload).length,
        localOnly: results.filter(r => r.localPdfExists && !r.firebaseUploaded).length,
        noPdf: results.filter(r => !r.localPdfExists && !r.firebaseUploaded).length,
      };

      return {
        success: true,
        summary,
        results: documentNo ? results[0] : results,
        message: `检查了${results.length}个订单的上传状态`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'PDF上传状态检查失败',
      };
    }
  }

  @Post('retry-upload')
  @ApiOperation({ summary: '重新上传PDF到Firebase Storage' })
  @ApiQuery({ name: 'documentNo', description: '订单编号' })
  @ApiQuery({ name: 'force', description: '是否强制重新上传（即使已存在）', required: false })
  @ApiResponse({
    status: 200,
    description: '返回重新上传结果',
  })
  async retryUpload(
    @Query('documentNo') documentNo: string,
    @Query('force') force?: string
  ) {
    if (!documentNo) {
      return {
        success: false,
        error: '请提供documentNo参数',
      };
    }

    try {
      const mainRepository = this.firebaseService['mainRepository'];
      const forceUpload = force === 'true';

      // 检查订单是否存在
      const order = await mainRepository.findOne({
        where: { document_no: documentNo },
        select: ['document_no', 'firebase_download_url', 'firebase_uploaded_at'],
      });

      if (!order) {
        return {
          success: false,
          error: `订单 ${documentNo} 不存在`,
        };
      }

      // 检查是否已经上传（除非强制重新上传）
      if (order.firebase_download_url && !forceUpload) {
        return {
          success: false,
          error: `订单 ${documentNo} 的PDF已经上传到Firebase，如需强制重新上传请添加 force=true 参数`,
          existingUrl: order.firebase_download_url,
          uploadedAt: order.firebase_uploaded_at,
        };
      }

      // 查找本地PDF文件
      const localPdfPath = this.findLocalPdfPath(documentNo);
      if (!localPdfPath) {
        return {
          success: false,
          error: `未找到订单 ${documentNo} 的本地PDF文件`,
          searchedPaths: this.getSearchPaths(documentNo),
        };
      }

      const fs = require('fs');
      if (!fs.existsSync(localPdfPath)) {
        return {
          success: false,
          error: `本地PDF文件不存在: ${localPdfPath}`,
        };
      }

      // 执行重新上传
      const uploadResult = await this.firebaseService.uploadPdf(localPdfPath, documentNo);

      return {
        success: uploadResult.success,
        documentNo,
        localPath: localPdfPath,
        firebase: uploadResult,
        message: uploadResult.success
          ? `PDF重新上传成功: ${documentNo}`
          : `PDF重新上传失败: ${uploadResult.error}`,
        isRetry: true,
        forced: forceUpload,
      };
    } catch (error) {
      return {
        success: false,
        error: `重新上传过程中发生异常: ${error.message}`,
        documentNo,
      };
    }
  }

  /**
   * 查找本地PDF文件路径
   */
  private findLocalPdfPath(documentNo: string): string | null {
    const fs = require('fs');
    const path = require('path');

    const searchPaths = this.getSearchPaths(documentNo);

    for (const searchPath of searchPaths) {
      if (fs.existsSync(searchPath)) {
        return searchPath;
      }
    }

    return null;
  }

  /**
   * 获取可能的PDF文件路径
   */
  private getSearchPaths(documentNo: string): string[] {
    const path = require('path');
    const baseDir = path.join(process.cwd(), 'PDF_Output');

    // 生成可能的日期文件夹
    const today = new Date();
    const dates = [];

    // 检查最近7天的文件夹
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }

    const searchPaths = [];

    // 为每个日期生成可能的路径
    for (const date of dates) {
      searchPaths.push(path.join(baseDir, date, `${documentNo}.pdf`));
    }

    // 也检查根目录
    searchPaths.push(path.join(baseDir, `${documentNo}.pdf`));

    return searchPaths;
  }

  @Post('batch-retry-upload')
  @ApiOperation({ summary: '批量重新上传失败的PDF文件' })
  @ApiQuery({ name: 'limit', description: '最大处理数量（默认10）', required: false })
  @ApiResponse({
    status: 200,
    description: '返回批量重新上传结果',
  })
  async batchRetryUpload(@Query('limit') limit?: string) {
    try {
      const maxLimit = parseInt(limit || '10', 10);
      const mainRepository = this.firebaseService['mainRepository'];

      // 查找需要重新上传的订单（本地有PDF但Firebase没有上传）
      const orders = await mainRepository.find({
        where: { firebase_download_url: null }, // Firebase未上传
        select: ['document_no', 'created_at'],
        order: { created_at: 'DESC' },
        take: maxLimit * 2, // 多查一些，因为可能有些本地文件不存在
      });

      const results = [];
      const fs = require('fs');
      let processedCount = 0;

      for (const order of orders) {
        if (processedCount >= maxLimit) break;

        // 检查本地PDF文件是否存在
        const localPdfPath = this.findLocalPdfPath(order.document_no);
        if (!localPdfPath || !fs.existsSync(localPdfPath)) {
          results.push({
            documentNo: order.document_no,
            success: false,
            error: '本地PDF文件不存在',
            skipped: true,
          });
          continue;
        }

        try {
          // 执行上传
          const uploadResult = await this.firebaseService.uploadPdf(localPdfPath, order.document_no);

          results.push({
            documentNo: order.document_no,
            success: uploadResult.success,
            localPath: localPdfPath,
            firebase: uploadResult,
            error: uploadResult.success ? null : uploadResult.error,
          });

          processedCount++;

          // 添加小延迟避免过快请求
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          results.push({
            documentNo: order.document_no,
            success: false,
            error: error.message,
            localPath: localPdfPath,
          });
          processedCount++;
        }
      }

      const summary = {
        totalChecked: orders.length,
        totalProcessed: processedCount,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success && !r.skipped).length,
        skipped: results.filter(r => r.skipped).length,
      };

      return {
        success: true,
        summary,
        results,
        message: `批量重新上传完成，处理了${processedCount}个PDF文件`,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '批量重新上传失败',
      };
    }
  }

  @Get('storage-stats')
  @ApiOperation({ summary: '获取Firebase Storage使用统计' })
  @ApiResponse({
    status: 200,
    description: '返回Firebase Storage使用统计信息',
  })
  async getStorageStats() {
    try {
      const stats = await this.firebaseService.getStorageStats();

      if (stats.success) {
        const totalSizeMB = (stats.totalSize / 1024 / 1024).toFixed(2);

        return {
          ...stats,
          totalSizeMB: `${totalSizeMB} MB`,
          message: `Firebase Storage统计获取成功，共${stats.totalFiles}个文件，总大小${totalSizeMB}MB`,
        };
      } else {
        return stats;
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Firebase Storage统计获取失败',
      };
    }
  }

  @Post('cleanup-old-files')
  @ApiOperation({ summary: '清理超过指定天数的PDF文件' })
  @ApiQuery({ name: 'daysOld', description: '文件超过多少天被认为是过期的（默认90天）', required: false })
  @ApiQuery({ name: 'dryRun', description: '是否为试运行（只检查不删除，默认true）', required: false })
  @ApiResponse({
    status: 200,
    description: '返回清理结果',
  })
  async cleanupOldFiles(
    @Query('daysOld') daysOld?: string,
    @Query('dryRun') dryRun?: string
  ) {
    try {
      const days = parseInt(daysOld || '90', 10);
      const isDryRun = dryRun !== 'false'; // 默认为试运行

      if (days < 30) {
        return {
          success: false,
          error: '为了安全起见，不能删除少于30天的文件',
        };
      }

      const result = await this.firebaseService.cleanupOldFiles(days, isDryRun);

      if (result.success) {
        const savedSpaceMB = (result.savedSpace / 1024 / 1024).toFixed(2);

        return {
          ...result,
          savedSpaceMB: `${savedSpaceMB} MB`,
          message: isDryRun
            ? `试运行完成：找到${result.expiredFiles}个超过${days}天的文件，预计可节省${savedSpaceMB}MB空间`
            : `清理完成：删除了${result.deletedFiles}个文件，节省了${savedSpaceMB}MB空间`,
          warning: isDryRun ? '这是试运行，没有实际删除文件。要执行实际删除，请设置 dryRun=false' : undefined,
        };
      } else {
        return result;
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '文件清理失败',
      };
    }
  }

  @Post('cleanup-by-date')
  @ApiOperation({ summary: '清理指定日期之前的PDF文件' })
  @ApiQuery({ name: 'beforeDate', description: '删除此日期之前的文件（格式：YYYY-MM-DD）' })
  @ApiQuery({ name: 'dryRun', description: '是否为试运行（只检查不删除，默认true）', required: false })
  @ApiResponse({
    status: 200,
    description: '返回清理结果',
  })
  async cleanupByDate(
    @Query('beforeDate') beforeDate: string,
    @Query('dryRun') dryRun?: string
  ) {
    if (!beforeDate) {
      return {
        success: false,
        error: '请提供beforeDate参数（格式：YYYY-MM-DD）',
      };
    }

    try {
      const cutoffDate = new Date(beforeDate);
      if (isNaN(cutoffDate.getTime())) {
        return {
          success: false,
          error: '日期格式无效，请使用YYYY-MM-DD格式',
        };
      }

      // 计算天数差
      const today = new Date();
      const diffTime = today.getTime() - cutoffDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 30) {
        return {
          success: false,
          error: '为了安全起见，不能删除少于30天的文件',
        };
      }

      const isDryRun = dryRun !== 'false';
      const result = await this.firebaseService.cleanupOldFiles(diffDays, isDryRun);

      if (result.success) {
        const savedSpaceMB = (result.savedSpace / 1024 / 1024).toFixed(2);

        return {
          ...result,
          cutoffDate: beforeDate,
          savedSpaceMB: `${savedSpaceMB} MB`,
          message: isDryRun
            ? `试运行完成：找到${result.expiredFiles}个${beforeDate}之前的文件，预计可节省${savedSpaceMB}MB空间`
            : `清理完成：删除了${result.deletedFiles}个${beforeDate}之前的文件，节省了${savedSpaceMB}MB空间`,
          warning: isDryRun ? '这是试运行，没有实际删除文件。要执行实际删除，请设置 dryRun=false' : undefined,
        };
      } else {
        return result;
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '按日期清理失败',
      };
    }
  }

  @Get('scheduler-status')
  @ApiOperation({ summary: '获取定时任务状态' })
  @ApiResponse({
    status: 200,
    description: '返回定时任务配置和状态',
  })
  getSchedulerStatus() {
    try {
      const status = this.firebaseSchedulerService.getSchedulerStatus();
      return {
        success: true,
        ...status,
        message: '定时任务状态获取成功',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '定时任务状态获取失败',
      };
    }
  }

  @Post('trigger-manual-cleanup')
  @ApiOperation({ summary: '手动触发清理任务' })
  @ApiQuery({ name: 'daysOld', description: '文件超过多少天被认为是过期的（默认90天）', required: false })
  @ApiQuery({ name: 'dryRun', description: '是否为试运行（只检查不删除，默认true）', required: false })
  @ApiResponse({
    status: 200,
    description: '返回手动清理结果',
  })
  async triggerManualCleanup(
    @Query('daysOld') daysOld?: string,
    @Query('dryRun') dryRun?: string
  ) {
    try {
      const days = parseInt(daysOld || '90', 10);
      const isDryRun = dryRun !== 'false';

      if (days < 30) {
        return {
          success: false,
          error: '为了安全起见，不能删除少于30天的文件',
        };
      }

      const result = await this.firebaseSchedulerService.triggerManualCleanup(days, isDryRun);

      if (result.success) {
        const savedSpaceMB = (result.savedSpace / 1024 / 1024).toFixed(2);

        return {
          ...result,
          savedSpaceMB: `${savedSpaceMB} MB`,
          message: isDryRun
            ? `手动清理试运行完成：找到${result.expiredFiles}个超过${days}天的文件，预计可节省${savedSpaceMB}MB空间`
            : `手动清理完成：删除了${result.deletedFiles}个文件，节省了${savedSpaceMB}MB空间`,
          warning: isDryRun ? '这是试运行，没有实际删除文件。要执行实际删除，请设置 dryRun=false' : undefined,
        };
      } else {
        return result;
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '手动清理失败',
      };
    }
  }
}
