import 'package:dio/dio.dart';
import 'package:queue_system/models/staff.dart';

class StaffService {
  final Dio _dio;

  StaffService({required String baseUrl}) : _dio = Dio() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
  }

  // 获取员工列表
  Future<List<Staff>> getStaffList() async {
    try {
      final response = await _dio.get('/staff');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => Staff.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load staff list');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return []; // 没有员工数据
      }
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to load staff list: $e');
    }
  }

  // 修改密码
  Future<void> changePassword(int staffId, String newPassword) async {
    try {
      final response = await _dio.put(
        '/staff/$staffId/password',
        data: {
          'password': newPassword,
        },
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to change password');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 400) {
        final errorMessage = e.response?.data['message'] ?? 'Invalid data';
        throw Exception(errorMessage);
      } else if (e.response?.statusCode == 404) {
        throw Exception('Staff not found');
      }
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to change password: $e');
    }
  }

  // 更新员工状态
  Future<void> updateStaffStatus(int staffId, String status) async {
    try {
      final response = await _dio.put(
        '/staff/$staffId/status',
        data: {
          'status': status,
        },
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update staff status');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 400) {
        final errorMessage = e.response?.data['message'] ?? 'Invalid data';
        throw Exception(errorMessage);
      } else if (e.response?.statusCode == 404) {
        throw Exception('Staff not found');
      }
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to update staff status: $e');
    }
  }
}
