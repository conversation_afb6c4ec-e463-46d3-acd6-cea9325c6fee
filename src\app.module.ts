import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ScheduleModule } from '@nestjs/schedule';
import databaseConfig from './config/database.config';
import firebaseConfig from './config/firebase.config';
import { SqlServerMain } from './sql-server/entities/main.entity';
import { SqlServerDetail } from './sql-server/entities/detail.entity';
import { SqlServerScTranDetail } from './sql-server/entities/sc-tran-detail.entity';
import { SqlServerStock } from './stock/entities/stock.entity';
import { SqlServerCustomer } from './sql-server/entities/customer.entity';
import { SqlServerCnMain } from './sql-server/entities/cn-main.entity';
import { SqlServerCnDetail } from './sql-server/entities/cn-detail.entity';
import { Main } from './postgres/entities/main.entity';
import { Detail } from './postgres/entities/detail.entity';
import { CnMain } from './postgres/entities/cn-main.entity';
import { CnDetail } from './postgres/entities/cn-detail.entity';
import { Staff } from './postgres/entities/staff.entity';
import { SyncModule } from './sync/sync.module';
import { StaffController } from './staff/staff.controller';
import { StockModule } from './stock/stock.module';
import { CheckModule } from './check/check.module';
import { NotificationModule } from './notification/notification.module';
import { ShippingModule } from './shipping/shipping.module';
import { PdfModule } from './pdf/pdf.module';
import { StatusModule } from './status/status.module';
import { Notification } from './postgres/entities/notification.entity';
import { StockReturnTracking } from './postgres/entities/stock-return-tracking.entity';
import { EmailModule } from './email/email.module';
import { FirebaseModule } from './firebase/firebase.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, firebaseConfig],
    }),
    ScheduleModule.forRoot(),
    TypeOrmModule.forRoot({
      name: 'sqlServerConnection',
      type: 'mssql',
      host: process.env.SQL_SERVER_HOST,
      port: parseInt(process.env.SQL_SERVER_PORT),
      username: process.env.SQL_SERVER_USERNAME,
      password: process.env.SQL_SERVER_PASSWORD,
      database: process.env.SQL_SERVER_DATABASE,
      entities: [SqlServerMain, SqlServerDetail, SqlServerScTranDetail, SqlServerStock, SqlServerCustomer, SqlServerCnMain, SqlServerCnDetail],
      synchronize: false,
      options: {
        encrypt: false,
        trustServerCertificate: true
      }
    }),
    TypeOrmModule.forRoot({
      name: 'postgresConnection',
      type: 'postgres',
      host: process.env.POSTGRES_HOST,
      port: parseInt(process.env.POSTGRES_PORT),
      username: process.env.POSTGRES_USERNAME,
      password: process.env.POSTGRES_PASSWORD,
      database: process.env.POSTGRES_DATABASE,
      entities: [Main, Detail, CnMain, CnDetail, Staff, Notification, StockReturnTracking],
      synchronize: process.env.NODE_ENV === 'development',
    }),
    TypeOrmModule.forFeature([SqlServerMain, SqlServerDetail, SqlServerScTranDetail, SqlServerStock, SqlServerCustomer, SqlServerCnMain, SqlServerCnDetail], 'sqlServerConnection'),
    TypeOrmModule.forFeature([Main, Detail, CnMain, CnDetail, Staff], 'postgresConnection'),
    SyncModule,
    StockModule,
    CheckModule,
    NotificationModule,
    ShippingModule,
    PdfModule,
    StatusModule,
    EmailModule,
    FirebaseModule
  ],
  controllers: [AppController, StaffController],
  providers: [AppService],
})
export class AppModule { }