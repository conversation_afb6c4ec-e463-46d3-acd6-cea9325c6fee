{"index_exclude_patterns": ["**/node_modules", "**/dist", "**/build", "**/.nest", "**/coverage", "**/logs", "**/uploads"], "index_include_patterns": [], "language_servers": {"typescript": {"enabled": true, "disable_features": []}}, "formatter": {"default_formatter": "prettier", "formatOnSave": true, "formatOnPaste": false, "ignore_patterns": []}, "code_lens": {"references": true, "implementations": true}, "inlay_hints": {"enabled": true, "parameter_hints": true, "type_hints": true}, "linters": {"eslint": {"enabled": true, "autofix_on_save": true}}, "autocomplete": {"format_completion_on_accept": true}, "editor": {"tab_size": 2, "line_wrap": "off", "format_on_save": true}, "terminal": {"default_profile": "powershell"}}