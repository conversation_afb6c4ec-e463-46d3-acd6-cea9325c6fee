import { Controller, Get, Post, Param, Query, NotFoundException, BadRequestException, HttpCode, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SqlServerStock } from './entities/stock.entity';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { CnDetail } from '../postgres/entities/cn-detail.entity';
import { CnMain } from '../postgres/entities/cn-main.entity';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { StockService } from './stock.service';

@ApiTags('Stock')
@Controller('stock')
export class StockController {
  constructor(
    @InjectRepository(SqlServerStock, 'sqlServerConnection')
    private readonly stockRepository: Repository<SqlServerStock>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    @InjectRepository(CnDetail, 'postgresConnection')
    private readonly cnDetailRepository: Repository<CnDetail>,
    @InjectRepository(CnMain, 'postgresConnection')
    private readonly cnMainRepository: Repository<CnMain>,
    private readonly stockService: StockService,
  ) { }

  @ApiOperation({ summary: '获取所有库存信息' })
  @ApiResponse({
    status: 200,
    description: '返回所有库存记录'
  })
  @Get()
  async findAll() {
    try {
      return await this.stockRepository.find();
    } catch (error) {
      console.error('Error fetching stock data:', error);
      throw error;
    }
  }

  @ApiOperation({ summary: '根据库存代码获取库存信息' })
  @ApiParam({ name: 'stockCode', description: '库存代码' })
  @ApiResponse({
    status: 200,
    description: '返回指定库存代码的库存信息'
  })
  @Get(':stockCode')
  async findByCode(@Param('stockCode') stockCode: string) {
    try {
      const stock = await this.stockRepository.findOne({
        where: { StockCode: stockCode }
      });

      if (!stock) {
        throw new NotFoundException(`Stock with code ${stockCode} not found`);
      }

      return stock;
    } catch (error) {
      console.error(`Error fetching stock data for code ${stockCode}:`, error);
      throw error;
    }
  }

  @ApiOperation({ summary: '获取所有货架号信息' })
  @ApiResponse({
    status: 200,
    description: '返回所有不同的货架号列表'
  })
  @Get('shelves/all')
  async getAllShelves() {
    try {
      const stocks = await this.stockRepository.find();
      // 提取所有不同的货架号
      const shelves = [...new Set(stocks.map(stock => stock.BinShelfNo))].filter(shelf => shelf);

      return {
        total: shelves.length,
        shelves: shelves
      };
    } catch (error) {
      console.error('Error fetching shelf data:', error);
      throw error;
    }
  }

  @ApiOperation({ summary: '根据货架号获取库存' })
  @ApiParam({ name: 'shelfNo', description: '货架号' })
  @ApiResponse({
    status: 200,
    description: '返回指定货架号的库存列表'
  })
  @Get('shelf/:shelfNo')
  async findByShelf(@Param('shelfNo') shelfNo: string) {
    try {
      const stocks = await this.stockRepository.find({
        where: { BinShelfNo: shelfNo }
      });

      if (stocks.length === 0) {
        throw new NotFoundException(`No stock found for shelf ${shelfNo}`);
      }

      return {
        total: stocks.length,
        shelf: shelfNo,
        stocks: stocks
      };
    } catch (error) {
      console.error(`Error fetching stock data for shelf ${shelfNo}:`, error);
      throw error;
    }
  }

  @ApiOperation({ summary: '获取待退回库存列表' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '返回待退回库存列表'
  })
  @Get('return/pending')
  async getPendingReturnStocks(@Query('staffId') staffId: number) {
    if (!staffId) {
      throw new BadRequestException('Staff ID is required');
    }

    try {
      return await this.stockService.getPendingReturnStocks(parseInt(staffId.toString()));
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get pending return stocks: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '确认库存已退回' })
  @ApiQuery({ name: 'detailId', description: 'CN明细记录ID' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '确认库存退回成功，返回操作结果'
  })
  @HttpCode(200)
  @Post('return/confirm')
  async confirmStockReturn(
    @Query('detailId') detailId: number,
    @Query('staffId') staffId: number
  ) {
    if (!detailId) {
      throw new BadRequestException('Detail ID is required');
    }

    if (!staffId) {
      throw new BadRequestException('Staff ID is required');
    }

    try {
      return await this.stockService.confirmStockReturn(
        parseInt(detailId.toString()),
        parseInt(staffId.toString())
      );
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Failed to confirm stock return: ${error.message}`);
    }
  }

  @ApiOperation({ summary: '库存归位' })
  @ApiQuery({ name: 'documentNo', description: '单据编号' })
  @ApiQuery({ name: 'line', description: '明细行号' })
  @ApiQuery({ name: 'staffId', description: '员工ID' })
  @ApiResponse({
    status: 200,
    description: '库存归位成功，返回操作结果'
  })
  @HttpCode(200)
  @Post('return')
  async returnStock(
    @Query('documentNo') documentNo: string,
    @Query('line') line: number,
    @Query('staffId') staffId: number
  ) {
    if (!documentNo) {
      throw new BadRequestException('Document number is required');
    }

    if (!line) {
      throw new BadRequestException('Line number is required');
    }

    if (!staffId) {
      throw new BadRequestException('Staff ID is required');
    }

    try {
      const result = await this.stockService.returnStock(documentNo, parseInt(line.toString()), parseInt(staffId.toString()));
      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to return stock: ${error.message}`);
    }
  }
}