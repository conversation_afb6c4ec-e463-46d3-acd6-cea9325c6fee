// PM2配置模板 - 复制此文件并根据你的环境修改
module.exports = {
  apps: [
    {
      name: 'backend-nestjs-{ENVIRONMENT}',  // 替换 {ENVIRONMENT} 为 dev/staging/prod
      script: 'dist/main.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',  // 根据服务器内存调整
      env: {
        NODE_ENV: '{ENVIRONMENT}',  // development/staging/production
        PORT: 3000,  // 根据需要修改端口
        
        // 🔧 路径配置 - 必须修改
        PWD: '{PROJECT_ROOT_PATH}',  // 项目根目录绝对路径
        PDF_OUTPUT_DIR: '{PROJECT_ROOT_PATH}/PDF_Output',  // PDF输出目录
        
        // 🗄️ SQL Server配置 - 必须修改
        SQL_SERVER_HOST: '{SQL_SERVER_HOST}',
        SQL_SERVER_PORT: '1433',
        SQL_SERVER_USERNAME: '{SQL_USERNAME}',
        SQL_SERVER_PASSWORD: '{SQL_PASSWORD}',
        SQL_SERVER_DATABASE: '{SQL_DATABASE}',
        
        // 🐘 PostgreSQL配置 - 必须修改
        POSTGRES_HOST: '{POSTGRES_HOST}',
        POSTGRES_PORT: '5432',
        POSTGRES_USERNAME: '{POSTGRES_USERNAME}',
        POSTGRES_PASSWORD: '{POSTGRES_PASSWORD}',
        POSTGRES_DATABASE: '{POSTGRES_DATABASE}',
        
        // 📧 邮件配置 - 必须修改
        EMAIL_HOST: '{EMAIL_HOST}',
        EMAIL_PORT: '{EMAIL_PORT}',
        EMAIL_SECURE: '{EMAIL_SECURE}',  // true/false
        EMAIL_USER: '{EMAIL_USER}',
        EMAIL_PASS: '{EMAIL_PASS}',
        EMAIL_FROM: '{EMAIL_FROM}'
      },
      
      // 🔧 工作目录 - 必须修改
      cwd: '{PROJECT_ROOT_PATH}',
      
      // 📝 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 🔄 重启配置
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      
      // ⚙️ 进程管理
      kill_timeout: 5000,
      listen_timeout: 8000,
      
      // 🪟 Windows特定配置（如果是Windows服务器）
      windowsHide: true,
      
      // 📁 忽略监听文件变化
      ignore_watch: [
        'node_modules',
        'logs',
        'PDF_Output',
        '.git'
      ],
      
      merge_logs: true,
      time: true
    }
  ]
};

/*
🔧 配置说明：

1. 替换所有 {PLACEHOLDER} 为实际值
2. 路径使用：
   - Windows: D:\\Project\\QueueSystem\\backend_nestjs
   - Linux: /var/www/backend_nestjs
3. 数据库密码包含特殊字符时用引号包围
4. 邮件端口：465(SSL) 或 587(TLS)
5. 根据服务器性能调整 max_memory_restart

📋 快速配置检查清单：
□ 项目路径正确
□ 数据库连接信息正确
□ 邮件配置正确
□ 端口未被占用
□ 目录权限正确
□ 防火墙规则配置
*/
