import 'package:json_annotation/json_annotation.dart';

part 'shipping_document.g.dart';

@JsonSerializable()
class ShippingDocument {
  final int id;

  @J<PERSON><PERSON><PERSON>(name: 'document_no')
  final String documentNo;

  @J<PERSON><PERSON><PERSON>(name: 'document_date')
  final DateTime documentDate;

  @Json<PERSON><PERSON>(name: 'customer')
  final String customerCode;

  @Json<PERSON>ey(name: 'customer_name')
  final String customerName;

  @Json<PERSON>ey(name: 'customer_email')
  final String? customerEmail;

  @<PERSON>son<PERSON><PERSON>(name: 'customer_deliver_address')
  final String? customerDeliverAddress;

  @<PERSON>son<PERSON>ey(name: 'customer_telephone')
  final String? customerTelephone;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'customer_fax')
  final String? customerFax;

  @Json<PERSON><PERSON>(name: 'salesman_code')
  final String? salesmanCode;

  @Json<PERSON>ey(name: 'issue_by')
  final String? issueBy;

  @Json<PERSON>ey(name: 'transporter_code')
  final String? transporterCode;

  @<PERSON>son<PERSON><PERSON>(name: 'transporter_name')
  final String? transporterName;

  final String remarks;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_shipped')
  final bool isShipped;

  @J<PERSON><PERSON><PERSON>(name: 'shipped_at')
  final DateTime? shippedAt;

  @JsonKey(name: 'shipped_by')
  final int? shippedBy;

  final bool? delivered;

  @JsonKey(name: 'delivered_at')
  final String? deliveredAt;

  @JsonKey(name: 'delivered_by')
  final int? deliveredBy;

  @JsonKey(name: 'delivery_notes')
  final String? deliveryNotes;

  @JsonKey(name: 'driver_id')
  final int? driverId;

  @JsonKey(name: 'rejection_reason')
  final String? rejectionReason;

  @JsonKey(name: 'rejected_by')
  final int? rejectedBy;

  @JsonKey(name: 'rejected_at')
  final String? rejectedAt;

  @JsonKey(name: 'driver_username')
  final String? driverUsername;

  final int priority;

  @JsonKey(name: 'ready_to_ship')
  final bool? readyToShip;

  @JsonKey(name: 'unconfirmed_items')
  final List<int>? unconfirmedItems;

  ShippingDocument({
    required this.id,
    required this.documentNo,
    required this.documentDate,
    required this.customerCode,
    required this.customerName,
    this.customerEmail,
    this.customerDeliverAddress,
    this.customerTelephone,
    this.customerFax,
    this.salesmanCode,
    this.issueBy,
    this.transporterCode,
    this.transporterName,
    required this.remarks,
    required this.createdAt,
    required this.isShipped,
    this.shippedAt,
    this.shippedBy,
    this.delivered,
    this.deliveredAt,
    this.deliveredBy,
    this.deliveryNotes,
    this.driverId,
    this.rejectionReason,
    this.rejectedBy,
    this.rejectedAt,
    this.driverUsername,
    this.priority = 0,
    this.readyToShip,
    this.unconfirmedItems,
  });

  factory ShippingDocument.fromJson(Map<String, dynamic> json) =>
      _$ShippingDocumentFromJson(json);

  Map<String, dynamic> toJson() => _$ShippingDocumentToJson(this);
}
