---
description:
globs:
alwaysApply: false
---
# 队列系统 Flutter 移动应用

## 项目描述
这是一个基于Flutter框架构建的移动应用，作为队列系统的前端界面，主要用于仓库员工检查、主管确认、BOM专员审核和司机送货等操作。应用通过REST API与NestJS后端进行通信，支持多语言界面（中文、英文和马来语）。

## 项目结构
- `lib/` - 源代码目录
  - `main.dart` - 应用程序入口点
  - `models/` - 数据模型定义
  - `providers/` - 状态管理和业务逻辑
  - `screens/` - 用户界面屏幕
  - `services/` - API服务和网络请求
  - `utils/` - 工具类和辅助函数
  - `widgets/` - 可复用UI组件
  - `l10n/` - 本地化和翻译

## 主要功能模块
1. **登录模块** - 员工身份验证和权限控制
2. **检查列表模块** - 员工和主管查看待检查物品列表
3. **物品检查模块** - 员工检查、BOM专员审核、主管确认物品
4. **订单出货模块** - 主管出货和司机送货确认
5. **通知系统模块** - 处理系统通知和拒绝反馈

## 技术栈
- Flutter - 跨平台UI框架
- Provider - 状态管理
- Dio - HTTP客户端
- SharedPreferences - 本地存储
- Intl - 国际化与本地化

## 支持平台
- Android
- iOS
- Web

## 版本信息
- 当前版本: 1.0.0+1
