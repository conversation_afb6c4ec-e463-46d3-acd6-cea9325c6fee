import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/models/shipping_document.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/providers/check_list_provider.dart';
import 'package:queue_system/screens/signature_screen.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/services/pdf_service.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/screens/native_pdf_viewer_screen.dart';
import 'package:queue_system/widgets/email_dialog.dart';
import 'package:queue_system/widgets/pdf_share_options_dialog.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';

class ToShipTab extends StatefulWidget {
  final int staffId;
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;
  final Function? onDataChanged; // 添加回调函数，用于通知父组件数据变化

  const ToShipTab({
    super.key,
    required this.staffId,
    required this.onConfirmAction,
    this.onDataChanged,
  });

  @override
  State<ToShipTab> createState() => _ToShipTabState();
}

class _ToShipTabState extends State<ToShipTab> {
  // 搜索相关状态
  String _searchQuery = '';
  Timer? _searchDebounceTimer;

  @override
  void dispose() {
    // 清理防抖定时器
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 立即更新搜索查询状态（用于UI显示）
    setState(() {
      _searchQuery = query;
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    setState(() {
      _searchQuery = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 搜索栏
        SearchBarWidget(
          hintText: context.t('search_documents'),
          onSearchChanged: _onSearchChanged,
          onClear: _onSearchCleared,
          initialValue: _searchQuery,
        ),
        // 文档列表
        Expanded(
          child: DocumentListWidget(
            staffId: widget.staffId,
            searchQuery: _searchQuery,
            searchDebounceTimer: _searchDebounceTimer,
            onConfirmAction: widget.onConfirmAction,
            onDataChanged: widget.onDataChanged,
          ),
        ),
      ],
    );
  }
}

// 独立的文档列表Widget
class DocumentListWidget extends StatefulWidget {
  final int staffId;
  final String searchQuery;
  final Timer? searchDebounceTimer;
  final Function(
    BuildContext context,
    String title,
    String content,
    Future<void> Function() action,
  ) onConfirmAction;
  final Function? onDataChanged;

  const DocumentListWidget({
    super.key,
    required this.staffId,
    required this.searchQuery,
    required this.searchDebounceTimer,
    required this.onConfirmAction,
    this.onDataChanged,
  });

  @override
  State<DocumentListWidget> createState() => _DocumentListWidgetState();
}

class _DocumentListWidgetState extends State<DocumentListWidget> {
  final _checkService = CheckService(baseUrl: AppConfig.baseUrl);
  final _pdfService = PdfService();
  List<ShippingDocument> _readyDocuments = [];
  List<ShippingDocument> _allDocuments = []; // 存储所有数据用于本地筛选
  List<Map<String, dynamic>> _drivers = [];
  bool _isLoading = true;
  String? _error;
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    _loadReadyDocuments(isRefresh: true);
    _loadDrivers();
  }

  @override
  void didUpdateWidget(DocumentListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当搜索查询改变时，执行防抖搜索
    if (widget.searchQuery != oldWidget.searchQuery) {
      _handleSearchQueryChange();
    }
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 处理搜索查询变化
  void _handleSearchQueryChange() {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 设置新的防抖定时器
    _searchDebounceTimer = Timer(const Duration(seconds: 1), () {
      // 1秒后执行搜索
      _performSearch();
    });
  }

  // 执行实际的搜索操作
  void _performSearch() {
    // 应用本地搜索筛选
    _applyLocalSearch();
  }

  Future<void> _loadReadyDocuments({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoading = true;
        _error = null;
        // 分页相关变量已移除，无需设置
        _readyDocuments.clear();
      });
    }

    try {
      // 使用简化的 API 获取所有未出货文档
      final response = await _checkService.getAllUnshippedDocuments(
        widget.staffId,
      );

      final List<ShippingDocument> documents =
          response['data'] as List<ShippingDocument>;

      setState(() {
        _allDocuments = documents;
        // 应用本地搜索筛选
        _applyLocalSearch();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      rethrow;
    }
  }

  // 应用本地搜索筛选
  void _applyLocalSearch() {
    if (widget.searchQuery.trim().isEmpty) {
      // 没有搜索条件，显示所有数据
      _readyDocuments = List.from(_allDocuments);
    } else {
      // 有搜索条件，进行本地筛选
      final query = widget.searchQuery.toLowerCase();

      _readyDocuments = _allDocuments.where((doc) {
        return doc.documentNo.toLowerCase().contains(query) ||
            doc.customerCode.toLowerCase().contains(query) ||
            doc.customerName.toLowerCase().contains(query) ||
            doc.remarks.toLowerCase().contains(query);
      }).toList();
    }
  }

  Future<void> _loadDrivers() async {
    try {
      final drivers = await _checkService.getDrivers();
      setState(() {
        _drivers = drivers;
      });
    } catch (e) {
      // 加载司机列表错误，忽略
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(context.t('loading_failed')),
            const SizedBox(height: 8),
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadReadyDocuments(isRefresh: true),
              child: Text(context.t('retry')),
            ),
          ],
        ),
      );
    }

    if (_readyDocuments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.check_circle_outline,
                size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(widget.searchQuery.isNotEmpty
                ? context.t('no_search_results')
                : context.t('no_ready_documents')),
            if (widget.searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${context.t('search_keyword')}: "${widget.searchQuery}"',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadReadyDocuments(isRefresh: true),
              child: Text(context.t('refresh')),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 显示搜索结果统计
        if (widget.searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(
                  '${context.t('search_results')}: ${_readyDocuments.length} 条结果',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        // 文档列表
        Expanded(child: _buildDocumentsList()),
      ],
    );
  }

  Widget _buildDocumentsList() {
    return RefreshIndicator(
      onRefresh: () => _loadReadyDocuments(isRefresh: true),
      child: ListView.builder(
        itemCount: _readyDocuments.length,
        itemBuilder: (context, index) {
          final document = _readyDocuments[index];
          return _buildDocumentCard(document);
        },
      ),
    );
  }

  Widget _buildDocumentCard(ShippingDocument document) {
    // 格式化创建时间
    final createdAt = _formatDate(document.createdAt);

    // 检查是否为紧急订单
    final isUrgent = document.priority > 0;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isUrgent ? Colors.red.shade50 : null,
      child: Container(
        decoration: isUrgent
            ? BoxDecoration(
                border: Border(left: BorderSide(color: Colors.red, width: 4)),
                borderRadius: BorderRadius.circular(4),
              )
            : null,
        child: ExpansionTile(
          title: LayoutBuilder(
            builder: (context, constraints) {
              // 获取屏幕宽度
              final screenWidth = MediaQuery.of(context).size.width;
              // 根据屏幕宽度调整布局
              final isSmallScreen = screenWidth < 360;

              return isSmallScreen
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                '${document.customerCode} - ${document.customerName}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${context.t('document_no')}: ${document.documentNo}',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500, fontSize: 13),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Created: $createdAt',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.blue.shade800,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${document.customerCode} - ${document.customerName}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              Text(
                                '${context.t('document_no')}: ${document.documentNo}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Created: $createdAt',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue.shade800,
                            ),
                          ),
                        ),
                      ],
                    );
            },
          ),
          subtitle: Row(
            children: [
              Expanded(
                child: Text(
                    'Document Date: ${_formatDate(document.documentDate)}'),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: (document.readyToShip ?? false)
                      ? Colors.green
                      : Colors.orange,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  (document.readyToShip ?? false)
                      ? context.t('ready_to_ship')
                      : context.t('not_ready'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          // 文档组默认不展开
          initiallyExpanded: false,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (document.remarks.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child:
                          Text('${context.t('remarks')}: ${document.remarks}'),
                    ),
                  Row(
                    children: [
                      // 只有主管才能看到Reject按钮，且只有准备好出货的单据才显示Reject按钮
                      if ((Provider.of<AuthProvider>(context, listen: false)
                                      .currentStaff
                                      ?.level ==
                                  'supervisor' ||
                              Provider.of<AuthProvider>(context, listen: false)
                                      .currentStaff
                                      ?.level ==
                                  'admin') &&
                          (document.readyToShip ?? false))
                        ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            minimumSize: const Size(0, 48),
                          ),
                          onPressed: () => _showRejectDialog(document),
                          icon: const Icon(Icons.cancel, color: Colors.white),
                          label: Text(
                            context.t('reject'),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: (document.readyToShip ?? false)
                                ? Colors.green
                                : Colors.grey,
                            minimumSize: const Size(0, 48),
                          ),
                          onPressed: (document.readyToShip ?? false)
                              ? () => _showShipmentDialog(document)
                              : null,
                          icon: Icon(Icons.local_shipping,
                              color: (document.readyToShip ?? false)
                                  ? Colors.white
                                  : Colors.grey[600]),
                          label: Text(
                            context.t('ship'),
                            style: TextStyle(
                              color: (document.readyToShip ?? false)
                                  ? Colors.white
                                  : Colors.grey[600],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示出货对话框
  Future<void> _showShipmentDialog(ShippingDocument document) async {
    // 获取当前用户信息
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isDriver = authProvider.currentStaff?.level == 'driver';

    // 默认为null表示不指派司机
    int? selectedDriverId;
    String? selectedDriverName;

    // 如果是司机，自动指派自己
    if (isDriver) {
      selectedDriverId = widget.staffId;
      // 查找司机名称
      if (_drivers.isNotEmpty) {
        final driver = _drivers.firstWhere(
          (d) => d['id'] == selectedDriverId,
          orElse: () => {'username': authProvider.currentStaff?.username ?? ''},
        );
        selectedDriverName = driver['username'] as String;
      } else {
        selectedDriverName = authProvider.currentStaff?.username;
      }

      // 直接显示确认对话框，不需要选择司机
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(context.t('confirm_shipment')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${context.t('document')}: ${document.documentNo}'),
              const SizedBox(height: 16),
              Text(
                  '${context.t('driver')}: ${selectedDriverName ?? context.t('you')}'),
              const SizedBox(height: 8),
              Text(
                context.t('auto_assign_driver_message'),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(context.t('cancel')),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(context.t('confirm')),
            ),
          ],
        ),
      );

      // 更新confirmed变量，以便后续处理
      return _processShipmentConfirmation(
          confirmed, document, selectedDriverId);
    }

    // 如果有特殊transporter，预选特殊transporter选项
    if (document.transporterName != null &&
        document.transporterName!.isNotEmpty) {
      selectedDriverId = -1; // 使用-1作为特殊transporter的ID
      selectedDriverName = document.transporterName;
    }

    // 显示司机选择对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(context.t('confirm_shipment')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${context.t('document')}: ${document.documentNo}'),
              const SizedBox(height: 16),
              Text(context.t('assign_driver')),
              const SizedBox(height: 8),
              Builder(
                builder: (context) {
                  // 构建下拉框选项列表
                  final List<DropdownMenuItem<int?>> dropdownItems = [
                    DropdownMenuItem<int?>(
                      value: null,
                      child: Text(context.t('no_driver_assigned')),
                    ),
                    // 如果有特殊transporter，添加特殊选项
                    if (document.transporterName != null &&
                        document.transporterName!.isNotEmpty)
                      DropdownMenuItem<int?>(
                        value: -1,
                        child: Text(
                          document.transporterName!,
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ..._drivers.map((driver) {
                      return DropdownMenuItem<int?>(
                        value: driver['id'] as int,
                        child: Text(driver['username'] as String),
                      );
                    }),
                  ];

                  // 验证当前选中的值是否在选项列表中
                  final validValues =
                      dropdownItems.map((item) => item.value).toSet();
                  final validatedValue = validValues.contains(selectedDriverId)
                      ? selectedDriverId
                      : null;

                  return DropdownButtonFormField<int?>(
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: validatedValue,
                    hint: Text(context.t('no_driver_assigned')),
                    isExpanded: true,
                    items: dropdownItems,
                    onChanged: (int? driverId) {
                      setState(() {
                        selectedDriverId = driverId;
                        if (driverId == -1) {
                          // 选择了特殊transporter
                          selectedDriverName = document.transporterName;
                        } else if (driverId != null) {
                          // 选择了普通司机
                          final driver = _drivers.firstWhere(
                              (d) => d['id'] == driverId,
                              orElse: () => {'username': ''});
                          selectedDriverName = driver['username'] as String;
                        } else {
                          // 选择了no driver assigned
                          selectedDriverName = null;
                        }
                      });
                    },
                  );
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(context.t('cancel')),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(context.t('confirm')),
            ),
          ],
        ),
      ),
    );

    // 处理确认结果
    return _processShipmentConfirmation(confirmed, document, selectedDriverId);
  }

  // 处理出货确认结果
  Future<void> _processShipmentConfirmation(
      bool? confirmed, ShippingDocument document, int? selectedDriverId) async {
    if (confirmed == true) {
      try {
        // 使用try-catch包装API调用
        try {
          // 添加调试输出，查看 selectedDriverId 的值
          //print("DEBUG: selectedDriverId = $selectedDriverId");

          //print("DEBUG: 开始调用 completeDocument API");
          Map<String, dynamic> response;
          try {
            // 如果选择的是特殊transporter（ID为-1），则传递null给后端
            final driverIdToSend =
                selectedDriverId == -1 ? null : selectedDriverId;

            response = await _checkService.completeDocument(
              document.documentNo,
              widget.staffId,
              driverIdToSend,
            );
            //print("DEBUG: completeDocument API 调用成功");
            // 添加调试输出，查看 response 的内容
            //print("DEBUG: response = $response");
          } catch (e) {
            //print("DEBUG: completeDocument API 调用失败: $e");
            rethrow; // 重新抛出异常，让外层 catch 块处理
          }
          //print("DEBUG: pdfPath = ${response['pdfPath']}");
          //print("DEBUG: pdfPath type = ${response['pdfPath']?.runtimeType}");
          //print("DEBUG: pdfPath null check = ${response['pdfPath'] != null}");
          //print(
          //    "DEBUG: selectedDriverId null check = ${selectedDriverId == null}");
          //print(
          //    "DEBUG: combined condition = ${response['pdfPath'] != null && selectedDriverId == null}");

          // 检查是否需要签名
          // 只有选择"no driver assigned"时才需要签名
          // 选择了任何司机（包括特殊transporter）都跳过签名
          if (selectedDriverId == null) {
            //print("DEBUG: 没有选择司机，需要签名");
            // 确保 pdfPath 不为 null
            final pdfPath = response['pdfPath'] as String? ?? "";

            // 如果组件已卸载，则不继续处理
            if (!mounted) return;

            // 打开签名页面
            final signatureBase64 = await Navigator.push<String>(
              context,
              MaterialPageRoute(
                builder: (context) => SignatureScreen(
                  documentNo: document.documentNo,
                ),
              ),
            );

            // 签名页面现在总是返回字符串（可能为空），不会返回null
            // 如果用户取消了签名，则仍然显示PDF生成成功的提示
            if (signatureBase64 == null) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        Expanded(
                          child: Text('PDF已生成并保存到服务器: $pdfPath'),
                        ),
                        TextButton(
                          onPressed: () {
                            // 显示操作选项对话框
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(context.t('pdf_options')),
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ListTile(
                                      leading: const Icon(Icons.picture_as_pdf),
                                      title: Text(context.t('view_pdf')),
                                      onTap: () {
                                        Navigator.pop(context);
                                        // 获取PDF查看URL
                                        final pdfUrl = _checkService
                                            .getPdfViewUrl(document.documentNo);
                                        // 打开PDF查看页面
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                NativePdfViewerScreen(
                                              pdfUrl: pdfUrl,
                                              title:
                                                  '${document.documentNo} PDF',
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    ListTile(
                                      leading: const Icon(Icons.email),
                                      title: Text(context.t('send_pdf')),
                                      onTap: () {
                                        Navigator.pop(context);
                                        _sendPdfByEmail(document);
                                      },
                                    ),
                                  ],
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(context.t('close')),
                                  ),
                                ],
                              ),
                            );
                          },
                          child: Text(
                            context.t('view'),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                    duration: const Duration(seconds: 10),
                    backgroundColor: Colors.green,
                  ),
                );
              }
              return;
            }

            // 显示处理中对话框
            if (mounted) {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  content: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              );
            }

            try {
              // 上传签名并添加到PDF
              await _pdfService.addSignatureToPdf(
                  document.documentNo, signatureBase64);

              // 关闭处理中对话框
              if (mounted) {
                // 使用try-catch包装，避免找不到对话框的错误
                try {
                  Navigator.of(context).pop();
                } catch (e) {
                  // 对话框可能已经关闭
                }

                // 立即显示Email选项对话框
                _showEmailOptionsDialog(document);
              }
            } catch (e) {
              // 关闭处理中对话框
              if (mounted) {
                Navigator.of(context).pop();

                // 显示错误提示
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('${context.t('error_processing_signature')}: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          } else {
            // 跳过签名的情况 - 选择了司机（包括特殊transporter）
            //print("DEBUG: 选择了司机，跳过签名");
            if (selectedDriverId == -1) {
              // 选择了特殊transporter：跳过签名但显示email选项
              //print("DEBUG: 特殊transporter，显示email选项");
              if (mounted) {
                _showEmailOptionsDialog(document);
              }
            } else {
              // 选择了普通司机：跳过签名，直接完成出货
              //print("DEBUG: 普通司机，直接完成出货");
              if (mounted) {
                final selectedDriver = _drivers.firstWhere(
                  (driver) => driver['id'] == selectedDriverId,
                  orElse: () => {'username': '未知司机'},
                );

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('出货完成 - 已指派给司机: ${selectedDriver['username']}'),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            }
          }

          // API调用成功，对于普通司机直接从列表中移除该文档
          // 对于特殊transporter和no driver assigned，会在email处理完成后移除
          if (mounted && selectedDriverId != null && selectedDriverId != -1) {
            // 只有选择了普通司机才立即移除文档
            setState(() {
              _readyDocuments
                  .removeWhere((doc) => doc.documentNo == document.documentNo);
            });

            // 通知父组件数据已变化，需要更新计数
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (widget.onDataChanged != null) {
                widget.onDataChanged!();
              }
            });
          }
        } catch (apiError) {
          // 添加调试信息
          //print("DEBUG: 外层 catch 块捕获到异常: $apiError");

          // 检查错误消息是否表明操作实际上是成功的
          String errorMsg = apiError.toString().toLowerCase();
          //print("DEBUG: 错误消息: $errorMsg");

          if (errorMsg.contains('has been marked as shipped') ||
              errorMsg.contains('已标记为已出货') ||
              errorMsg.contains('success')) {
            //print("DEBUG: 操作实际上是成功的，从列表中移除文档");
            // 虽然抛出异常，但实际上操作成功了
            if (mounted) {
              setState(() {
                _readyDocuments.removeWhere(
                    (doc) => doc.documentNo == document.documentNo);
              });

              // 通知父组件数据已变化，需要更新计数
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (widget.onDataChanged != null) {
                  widget.onDataChanged!();
                }
              });
            }
          } else {
            //print("DEBUG: 真正的错误，需要刷新整个列表");
            // 真正的错误，需要刷新整个列表
            if (mounted) {
              await _loadReadyDocuments();
            }

            // 显示错误提示
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('出货操作失败: $apiError'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } catch (e) {
        // 这里捕获的是除了API直接错误之外的其他错误
        //print("DEBUG: 最外层 catch 块捕获到异常: $e");

        // 显示错误提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('出货操作发生未知错误: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // 格式化日期
  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  // 显示拒绝对话框
  Future<void> _showRejectDialog(ShippingDocument document) async {
    // 创建一个TextEditingController用于输入拒绝原因
    final reasonController = TextEditingController();

    // 显示拒绝确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('reject_shipment')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${context.t('document')}: ${document.documentNo}'),
            const SizedBox(height: 16),
            Text(context.t('reject_reason')),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                border: const OutlineInputBorder(),
                hintText: context.t('enter_reject_reason'),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            Text(
              context.t('reject_shipment_warning'),
              style: TextStyle(
                fontSize: 12,
                color: Colors.red.shade700,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.t('cancel')),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              context.t('confirm'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    // 如果用户确认拒绝
    if (confirmed == true && mounted) {
      // 保存需要的本地变量，避免在异步操作中使用BuildContext
      final noReasonProvided = context.t('no_reason_provided');
      final reason = reasonController.text.isEmpty
          ? noReasonProvided
          : reasonController.text;
      final documentNoToRemove = document.documentNo;

      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          content: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );

      try {
        // 直接执行拒绝操作
        await _checkService.rejectShipment(
          documentNoToRemove,
          widget.staffId,
          reason,
        );

        // 关闭加载指示器
        if (mounted) {
          Navigator.of(context).pop();
        }

        // 操作成功后，从列表中移除该文档
        if (mounted) {
          setState(() {
            _readyDocuments
                .removeWhere((doc) => doc.documentNo == documentNoToRemove);
          });

          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('operation_success')),
              backgroundColor: Colors.green,
            ),
          );

          // 通知父组件数据已变化，需要更新计数
          if (widget.onDataChanged != null) {
            widget.onDataChanged!();
          }

          // 刷新全局数据，确保to_confirm页面能够显示被reject的文档
          final checkListProvider =
              Provider.of<CheckListProvider>(context, listen: false);
          final authProvider =
              Provider.of<AuthProvider>(context, listen: false);

          // 刷新数据
          checkListProvider.loadCheckList(
            widget.staffId,
            currentStaff: authProvider.currentStaff,
          );
        }
      } catch (e) {
        // 关闭加载指示器
        if (mounted) {
          Navigator.of(context).pop();
        }

        // 显示错误消息
        if (mounted) {
          String errorMessage = e.toString();

          // 检查错误消息是否表明操作实际上是成功的
          if (errorMessage.toLowerCase().contains('has been rejected') ||
              errorMessage.toLowerCase().contains('已被拒绝') ||
              errorMessage.toLowerCase().contains('已重置') ||
              errorMessage.toLowerCase().contains('reset') ||
              errorMessage.toLowerCase().contains('success')) {
            // 虽然抛出异常，但实际上操作成功了
            setState(() {
              _readyDocuments
                  .removeWhere((doc) => doc.documentNo == documentNoToRemove);
            });

            // 显示成功消息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(context.t('operation_success')),
                backgroundColor: Colors.green,
              ),
            );

            // 通知父组件数据已变化，需要更新计数
            if (widget.onDataChanged != null) {
              widget.onDataChanged!();
            }

            // 刷新全局数据
            final checkListProvider =
                Provider.of<CheckListProvider>(context, listen: false);
            final authProvider =
                Provider.of<AuthProvider>(context, listen: false);

            checkListProvider.loadCheckList(
              widget.staffId,
              currentStaff: authProvider.currentStaff,
            );
          } else {
            // 真正的错误，显示错误消息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text('${context.t('operation_failed')}: $errorMessage'),
                backgroundColor: Colors.red,
              ),
            );

            // 重新加载数据
            _loadReadyDocuments();
          }
        }
      }
    }
  }

  // 显示Email选项对话框
  Future<void> _showEmailOptionsDialog(ShippingDocument document) async {
    if (!mounted) return;

    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => PdfShareOptionsDialog(
        documentNo: document.documentNo,
        defaultEmail: document.customerEmail,
        defaultPhoneNumber: document.customerTelephone,
        onSendEmail: (email) {
          Navigator.of(context).pop(); // 关闭对话框
          _sendPdfByEmail(document, email);

          // 从列表中移除该文档
          _removeDocumentFromList(document);
        },
        onSkipEmail: () {
          Navigator.of(context).pop(); // 关闭对话框
          // 显示成功提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('pdf_created_successfully')),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );

          // 从列表中移除该文档
          _removeDocumentFromList(document);
        },
        onViewPdf: (pdfUrl) {
          Navigator.of(context).pop(); // 关闭对话框
          // 打开PDF查看页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NativePdfViewerScreen(
                pdfUrl: pdfUrl,
                title: '${document.documentNo} PDF',
              ),
            ),
          ).then((_) {
            // 当PDF查看页面关闭后，再次显示Email选项对话框
            _showEmailOptionsDialog(document);
          });
        },
      ),
    );
  }

  // 从列表中移除文档并通知父组件数据变化
  void _removeDocumentFromList(ShippingDocument document) {
    if (mounted) {
      setState(() {
        _readyDocuments
            .removeWhere((doc) => doc.documentNo == document.documentNo);
      });

      // 通知父组件数据已变化，需要更新计数
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.onDataChanged != null) {
          widget.onDataChanged!();
        }
      });
    }
  }

  // 通过邮件发送PDF
  Future<void> _sendPdfByEmail(ShippingDocument document,
      [String? prefilledEmail]) async {
    // 如果没有预填充的邮箱，则显示邮件输入对话框
    String? email = prefilledEmail;
    email ??= await showDialog<String>(
      context: context,
      builder: (context) => EmailDialog(
        documentNo: document.documentNo,
        defaultEmail: document.customerEmail,
      ),
    );

    // 如果用户取消了操作，email将为null
    if (email == null) return;

    // 显示加载指示器
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(context.t('sending_email')),
          ],
        ),
      ),
    );

    try {
      // 发送邮件
      final result = await _pdfService.sendPdfEmail(
        document.documentNo,
        email,
        subject: 'Delivery Order: ${document.documentNo}',
        text:
            'Please find the attached delivery order PDF for document ${document.documentNo}.',
      );

      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示结果
      if (mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('email_sent_successfully')),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${context.t('email_sent_failed')}: ${result['message']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('email_sent_failed')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
