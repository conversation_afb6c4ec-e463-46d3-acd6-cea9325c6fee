import 'package:flutter/foundation.dart';
import 'package:queue_system/models/stock_return_tracking_model.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/services/check_service.dart';

class StockReturnTrackingProvider with ChangeNotifier {
  final CheckService _checkService;
  final AuthProvider _authProvider;

  List<StockReturnTrackingModel> _pendingReturns = [];
  List<StockReturnTrackingModel> _completedReturns = [];
  bool _isLoading = false;
  String? _error;

  StockReturnTrackingProvider({
    required CheckService checkService,
    required AuthProvider authProvider,
  })  : _checkService = checkService,
        _authProvider = authProvider;

  List<StockReturnTrackingModel> get pendingReturns => _pendingReturns;
  List<StockReturnTrackingModel> get completedReturns => _completedReturns;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get pendingCount => _pendingReturns.length;

  // 为了兼容性，添加别名
  List<StockReturnTrackingModel> get pendingTracking => _pendingReturns;

  // 为了兼容性，添加别名方法
  Future<void> loadPendingTracking(int staffId) async {
    await loadPendingReturns();
  }

  // 加载待归还库存列表
  Future<void> loadPendingReturns() async {
    if (_authProvider.currentStaff == null) {
      _error = 'User not logged in';
      notifyListeners();
      return;
    }

    // 只有普通员工可以查看待归还库存
    if (_authProvider.currentStaff!.level != 'regular') {
      _pendingReturns = [];
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _pendingReturns = await _checkService.getPendingStockReturnTracking(
        _authProvider.currentStaff!.id,
      );
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // 为了兼容性，添加别名方法
  Future<bool> confirmTracking(int trackingId, int staffId,
      {String? notes}) async {
    return await confirmStockReturn(trackingId, notes: notes);
  }

  // 确认库存归还完成
  Future<bool> confirmStockReturn(int trackingId, {String? notes}) async {
    if (_authProvider.currentStaff == null) {
      _error = 'User not logged in';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _checkService.confirmStockReturnTracking(
        trackingId,
        _authProvider.currentStaff!.id,
        notes: notes,
      );

      // 后端返回 StockReturnTrackingModel 对象，表示成功
      // 从待归还列表中移除已完成的项目
      _pendingReturns.removeWhere((item) => item.id == trackingId);

      // 重新加载数据以确保同步
      await loadPendingReturns();

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // 加载已完成的归还记录（可选功能）
  Future<void> loadCompletedReturns() async {
    if (_authProvider.currentStaff == null) {
      _error = 'User not logged in';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // 这里可以添加获取已完成记录的API调用
      // _completedReturns = await _checkService.getCompletedStockReturnTracking(
      //   _authProvider.currentStaff!.id,
      // );
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
    }
  }

  // 刷新所有数据
  Future<void> refreshAll() async {
    await loadPendingReturns();
  }

  // 清除错误
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // 根据文档编号过滤待归还项目
  List<StockReturnTrackingModel> filterByDocument(String documentNo) {
    return _pendingReturns
        .where((item) => item.documentNo.contains(documentNo))
        .toList();
  }

  // 根据货架位置过滤待归还项目
  List<StockReturnTrackingModel> filterByShelf(String shelfNo) {
    return _pendingReturns
        .where((item) => item.binShelfNo.contains(shelfNo))
        .toList();
  }

  // 获取按文档分组的待归还项目
  Map<String, List<StockReturnTrackingModel>> getGroupedByDocument() {
    final Map<String, List<StockReturnTrackingModel>> grouped = {};

    for (final item in _pendingReturns) {
      if (!grouped.containsKey(item.documentNo)) {
        grouped[item.documentNo] = [];
      }
      grouped[item.documentNo]!.add(item);
    }

    return grouped;
  }

  // 获取按货架分组的待归还项目
  Map<String, List<StockReturnTrackingModel>> getGroupedByShelf() {
    final Map<String, List<StockReturnTrackingModel>> grouped = {};

    for (final item in _pendingReturns) {
      final shelfPrefix = item.binShelfNo.split('-').first;
      if (!grouped.containsKey(shelfPrefix)) {
        grouped[shelfPrefix] = [];
      }
      grouped[shelfPrefix]!.add(item);
    }

    return grouped;
  }
}
