import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { Main } from '../postgres/entities/main.entity';
import { FirebaseService } from './firebase.service';
import { FirebaseSchedulerService } from './firebase-scheduler.service';
import { FirebaseTestController } from './firebase-test.controller';
import firebaseConfig from '../config/firebase.config';

@Module({
  imports: [
    ConfigModule.forFeature(firebaseConfig),
    TypeOrmModule.forFeature([Main], 'postgresConnection'),
    ScheduleModule.forRoot(), // 启用定时任务
  ],
  controllers: [FirebaseTestController],
  providers: [FirebaseService, FirebaseSchedulerService],
  exports: [FirebaseService, FirebaseSchedulerService],
})
export class FirebaseModule { }
