import '../models/check_item.dart';
import '../models/shipping_document.dart';

class SearchUtils {
  /// 搜索CheckItem列表
  static List<CheckItem> searchCheckItems(
    List<CheckItem> items,
    String query,
  ) {
    if (query.trim().isEmpty) {
      return items;
    }

    final searchQuery = query.toLowerCase().trim();

    return items.where((item) {
      return item.documentNo.toLowerCase().contains(searchQuery) ||
          item.customer.toLowerCase().contains(searchQuery) ||
          item.customerName.toLowerCase().contains(searchQuery) ||
          item.stock.toLowerCase().contains(searchQuery) ||
          item.description.toLowerCase().contains(searchQuery) ||
          item.binShelfNo.toLowerCase().contains(searchQuery);
    }).toList();
  }

  /// 搜索ShippingDocument列表
  static List<ShippingDocument> searchShippingDocuments(
    List<ShippingDocument> documents,
    String query,
  ) {
    if (query.trim().isEmpty) {
      return documents;
    }

    final searchQuery = query.toLowerCase().trim();

    return documents.where((doc) {
      return doc.documentNo.toLowerCase().contains(searchQuery) ||
          doc.customerCode.toLowerCase().contains(searchQuery) ||
          doc.customerName.toLowerCase().contains(searchQuery) ||
          (doc.transporterName?.toLowerCase().contains(searchQuery) ?? false) ||
          (doc.issueBy?.toLowerCase().contains(searchQuery) ?? false) ||
          (doc.salesmanCode?.toLowerCase().contains(searchQuery) ?? false) ||
          (doc.customerEmail?.toLowerCase().contains(searchQuery) ?? false) ||
          (doc.customerDeliverAddress?.toLowerCase().contains(searchQuery) ??
              false) ||
          (doc.driverUsername?.toLowerCase().contains(searchQuery) ?? false) ||
          doc.remarks.toLowerCase().contains(searchQuery);
    }).toList();
  }

  /// 通用搜索方法，支持自定义搜索字段
  static List<T> searchGeneric<T>(
    List<T> items,
    String query,
    List<String Function(T)> getSearchFields,
  ) {
    if (query.trim().isEmpty) {
      return items;
    }

    final searchQuery = query.toLowerCase().trim();

    return items.where((item) {
      return getSearchFields.any((getField) {
        final fieldValue = getField(item);
        return fieldValue.toLowerCase().contains(searchQuery);
      });
    }).toList();
  }
}
