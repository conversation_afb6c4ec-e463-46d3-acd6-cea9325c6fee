import 'package:flutter/foundation.dart';
import 'package:queue_system/models/notification_model.dart';
import 'package:queue_system/services/api_service.dart';

class NotificationService {
  final ApiService apiService;

  NotificationService({required this.apiService});

  Future<List<NotificationModel>> getUnreadNotifications(int userId) async {
    final response = await apiService.get('/notification/unread/$userId');

    try {
      if (response is List) {
        return response
            .map((item) => NotificationModel.fromJson(item))
            .toList();
      } else if (response is Map &&
          response.containsKey('data') &&
          response['data'] is List) {
        final List<dynamic> data = response['data'];
        return data.map((item) => NotificationModel.fromJson(item)).toList();
      } else {
        throw Exception('获取未读通知失败: 响应格式不正确');
      }
    } catch (e) {
      throw Exception('获取未读通知失败: $e');
    }
  }

  Future<List<NotificationModel>> getAllNotifications(int userId) async {
    final response = await apiService.get('/notification/all/$userId');

    try {
      if (response is List) {
        return response
            .map((item) => NotificationModel.fromJson(item))
            .toList();
      } else if (response is Map &&
          response.containsKey('data') &&
          response['data'] is List) {
        final List<dynamic> data = response['data'];
        return data.map((item) => NotificationModel.fromJson(item)).toList();
      } else {
        throw Exception('获取所有通知失败: 响应格式不正确');
      }
    } catch (e) {
      throw Exception('获取所有通知失败: $e');
    }
  }

  Future<NotificationModel> markAsRead(int notificationId) async {
    final response =
        await apiService.post('/notification/read/$notificationId');

    try {
      if (response is Map) {
        if (response.containsKey('detail')) {
          return NotificationModel.fromJson(
              response['detail'] as Map<String, dynamic>);
        } else {
          return NotificationModel.fromJson(response as Map<String, dynamic>);
        }
      } else {
        throw Exception('标记通知为已读失败: 响应格式不正确');
      }
    } catch (e) {
      throw Exception('标记通知为已读失败: $e');
    }
  }

  Future<void> markAllAsRead(int userId) async {
    try {
      final response = await apiService.post('/notification/read-all/$userId');

      // 记录响应，便于调试
      debugPrint('标记所有通知为已读响应: $response');

      // 即使响应为空也视为成功
      return;
    } catch (e) {
      debugPrint('标记所有通知为已读失败: $e');
      throw Exception('标记所有通知为已读失败: $e');
    }
  }
}
