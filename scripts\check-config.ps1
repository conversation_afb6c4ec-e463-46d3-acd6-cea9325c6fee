# Configuration Check Script - Verify current environment configuration loading

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("npm", "pm2-dev", "pm2-staging", "pm2-prod")]
    [string]$Mode = "npm"
)

Write-Host "=== Configuration Check Tool ===" -ForegroundColor Green
Write-Host "Check mode: $Mode" -ForegroundColor Cyan

# Use current directory as project root
$projectRoot = Get-Location
Write-Host "Using project root: $projectRoot" -ForegroundColor Yellow

switch ($Mode) {
    "npm" {
        Write-Host "`nCheck .env file configuration" -ForegroundColor Cyan
        if (Test-Path ".env") {
            Write-Host "OK .env file exists" -ForegroundColor Green
            Write-Host "`n.env file content:" -ForegroundColor Yellow
            Get-Content ".env" | Where-Object { $_ -notlike "#*" -and $_ -ne "" } | ForEach-Object {
                $parts = $_ -split "=", 2
                if ($parts.Length -eq 2) {
                    $key = $parts[0]
                    $value = if ($parts[1].Length -gt 20) { $parts[1].Substring(0, 20) + "..." } else { $parts[1] }
                    Write-Host "  $key = $value" -ForegroundColor Gray
                }
            }
            Write-Host "`nThese configurations will be loaded when using 'npm start'" -ForegroundColor Yellow
        } else {
            Write-Host "ERROR .env file does not exist" -ForegroundColor Red
        }
    }
    
    "pm2-dev" {
        Write-Host "`nCheck ecosystem.config.js configuration" -ForegroundColor Cyan
        if (Test-Path "ecosystem.config.js") {
            Write-Host "OK ecosystem.config.js file exists" -ForegroundColor Green
            Write-Host "`nThis configuration will be used when starting with 'npm run pm2:dev'" -ForegroundColor Yellow

            # Try to parse environment variables from config file
            $configContent = Get-Content "ecosystem.config.js" -Raw
            if ($configContent -match "env:\s*{([^}]+)}") {
                Write-Host "`nPM2 environment variable configuration:" -ForegroundColor Yellow
                $envBlock = $matches[1]
                $envBlock -split "," | ForEach-Object {
                    $line = $_.Trim()
                    if ($line -match "(\w+):\s*['\"]([^'\"]+)['\"]") {
                        $key = $matches[1]
                        $value = if ($matches[2].Length -gt 20) { $matches[2].Substring(0, 20) + "..." } else { $matches[2] }
                        Write-Host "  $key = $value" -ForegroundColor Gray
                    }
                }
            }
        } else {
            Write-Host "ERROR ecosystem.config.js file does not exist" -ForegroundColor Red
        }
    }
    
    "pm2-staging" {
        Write-Host "`nCheck ecosystem.staging.config.js configuration" -ForegroundColor Cyan
        if (Test-Path "ecosystem.staging.config.js") {
            Write-Host "OK ecosystem.staging.config.js file exists" -ForegroundColor Green
            Write-Host "`nThis configuration will be used when starting with 'npm run pm2:staging'" -ForegroundColor Yellow
        } else {
            Write-Host "ERROR ecosystem.staging.config.js file does not exist" -ForegroundColor Red
            Write-Host "Please create based on ecosystem.template.config.js" -ForegroundColor Yellow
        }
    }

    "pm2-prod" {
        Write-Host "`nCheck ecosystem.production.config.js configuration" -ForegroundColor Cyan
        if (Test-Path "ecosystem.production.config.js") {
            Write-Host "OK ecosystem.production.config.js file exists" -ForegroundColor Green
            Write-Host "`nThis configuration will be used when starting with 'npm run pm2:prod'" -ForegroundColor Yellow
        } else {
            Write-Host "ERROR ecosystem.production.config.js file does not exist" -ForegroundColor Red
            Write-Host "Please create based on ecosystem.template.config.js" -ForegroundColor Yellow
        }
    }
}

Write-Host "`nCurrently running processes" -ForegroundColor Cyan
try {
    $pm2Processes = pm2 jlist | ConvertFrom-Json
    if ($pm2Processes.Count -gt 0) {
        Write-Host "PM2 processes:" -ForegroundColor Green
        $pm2Processes | ForEach-Object {
            $status = if ($_.pm2_env.status -eq "online") { "OK" } else { "ERROR" }
            Write-Host "  $status $($_.name) - $($_.pm2_env.status)" -ForegroundColor Gray
        }
    } else {
        Write-Host "No running PM2 processes" -ForegroundColor Yellow
    }
} catch {
    Write-Host "PM2 not installed or not running" -ForegroundColor Yellow
}

Write-Host "`nConfiguration modification guide" -ForegroundColor Cyan
Write-Host "Choose the configuration file to modify based on your startup method:" -ForegroundColor Yellow
Write-Host "  npm start           -> modify .env" -ForegroundColor Gray
Write-Host "  npm run pm2:dev     -> modify ecosystem.config.js" -ForegroundColor Gray
Write-Host "  npm run pm2:staging -> modify ecosystem.staging.config.js" -ForegroundColor Gray
Write-Host "  npm run pm2:prod    -> modify ecosystem.production.config.js" -ForegroundColor Gray

Write-Host "`nIMPORTANT REMINDER" -ForegroundColor Red
Write-Host "PM2 does not automatically read .env files!" -ForegroundColor Red
Write-Host "Each environment's configuration file is independent!" -ForegroundColor Red

Write-Host "`n=== Check Complete ===" -ForegroundColor Green
