import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:queue_system/l10n/app_localizations.dart';
import 'package:queue_system/services/pdf_service.dart';
import 'package:queue_system/services/check_service.dart';
import 'package:queue_system/models/shipping_document.dart';
import 'package:queue_system/utils/app_config.dart';
import 'package:queue_system/providers/auth_provider.dart';
import 'package:queue_system/widgets/email_dialog.dart';
import 'package:queue_system/widgets/search_bar_widget.dart';
import 'package:queue_system/widgets/whatsapp_dialog.dart';
import 'package:queue_system/services/whatsapp_service.dart';
import 'package:queue_system/utils/search_utils.dart';

class PdfListScreen extends StatefulWidget {
  const PdfListScreen({super.key});

  @override
  State<PdfListScreen> createState() => _PdfListScreenState();
}

class _PdfListScreenState extends State<PdfListScreen> {
  final CheckService _checkService = CheckService(baseUrl: AppConfig.baseUrl);
  final PdfService _pdfService = PdfService();
  List<ShippingDocument> _documents = [];
  bool _isLoading = true;
  String? _error;

  // 搜索相关状态
  String _searchQuery = '';
  List<ShippingDocument> _filteredDocuments = [];
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  // 更新过滤后的文档列表
  void _updateFilteredDocuments() {
    setState(() {
      _filteredDocuments =
          SearchUtils.searchShippingDocuments(_documents, _searchQuery);
    });
  }

  // 处理搜索查询变化（带防抖功能）
  void _onSearchChanged(String query) {
    // 取消之前的定时器
    _searchDebounceTimer?.cancel();

    // 立即更新搜索查询状态（用于UI显示）
    setState(() {
      _searchQuery = query;
    });

    // 设置新的防抖定时器
    _searchDebounceTimer = Timer(const Duration(seconds: 1), () {
      // 1秒后执行搜索
      _updateFilteredDocuments();
    });
  }

  // 清除搜索
  void _onSearchCleared() {
    // 取消防抖定时器
    _searchDebounceTimer?.cancel();

    setState(() {
      _searchQuery = '';
      _updateFilteredDocuments();
    });
  }

  Future<void> _loadDocuments() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // 获取当前登录用户的ID
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentStaff = authProvider.currentStaff;

      if (currentStaff == null) {
        throw '用户未登录';
      }

      List<ShippingDocument> documents = [];

      // 根据用户角色获取不同的文档列表
      if (currentStaff.level == 'supervisor' ||
          currentStaff.level == 'admin' ||
          currentStaff.level == 'driver') {
        // 如果是主管、管理员或司机，获取所有出货文档
        final result = await _checkService
            .getSupervisorShipments(currentStaff.id, page: 1, limit: 1000);
        final allDocuments = result['data'] as List<ShippingDocument>;
        // 只显示已出货文档
        documents = allDocuments.where((doc) => doc.isShipped).toList();
      } else {
        // 其他角色不应该能访问此页面，但为了安全起见，返回空列表
        documents = [];
      }

      // 添加调试信息
      debugPrint("获取到的文档数量: ${documents.length}");

      setState(() {
        _documents = documents;
        _isLoading = false;
        _updateFilteredDocuments(); // 更新过滤后的文档列表
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // 打开PDF的方法
  void _openPdf(ShippingDocument document) {
    // 显示选择对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.t('view_pdf')),
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 使用本地PDF查看器查看
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.picture_as_pdf, color: Colors.blue),
              ),
              title: Text(
                context.t('view_native'),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.of(context).pop();
                _viewPdfNative(document);
              },
            ),
            const Divider(),
            // 下载PDF
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.download, color: Colors.orange),
              ),
              title: Text(
                context.t('download_pdf'),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.of(context).pop();
                _downloadPdf(document);
              },
            ),
            const Divider(),
            // 通过邮件发送PDF
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.email, color: Colors.green),
              ),
              title: Text(
                context.t('send_pdf'),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.of(context).pop();
                _sendPdfByEmail(document);
              },
            ),
            const Divider(),
            // 通过WhatsApp发送PDF
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.message, color: Colors.green[600]),
              ),
              title: Text(
                context.t('send_to_whatsapp'),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.of(context).pop();
                _sendPdfByWhatsApp(document);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(context.t('close')),
          ),
        ],
      ),
    );
  }

  // 通过邮件发送PDF
  Future<void> _sendPdfByEmail(ShippingDocument document) async {
    // 显示邮件输入对话框
    final email = await showDialog<String>(
      context: context,
      builder: (context) => EmailDialog(
        documentNo: document.documentNo,
        defaultEmail: document.customerEmail,
      ),
    );

    // 如果用户取消了操作，email将为null
    if (email == null) return;

    // 显示加载指示器
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(context.t('sending_email')),
          ],
        ),
      ),
    );

    try {
      // 发送邮件
      final result = await _pdfService.sendPdfEmail(
        document.documentNo,
        email,
        subject: 'Delivery Order: ${document.documentNo}',
        text:
            'Please find the attached delivery order PDF for document ${document.documentNo}.',
      );

      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示结果
      if (mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('email_sent_successfully')),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${context.t('email_sent_failed')}: ${result['message']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('email_sent_failed')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 通过WhatsApp发送PDF
  Future<void> _sendPdfByWhatsApp(ShippingDocument document) async {
    try {
      // 显示加载指示器
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(context.t('getting_pdf_download_link')),
            ],
          ),
        ),
      );

      // 获取Firebase下载链接
      final firebaseResult =
          await _pdfService.getFirebaseDownloadUrl(document.documentNo);

      // 关闭加载指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (!firebaseResult['success'] || firebaseResult['downloadUrl'] == null) {
        // 如果没有Firebase下载链接，显示错误
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('pdf_not_uploaded_to_cloud')),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // 显示WhatsApp对话框
      if (!mounted) return;
      final result = await showDialog<Map<String, String>>(
        context: context,
        builder: (context) => WhatsAppDialog(
          documentNo: document.documentNo,
          defaultPhoneNumber: document.customerTelephone,
          pdfDownloadUrl: firebaseResult['downloadUrl'],
        ),
      );

      // 如果用户取消了操作，result将为null
      if (result == null) return;

      // 发送WhatsApp消息
      final success = await WhatsAppService.sendMessage(
        phoneNumber: result['phoneNumber']!,
        message: result['message']!,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('whatsapp_opened_complete_sending')),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // 显示WhatsApp未安装的对话框
          WhatsAppService.showWhatsAppNotInstalledDialog(context);
        }
      }
    } catch (e) {
      // 关闭可能存在的加载指示器
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('whatsapp_share_failed')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 使用本地PDF查看器查看PDF
  void _viewPdfNative(ShippingDocument document) {
    try {
      _pdfService.viewPdfNative(context, document.documentNo);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('error_open_pdf')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 下载PDF文件
  Future<void> _downloadPdf(ShippingDocument document) async {
    try {
      // 显示下载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.t('downloading_pdf')),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // 调用下载服务
      final success = await _pdfService.downloadPdf(document.documentNo);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('pdf_download_success')),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.t('pdf_download_failed')),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${context.t('pdf_download_failed')}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('pdf_history')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDocuments,
            tooltip: context.t('refresh'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(context.t('loading_failed')),
                      const SizedBox(height: 8),
                      Text(_error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadDocuments,
                        child: Text(context.t('retry')),
                      ),
                    ],
                  ),
                )
              : _documents.isEmpty
                  ? Center(
                      child: Text(context.t('no_pdf_documents')),
                    )
                  : Column(
                      children: [
                        // 搜索栏
                        SearchBarWidget(
                          hintText: context.t('search_documents'),
                          onSearchChanged: _onSearchChanged,
                          onClear: _onSearchCleared,
                          initialValue: _searchQuery,
                        ),
                        // 显示搜索结果统计
                        if (_searchQuery.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Row(
                              children: [
                                Icon(Icons.info_outline,
                                    size: 16, color: Colors.grey.shade600),
                                const SizedBox(width: 8),
                                Text(
                                  '${context.t('search_results')}: ${_getDisplayDocuments().length} / ${_documents.length}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        // 文档列表
                        Expanded(child: _buildDocumentsList()),
                      ],
                    ),
    );
  }

  // 获取要显示的文档列表
  List<ShippingDocument> _getDisplayDocuments() {
    return _filteredDocuments.isNotEmpty || _searchQuery.isNotEmpty
        ? _filteredDocuments
        : _documents;
  }

  Widget _buildDocumentsList() {
    final documentsToShow = _getDisplayDocuments();

    // 如果搜索后没有结果，显示无结果提示
    if (_searchQuery.isNotEmpty && _filteredDocuments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              context.t('no_search_results'),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              '${context.t('search_keyword')}: "$_searchQuery"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: documentsToShow.length,
      itemBuilder: (context, index) {
        final document = documentsToShow[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(document.documentNo),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    '${context.t('customer')}: ${document.customerCode} - ${document.customerName}'),
                Text(
                    '${context.t('document_date')}: ${_formatDate(document.documentDate)}'),
                if (document.shippedAt != null)
                  Text(
                      '${context.t('shipped_at')}: ${_formatDateTime(document.shippedAt!)}'),
              ],
            ),
            trailing: ElevatedButton.icon(
              icon: const Icon(Icons.picture_as_pdf),
              label: Text(context.t('view_pdf')),
              onPressed: () => _openPdf(document),
            ),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
