---
title: "API 服务实现"
description: "队列系统 Flutter 移动应用的网络请求和 API 服务实现"
version: "1.0.0"
last_updated: "2025-05-19"
---

# API 服务实现

## 概述

队列系统移动应用使用 Dio 包进行网络请求，并采用服务层模式封装 API 调用。这种方式将网络请求与业务逻辑分离，使代码更易于维护和测试。

## 网络层架构

### 1. API 客户端 (ApiClient)

`ApiClient` 是一个基础类，负责配置和管理 Dio 实例，处理通用的请求/响应拦截器、错误处理和认证逻辑。

```dart
class ApiClient {
  final Dio _dio = Dio();
  final String baseUrl = 'http://localhost:3000';
  
  ApiClient() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // 添加认证令牌
        final token = AuthService.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
      onResponse: (response, handler) {
        // 处理响应
        return handler.next(response);
      },
      onError: (DioError e, handler) {
        // 处理错误
        return handler.next(e);
      }
    ));
  }
  
  // 获取 Dio 实例
  Dio get dio => _dio;
}
```

### 2. 服务类 (Services)

服务类基于 `ApiClient` 构建，每个服务类负责特定领域的 API 调用。

## 主要服务类

### 1. AuthService

负责用户认证相关的 API 调用：

```dart
class AuthService {
  final ApiClient _apiClient;
  
  AuthService(this._apiClient);
  
  // 用户登录
  Future<Staff> login(String username, String password) async {
    try {
      final response = await _apiClient.dio.post('/staff/login', data: {
        'username': username,
        'password': password
      });
      
      if (response.statusCode == 200 && response.data['success']) {
        return Staff.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? '登录失败');
      }
    } catch (e) {
      throw Exception('登录失败: ${e.toString()}');
    }
  }
  
  // 获取当前用户信息
  Future<Staff> getCurrentUser(int id) async {
    try {
      final response = await _apiClient.dio.get('/staff/$id');
      
      if (response.statusCode == 200) {
        return Staff.fromJson(response.data);
      } else {
        throw Exception('获取用户信息失败');
      }
    } catch (e) {
      throw Exception('获取用户信息失败: ${e.toString()}');
    }
  }
}
```

### 2. CheckService

负责货物检查相关的 API 调用：

```dart
class CheckService {
  final ApiClient _apiClient;
  
  CheckService(this._apiClient);
  
  // 获取待检查列表
  Future<Map<String, List<DeliveryOrderDetail>>> getCheckList(int staffId) async {
    try {
      final response = await _apiClient.dio.get('/check/list', queryParameters: {
        'staffId': staffId
      });
      
      if (response.statusCode == 200) {
        final Map<String, List<DeliveryOrderDetail>> result = {
          'pendingStaffCheck': [],
          'pendingSupervisorCheck': []
        };
        
        if (response.data['pendingStaffCheck'] != null) {
          result['pendingStaffCheck'] = (response.data['pendingStaffCheck'] as List)
              .map((item) => DeliveryOrderDetail.fromJson(item))
              .toList();
        }
        
        if (response.data['pendingSupervisorCheck'] != null) {
          result['pendingSupervisorCheck'] = (response.data['pendingSupervisorCheck'] as List)
              .map((item) => DeliveryOrderDetail.fromJson(item))
              .toList();
        }
        
        return result;
      } else {
        throw Exception('获取待检查列表失败');
      }
    } catch (e) {
      throw Exception('获取待检查列表失败: ${e.toString()}');
    }
  }
  
  // 员工检查货物
  Future<DeliveryOrderDetail> staffCheck(String documentNo, int line, int staffId) async {
    try {
      final response = await _apiClient.dio.post('/check/staff', queryParameters: {
        'documentNo': documentNo,
        'line': line,
        'staffId': staffId
      });
      
      if (response.statusCode == 200 && response.data['success']) {
        return DeliveryOrderDetail.fromJson(response.data['detail']);
      } else {
        throw Exception(response.data['message'] ?? '检查失败');
      }
    } catch (e) {
      throw Exception('检查失败: ${e.toString()}');
    }
  }
  
  // BOM专员检查货物
  Future<DeliveryOrderDetail> bomSpecialistCheck(String documentNo, int line, int bomSpecialistId) async {
    try {
      final response = await _apiClient.dio.post('/check/bom', queryParameters: {
        'documentNo': documentNo,
        'line': line,
        'bomSpecialistId': bomSpecialistId
      });
      
      if (response.statusCode == 200 && response.data['success']) {
        return DeliveryOrderDetail.fromJson(response.data['detail']);
      } else {
        throw Exception(response.data['message'] ?? 'BOM检查失败');
      }
    } catch (e) {
      throw Exception('BOM检查失败: ${e.toString()}');
    }
  }
}
```

### 3. ShippingService

负责出货和送货相关的 API 调用：

```dart
class ShippingService {
  final ApiClient _apiClient;
  
  ShippingService(this._apiClient);
  
  // 获取可出货订单列表
  Future<List<DeliveryOrderMain>> getReadyDocuments(int staffId) async {
    try {
      final response = await _apiClient.dio.get('/check/ready-documents', queryParameters: {
        'staffId': staffId
      });
      
      if (response.statusCode == 200) {
        return (response.data as List)
            .map((item) => DeliveryOrderMain.fromJson(item))
            .toList();
      } else {
        throw Exception('获取可出货订单列表失败');
      }
    } catch (e) {
      throw Exception('获取可出货订单列表失败: ${e.toString()}');
    }
  }
  
  // 标记订单为已出货
  Future<DeliveryOrderMain> completeDocument(String documentNo, int supervisorId, int? driverId) async {
    try {
      final Map<String, dynamic> params = {
        'documentNo': documentNo,
        'supervisorId': supervisorId,
      };
      
      if (driverId != null) {
        params['driverId'] = driverId;
      }
      
      final response = await _apiClient.dio.post('/check/complete', queryParameters: params);
      
      if (response.statusCode == 200 && response.data['success']) {
        return DeliveryOrderMain.fromJson(response.data['document']);
      } else {
        throw Exception(response.data['message'] ?? '标记订单为已出货失败');
      }
    } catch (e) {
      throw Exception('标记订单为已出货失败: ${e.toString()}');
    }
  }
}
```

## 错误处理

服务层实现了统一的错误处理机制，将网络错误转换为应用内的异常：

```dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  
  ApiException(this.message, {this.statusCode});
  
  @override
  String toString() => 'ApiException: $message (Status Code: $statusCode)';
}

// 在 ApiClient 中使用
void handleError(DioError e) {
  if (e.response != null) {
    // 服务器返回错误
    final statusCode = e.response?.statusCode;
    final message = e.response?.data['message'] ?? '未知错误';
    throw ApiException(message, statusCode: statusCode);
  } else {
    // 网络错误或其他错误
    throw ApiException(e.message);
  }
}
```

## 依赖注入

使用依赖注入模式管理服务实例：

```dart
class ServiceLocator {
  static final ApiClient _apiClient = ApiClient();
  
  static AuthService get authService => AuthService(_apiClient);
  static CheckService get checkService => CheckService(_apiClient);
  static ShippingService get shippingService => ShippingService(_apiClient);
  static NotificationService get notificationService => NotificationService(_apiClient);
  static PdfService get pdfService => PdfService(_apiClient);
}
```

## 与状态管理的集成

服务层与状态管理层（Provider）集成：

```dart
class CheckProvider with ChangeNotifier {
  final CheckService _checkService = ServiceLocator.checkService;
  
  // 状态
  List<DeliveryOrderDetail> _checkList = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  List<DeliveryOrderDetail> get checkList => _checkList;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // 获取待检查列表
  Future<void> fetchCheckList(int staffId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final result = await _checkService.getCheckList(staffId);
      _checkList = [...result['pendingStaffCheck'] ?? []];
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
}
```
