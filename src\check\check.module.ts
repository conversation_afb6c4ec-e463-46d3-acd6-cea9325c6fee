import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CheckController } from './check.controller';
import { CheckService } from './check.service';
import { Detail } from '../postgres/entities/detail.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { Main } from '../postgres/entities/main.entity';
import { NotificationModule } from '../notification/notification.module';
import { PdfModule } from '../pdf/pdf.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Detail, Staff, Main], 'postgresConnection'),
    NotificationModule,
    PdfModule
  ],
  controllers: [CheckController],
  providers: [CheckService],
  exports: [CheckService]
})
export class CheckModule { }