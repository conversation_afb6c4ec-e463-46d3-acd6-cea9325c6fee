const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testUrgentAPI() {
  console.log('🧪 测试紧急排号 API 功能...\n');

  try {
    // 1. 测试 API 是否可访问
    console.log('1. 检查 API 服务状态...');
    try {
      const healthCheck = await axios.get(`${BASE_URL}/api-docs`);
      console.log('✅ API 服务正常运行');
    } catch (error) {
      console.log('❌ API 服务不可访问，请确保后端服务已启动');
      console.log('   启动命令: npm run start:dev 或 pm2 start ecosystem.config.js');
      return;
    }

    // 2. 测试登录一个 senior 用户
    console.log('\n2. 测试用户登录...');
    let supervisorId;
    try {
      // 尝试登录现有的 senior 用户
      const loginResponse = await axios.post(`${BASE_URL}/staff/login`, {
        username: 'supervisor',
        password: 'password123'
      });
      supervisorId = loginResponse.data.id;
      console.log(`✅ 登录成功，用户ID: ${supervisorId}, 级别: ${loginResponse.data.level}`);
    } catch (error) {
      console.log('ℹ️  默认 supervisor 用户不存在，尝试创建...');
      try {
        // 创建一个 senior 用户
        const createResponse = await axios.post(`${BASE_URL}/staff`, {
          username: 'supervisor',
          password: 'password123',
          level: 'senior',
          full_name: 'Test Supervisor'
        });
        console.log('✅ 创建 senior 用户成功');
        
        // 重新登录
        const loginResponse = await axios.post(`${BASE_URL}/staff/login`, {
          username: 'supervisor',
          password: 'password123'
        });
        supervisorId = loginResponse.data.id;
        console.log(`✅ 登录成功，用户ID: ${supervisorId}`);
      } catch (createError) {
        console.log('❌ 无法创建或登录 senior 用户:', createError.response?.data?.message || createError.message);
        return;
      }
    }

    // 3. 获取待检查列表
    console.log('\n3. 获取待检查列表...');
    const checkListResponse = await axios.get(`${BASE_URL}/check/list?staffId=${supervisorId}`);
    const waitingList = checkListResponse.data.waitingList || [];
    
    if (waitingList.length === 0) {
      console.log('⚠️  没有找到待处理的文档');
      console.log('   请确保数据库中有测试数据，或运行数据同步');
      return;
    }

    const testDocument = waitingList[0];
    const documentNo = testDocument.document_no.split('/')[0];
    console.log(`✅ 找到测试文档: ${documentNo}`);
    console.log(`   当前优先级: ${testDocument.priority || 0}`);

    // 4. 测试设置紧急状态 API
    console.log('\n4. 测试设置紧急状态 API...');
    try {
      const setUrgentResponse = await axios.post(`${BASE_URL}/check/set-urgent`, null, {
        params: {
          documentNo: documentNo,
          supervisorId: supervisorId,
          priority: 1
        }
      });
      console.log('✅ 设置紧急状态成功');
      console.log(`   响应: ${setUrgentResponse.data.message}`);
      console.log(`   文档优先级: ${setUrgentResponse.data.document.priority}`);
    } catch (error) {
      console.log('❌ 设置紧急状态失败:', error.response?.data?.message || error.message);
      return;
    }

    // 5. 验证排序效果
    console.log('\n5. 验证排序效果...');
    const updatedListResponse = await axios.get(`${BASE_URL}/check/list?staffId=${supervisorId}`);
    const updatedWaitingList = updatedListResponse.data.waitingList || [];
    
    const urgentDoc = updatedWaitingList.find(item => 
      item.document_no.startsWith(documentNo)
    );
    
    if (urgentDoc && urgentDoc.priority > 0) {
      console.log('✅ 紧急文档优先级已更新');
      console.log(`   文档: ${urgentDoc.document_no}`);
      console.log(`   优先级: ${urgentDoc.priority}`);
      
      // 检查排序
      const urgentDocIndex = updatedWaitingList.findIndex(item => 
        item.document_no.startsWith(documentNo)
      );
      console.log(`   在列表中的位置: ${urgentDocIndex + 1}/${updatedWaitingList.length}`);
    } else {
      console.log('❌ 未找到紧急文档或优先级未更新');
    }

    // 6. 测试取消紧急状态
    console.log('\n6. 测试取消紧急状态...');
    try {
      const cancelUrgentResponse = await axios.post(`${BASE_URL}/check/set-urgent`, null, {
        params: {
          documentNo: documentNo,
          supervisorId: supervisorId,
          priority: 0
        }
      });
      console.log('✅ 取消紧急状态成功');
      console.log(`   响应: ${cancelUrgentResponse.data.message}`);
      console.log(`   文档优先级: ${cancelUrgentResponse.data.document.priority}`);
    } catch (error) {
      console.log('❌ 取消紧急状态失败:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 紧急排号 API 测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   ✅ API 服务可访问');
    console.log('   ✅ 用户认证正常');
    console.log('   ✅ 设置紧急状态功能正常');
    console.log('   ✅ 取消紧急状态功能正常');
    console.log('   ✅ 数据库排序逻辑正常');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.response?.data || error.message);
  }
}

// 运行测试
testUrgentAPI();
