const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function diagnoseUrgentFeature() {
  console.log('🔍 诊断紧急排号功能问题...\n');

  try {
    // 1. 检查 API 服务状态
    console.log('1. 检查 API 服务状态...');
    try {
      await axios.get(`${BASE_URL}/api-docs`);
      console.log('✅ API 服务正常运行');
    } catch (error) {
      console.log('❌ API 服务不可访问');
      return;
    }

    // 2. 登录 senior 用户
    console.log('\n2. 登录 senior 用户...');
    let supervisorId;
    try {
      const loginResponse = await axios.post(`${BASE_URL}/staff/login`, {
        username: 'supervisor',
        password: 'password123'
      });
      supervisorId = loginResponse.data.id;
      console.log(`✅ 登录成功，用户ID: ${supervisorId}, 级别: ${loginResponse.data.level}`);
    } catch (error) {
      console.log('❌ 登录失败:', error.response?.data?.message || error.message);
      return;
    }

    // 3. 检查数据库中的订单数据
    console.log('\n3. 检查数据库中的订单数据...');
    try {
      const checkListResponse = await axios.get(`${BASE_URL}/check/list?staffId=${supervisorId}`);
      const data = checkListResponse.data;

      console.log('📊 数据统计:');
      console.log(`   waitingList: ${data.waitingList?.length || 0} 条`);
      console.log(`   pendingStaffCheck: ${data.pendingStaffCheck?.length || 0} 条`);
      console.log(`   pendingBomCheck: ${data.pendingBomCheck?.length || 0} 条`);
      console.log(`   pendingSupervisorCheck: ${data.pendingSupervisorCheck?.length || 0} 条`);
      console.log(`   total: ${data.total || 0} 条`);

      // 查找可用的测试文档
      let testDocument = null;
      let testSource = '';

      if (data.waitingList && data.waitingList.length > 0) {
        console.log('\n📋 waitingList 前3条记录:');
        data.waitingList.slice(0, 3).forEach((item, index) => {
          console.log(`   ${index + 1}. ${item.document_no} - Priority: ${item.priority || 0} - Customer: ${item.customer}`);
        });
        testDocument = data.waitingList[0];
        testSource = 'waitingList';
      } else if (data.pendingSupervisorCheck && data.pendingSupervisorCheck.length > 0) {
        console.log('\n📋 pendingSupervisorCheck 前3条记录:');
        data.pendingSupervisorCheck.slice(0, 3).forEach((item, index) => {
          console.log(`   ${index + 1}. ${item.document_no} - Priority: ${item.priority || 0} - Customer: ${item.customer}`);
        });
        testDocument = data.pendingSupervisorCheck[0];
        testSource = 'pendingSupervisorCheck';
      }

      if (testDocument) {
        // 4. 测试设置紧急状态
        const documentNo = testDocument.document_no.split('/')[0];
        console.log(`\n4. 测试设置紧急状态 - 文档: ${documentNo} (来源: ${testSource})`);
        console.log(`   当前优先级: ${testDocument.priority || 0}`);

        try {
          const setUrgentResponse = await axios.post(`${BASE_URL}/check/set-urgent`, null, {
            params: {
              documentNo: documentNo,
              supervisorId: supervisorId,
              priority: 1
            }
          });
          console.log('✅ 设置紧急状态成功');
          console.log(`   响应: ${setUrgentResponse.data.message}`);
          console.log(`   文档优先级: ${setUrgentResponse.data.document.priority}`);

          // 5. 验证排序效果
          console.log('\n5. 验证排序效果...');
          const updatedListResponse = await axios.get(`${BASE_URL}/check/list?staffId=${supervisorId}`);
          const updatedData = updatedListResponse.data;

          console.log('📊 更新后的数据:');
          console.log(`   waitingList: ${updatedData.waitingList?.length || 0} 条`);

          if (updatedData.waitingList && updatedData.waitingList.length > 0) {
            console.log('\n📋 更新后的 waitingList 前5条记录:');
            updatedData.waitingList.slice(0, 5).forEach((item, index) => {
              const isUrgent = item.priority > 0 ? '🔴' : '⚪';
              console.log(`   ${index + 1}. ${isUrgent} ${item.document_no} - Priority: ${item.priority || 0} - Customer: ${item.customer}`);
            });

            // 检查紧急文档是否排在前面
            const urgentDoc = updatedData.waitingList.find(item =>
              item.document_no.startsWith(documentNo)
            );

            if (urgentDoc && urgentDoc.priority > 0) {
              const urgentDocIndex = updatedData.waitingList.findIndex(item =>
                item.document_no.startsWith(documentNo)
              );
              console.log(`\n✅ 紧急文档排序验证:`);
              console.log(`   文档: ${urgentDoc.document_no}`);
              console.log(`   优先级: ${urgentDoc.priority}`);
              console.log(`   在列表中的位置: ${urgentDocIndex + 1}/${updatedData.waitingList.length}`);

              if (urgentDocIndex === 0) {
                console.log('🎉 紧急文档正确排在第一位！');
              } else {
                console.log('⚠️  紧急文档没有排在第一位，可能有其他更高优先级的文档');
              }
            } else {
              console.log('❌ 未找到紧急文档或优先级未更新');
            }
          }

          // 6. 测试取消紧急状态
          console.log('\n6. 测试取消紧急状态...');
          try {
            const cancelResponse = await axios.post(`${BASE_URL}/check/set-urgent`, null, {
              params: {
                documentNo: documentNo,
                supervisorId: supervisorId,
                priority: 0
              }
            });
            console.log('✅ 取消紧急状态成功');
            console.log(`   文档优先级: ${cancelResponse.data.document.priority}`);
          } catch (error) {
            console.log('❌ 取消紧急状态失败:', error.response?.data?.message || error.message);
          }

        } catch (error) {
          console.log('❌ 设置紧急状态失败:', error.response?.data?.message || error.message);
          console.log('   状态码:', error.response?.status);
          console.log('   完整错误:', error.response?.data);
        }

      } else {
        console.log('⚠️  没有找到待处理的文档');
        console.log('   建议: 运行数据同步或添加测试数据');
      }

    } catch (error) {
      console.log('❌ 获取检查列表失败:', error.response?.data?.message || error.message);
    }

    console.log('\n🎯 诊断完成！');

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error.message);
  }
}

// 运行诊断
diagnoseUrgentFeature();
