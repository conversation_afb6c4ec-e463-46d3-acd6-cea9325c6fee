import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StockReturnTracking } from '../postgres/entities/stock-return-tracking.entity';
import { Staff } from '../postgres/entities/staff.entity';
import { Detail } from '../postgres/entities/detail.entity';

@Injectable()
export class StockReturnTrackingService {
  private readonly logger = new Logger(StockReturnTrackingService.name);

  constructor(
    @InjectRepository(StockReturnTracking, 'postgresConnection')
    private readonly trackingRepository: Repository<StockReturnTracking>,
    @InjectRepository(Staff, 'postgresConnection')
    private readonly staffRepository: Repository<Staff>,
    @InjectRepository(Detail, 'postgresConnection')
    private readonly detailRepository: Repository<Detail>
  ) {}

  // 创建库存归还追踪记录
  async createStockReturnTracking(
    checkedDetails: Detail[],
    triggeredBy: number,
    triggerReason: string
  ): Promise<StockReturnTracking[]> {
    try {
      const trackingRecords: StockReturnTracking[] = [];

      for (const detail of checkedDetails) {
        if (detail.bin_shelf_no && detail.bin_shelf_no.trim() !== '') {
          // 提取楼层号
          const floorNumber = detail.bin_shelf_no.split('-')[0];
          
          // 查找负责该楼层的员工
          const floorStaff = await this.staffRepository.find({
            where: {
              floor: `${floorNumber}F`,
              level: 'regular'
            }
          });

          // 为每个楼层员工创建追踪记录
          for (const staff of floorStaff) {
            const tracking = this.trackingRepository.create({
              document_no: detail.document_no.split('/')[0],
              line: detail.line,
              stock_code: detail.stock,
              bin_shelf_no: detail.bin_shelf_no,
              quantity: detail.quantity,
              trigger_reason: triggerReason,
              triggered_by: triggeredBy,
              assigned_to: staff.id
            });

            const savedTracking = await this.trackingRepository.save(tracking);
            trackingRecords.push(savedTracking);
          }
        }
      }

      this.logger.log(`Created ${trackingRecords.length} stock return tracking records`);
      return trackingRecords;

    } catch (error) {
      this.logger.error(`Error creating stock return tracking: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 获取员工的待归还库存列表
  async getPendingReturnsByStaff(staffId: number): Promise<StockReturnTracking[]> {
    try {
      // 验证员工存在
      const staff = await this.staffRepository.findOne({ where: { id: staffId } });
      if (!staff) {
        throw new NotFoundException('Staff not found');
      }

      // 只有普通员工可以查看待归还库存
      if (staff.level !== 'regular') {
        return [];
      }

      return await this.trackingRepository.find({
        where: {
          assigned_to: staffId,
          is_completed: false
        },
        relations: ['trigger_staff', 'assigned_staff'],
        order: {
          created_at: 'DESC'
        }
      });

    } catch (error) {
      this.logger.error(`Error getting pending returns for staff ${staffId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 确认库存归还完成
  async confirmStockReturn(
    trackingId: number,
    staffId: number,
    notes?: string
  ): Promise<StockReturnTracking> {
    try {
      // 查找追踪记录
      const tracking = await this.trackingRepository.findOne({
        where: { id: trackingId },
        relations: ['assigned_staff']
      });

      if (!tracking) {
        throw new NotFoundException('Stock return tracking record not found');
      }

      // 验证员工权限
      const staff = await this.staffRepository.findOne({ where: { id: staffId } });
      if (!staff) {
        throw new NotFoundException('Staff not found');
      }

      // 验证是否为分配的员工或同楼层员工
      if (tracking.assigned_to !== staffId) {
        // 检查是否为同楼层员工
        const trackingFloor = tracking.bin_shelf_no.split('-')[0];
        const staffFloor = staff.floor.replace('F', '');
        
        if (trackingFloor !== staffFloor) {
          throw new ForbiddenException('You can only confirm returns for your assigned floor');
        }
      }

      // 检查是否已完成
      if (tracking.is_completed) {
        throw new ForbiddenException('This stock return has already been completed');
      }

      // 更新完成状态
      tracking.is_completed = true;
      tracking.completed_by = staffId;
      tracking.completed_at = new Date();
      tracking.notes = notes || null;

      const updatedTracking = await this.trackingRepository.save(tracking);

      this.logger.log(`Stock return confirmed: tracking ID ${trackingId} by staff ${staffId}`);
      return updatedTracking;

    } catch (error) {
      this.logger.error(`Error confirming stock return: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 获取库存归还统计信息
  async getReturnStatistics(staffId?: number): Promise<any> {
    try {
      const whereCondition = staffId ? { assigned_to: staffId } : {};

      const [total, completed, pending] = await Promise.all([
        this.trackingRepository.count({ where: whereCondition }),
        this.trackingRepository.count({ 
          where: { ...whereCondition, is_completed: true } 
        }),
        this.trackingRepository.count({ 
          where: { ...whereCondition, is_completed: false } 
        })
      ]);

      return {
        total,
        completed,
        pending,
        completion_rate: total > 0 ? Math.round((completed / total) * 100) : 0
      };

    } catch (error) {
      this.logger.error(`Error getting return statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 获取所有追踪记录（管理员用）
  async getAllTrackingRecords(page: number = 1, limit: number = 50): Promise<any> {
    try {
      const [records, total] = await this.trackingRepository.findAndCount({
        relations: ['trigger_staff', 'assigned_staff', 'completed_staff'],
        order: {
          created_at: 'DESC'
        },
        skip: (page - 1) * limit,
        take: limit
      });

      return {
        records,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };

    } catch (error) {
      this.logger.error(`Error getting all tracking records: ${error.message}`, error.stack);
      throw error;
    }
  }
}
